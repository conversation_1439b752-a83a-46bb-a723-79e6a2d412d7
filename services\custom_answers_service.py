"""
Custom Answers Service Module

This module provides functionality for loading, matching, and refreshing custom answers
from a Google Sheet. It uses embedding-based matching to find the most semantically
similar custom answers to user questions.
"""

import os
import json
import time
import logging
import numpy as np
from typing import List, Dict, Optional, Tuple
from dotenv import load_dotenv
from openai import OpenAI
import googleapiclient.discovery
import googleapiclient.errors
from google.oauth2 import service_account

# Import shared models
from database.models import CustomAnswer
from utils.utils import is_arabic, translate_text
from services.google_sheets_service import get_sheets_service

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("custom_answers_service")

# Load environment variables
load_dotenv()

# Get configuration from environment variables
GOOGLE_SHEETS_ID = os.environ.get("GOOGLE_SHEETS_ID")
CUSTOM_ANSWERS_REFRESH_URL = os.environ.get("CUSTOM_ANSWERS_REFRESH_URL", "")
PINECONE_REFRESH_URL = os.environ.get("PINECONE_REFRESH_URL", "")

# Define the column headers for the custom answers sheet
CUSTOM_ANSWERS_HEADERS = [
    "Question Pattern",
    "Answer",
    "Refresh Custom Answers Link",
    "Refresh Pinecone Index Link",
]

# In-memory cache for custom answers
custom_answers_cache: List[CustomAnswer] = []

# OpenAI client for embeddings
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))


def initialize_custom_answers_sheet() -> bool:
    """
    Initialize the custom answers sheet with headers if it doesn't exist.

    Returns:
        True if successful, False otherwise
    """
    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return False

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return False

    try:
        # Check if the sheet exists
        try:
            result = (
                service.spreadsheets()
                .values()
                .get(spreadsheetId=GOOGLE_SHEETS_ID, range="CustomAnswers!A1:D1")
                .execute()
            )

            values = result.get("values", [])

            # If the sheet exists but doesn't have headers, add them
            if not values or len(values[0]) < len(CUSTOM_ANSWERS_HEADERS):
                service.spreadsheets().values().update(
                    spreadsheetId=GOOGLE_SHEETS_ID,
                    range="CustomAnswers!A1",
                    valueInputOption="RAW",
                    body={"values": [CUSTOM_ANSWERS_HEADERS]},
                ).execute()
                logger.info("Initialized CustomAnswers sheet with headers")

                # Pre-fill the refresh link columns with the environment variables only in the first data row (row 2)
                refresh_links_row = [""] * len(
                    CUSTOM_ANSWERS_HEADERS
                )  # Create a blank row of the correct length
                if CUSTOM_ANSWERS_REFRESH_URL:
                    refresh_links_row[2] = CUSTOM_ANSWERS_REFRESH_URL
                if PINECONE_REFRESH_URL:
                    refresh_links_row[3] = PINECONE_REFRESH_URL

                # Only add the row if at least one URL is present
                if CUSTOM_ANSWERS_REFRESH_URL or PINECONE_REFRESH_URL:
                    service.spreadsheets().values().update(
                        spreadsheetId=GOOGLE_SHEETS_ID,
                        range="CustomAnswers!A2",  # Start from A2 for the first data row
                        valueInputOption="RAW",
                        body={"values": [refresh_links_row]},
                    ).execute()
                    logger.info(
                        f"Pre-filled refresh links: Custom Answers='{CUSTOM_ANSWERS_REFRESH_URL}', Pinecone='{PINECONE_REFRESH_URL}'"
                    )
            else:
                logger.info("CustomAnswers sheet already has headers")

            return True
        except googleapiclient.errors.HttpError as e:
            # If the sheet doesn't exist, create it
            if e.resp.status == 404 or "Unable to parse range" in str(e):
                # Create the sheet
                body = {
                    "requests": [
                        {"addSheet": {"properties": {"title": "CustomAnswers"}}}
                    ]
                }
                service.spreadsheets().batchUpdate(
                    spreadsheetId=GOOGLE_SHEETS_ID, body=body
                ).execute()
                logger.info("Created CustomAnswers sheet")

                # Add headers
                service.spreadsheets().values().update(
                    spreadsheetId=GOOGLE_SHEETS_ID,
                    range="CustomAnswers!A1",
                    valueInputOption="RAW",
                    body={"values": [CUSTOM_ANSWERS_HEADERS]},
                ).execute()
                logger.info("Added headers to CustomAnswers sheet")

                # Pre-fill the refresh link columns with the environment variables only in the first data row (row 2)
                refresh_links_row = [""] * len(
                    CUSTOM_ANSWERS_HEADERS
                )  # Create a blank row of the correct length
                if CUSTOM_ANSWERS_REFRESH_URL:
                    refresh_links_row[2] = CUSTOM_ANSWERS_REFRESH_URL
                if PINECONE_REFRESH_URL:
                    refresh_links_row[3] = PINECONE_REFRESH_URL

                # Only add the row if at least one URL is present
                if CUSTOM_ANSWERS_REFRESH_URL or PINECONE_REFRESH_URL:
                    service.spreadsheets().values().update(
                        spreadsheetId=GOOGLE_SHEETS_ID,
                        range="CustomAnswers!A2",  # Start from A2 for the first data row
                        valueInputOption="RAW",
                        body={"values": [refresh_links_row]},
                    ).execute()
                    logger.info(
                        f"Pre-filled refresh links: Custom Answers='{CUSTOM_ANSWERS_REFRESH_URL}', Pinecone='{PINECONE_REFRESH_URL}'"
                    )

                return True
            else:
                raise
    except Exception as e:
        logger.error(f"Error initializing CustomAnswers sheet: {str(e)}")
        return False


def create_embedding(
    text: str, max_retries: int = 3, retry_delay: int = 2
) -> Optional[List[float]]:
    """
    Create an embedding for the given text using OpenAI's API.

    Args:
        text: The text to create an embedding for
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 2)

    Returns:
        The embedding as a list of floats, or None if the embedding could not be created
    """
    if not text:
        return None

    # Implement exponential backoff for retries
    for attempt in range(max_retries + 1):
        try:
            response = client.embeddings.create(
                model="text-embedding-3-small", input=text
            )
            return response.data[0].embedding
        except Exception as e:
            # Check if this is a rate limit or quota error
            error_str = str(e).lower()
            is_rate_limit = any(
                term in error_str
                for term in [
                    "rate limit",
                    "quota",
                    "capacity",
                    "too many requests",
                    "429",
                ]
            )

            # Log the error
            if is_rate_limit:
                logger.warning(
                    f"OpenAI API rate limit error (attempt {attempt+1}/{max_retries+1}): {str(e)}"
                )
            else:
                logger.error(
                    f"Embedding creation error (attempt {attempt+1}/{max_retries+1}): {str(e)}"
                )

            # If this is the last attempt, return None
            if attempt >= max_retries:
                logger.error(
                    f"Embedding creation failed after {max_retries+1} attempts."
                )
                return None

            # Calculate exponential backoff delay (2^attempt * retry_delay)
            backoff_delay = retry_delay * (2**attempt)
            logger.info(f"Retrying embedding creation in {backoff_delay} seconds...")

            # Wait before retrying
            time.sleep(backoff_delay)

    return None


def cosine_similarity(a: List[float], b: List[float]) -> float:
    """
    Calculate the cosine similarity between two vectors.

    Args:
        a: First vector
        b: Second vector

    Returns:
        Cosine similarity between the vectors (between -1 and 1)
    """
    a_np = np.array(a)
    b_np = np.array(b)

    # Calculate dot product
    dot_product = np.dot(a_np, b_np)

    # Calculate magnitudes
    magnitude_a = np.linalg.norm(a_np)
    magnitude_b = np.linalg.norm(b_np)

    # Calculate cosine similarity
    if magnitude_a == 0 or magnitude_b == 0:
        return 0

    return dot_product / (magnitude_a * magnitude_b)


def load_custom_answers(
    max_retries: int = 3, retry_delay: int = 5
) -> List[CustomAnswer]:
    """
    Load custom answers from the Google Sheet and create embeddings for them.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        List of CustomAnswer objects with embeddings
    """
    global custom_answers_cache

    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return []

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return []

    # Initialize the sheet if needed
    sheet_initialized = False
    for attempt in range(max_retries + 1):
        try:
            if initialize_custom_answers_sheet():
                sheet_initialized = True
                break
            else:
                if attempt < max_retries:
                    logger.warning(
                        f"Failed to initialize sheet, retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                    )
                    time.sleep(retry_delay)
                else:
                    logger.error(
                        f"Failed to initialize sheet after {max_retries} attempts"
                    )
                    return []
        except Exception as e:
            if attempt < max_retries:
                logger.warning(f"Error initializing sheet: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to initialize sheet after {max_retries} attempts: {str(e)}"
                )
                return []

    # Load the custom answers from the sheet
    try:
        result = (
            service.spreadsheets()
            .values()
            .get(
                spreadsheetId=GOOGLE_SHEETS_ID,
                range="CustomAnswers!A2:B",  # Only get question and answer columns
            )
            .execute()
        )

        values = result.get("values", [])
        if not values:
            logger.info("No custom answers found in Google Sheet")
            custom_answers_cache = []
            return []

        # Create custom answers with embeddings
        custom_answers = []
        for i, row in enumerate(values):
            if len(row) < 2:
                logger.warning(f"Skipping row {i+2} due to missing data: {row}")
                continue

            question_pattern = row[0].strip()
            answer = row[1].strip()

            if not question_pattern or not answer:
                logger.warning(f"Skipping row {i+2} due to empty question or answer")
                continue

            # Translate Arabic question patterns to English for embedding
            embedding_text = question_pattern
            if is_arabic(question_pattern):
                embedding_text = translate_text(question_pattern, "English")
                logger.info(
                    f"Translated Arabic question pattern for embedding: {embedding_text[:50]}..."
                )

            # Create embedding for the question pattern (in English)
            embedding = create_embedding(embedding_text)

            if embedding:
                custom_answer = CustomAnswer(
                    question_pattern=question_pattern,
                    answer=answer,
                    embedding=embedding,
                )
                custom_answers.append(custom_answer)
                logger.info(f"Loaded custom answer: {question_pattern[:50]}...")
            else:
                logger.warning(
                    f"Failed to create embedding for question pattern: {question_pattern[:50]}..."
                )

        # Update the cache
        custom_answers_cache = custom_answers
        logger.info(f"Loaded {len(custom_answers)} custom answers from Google Sheet")
        return custom_answers

    except Exception as e:
        logger.error(f"Error loading custom answers from Google Sheet: {str(e)}")
        return []


def find_matching_custom_answer(
    question: str, similarity_threshold: float = 0.6
) -> Optional[CustomAnswer]:
    """
    Find the custom answer that best matches the given question.

    Args:
        question: The question to match
        similarity_threshold: The minimum similarity score to consider a match (default: 0.6)

    Returns:
        The best matching CustomAnswer, or None if no match is found
    """
    if not question or not custom_answers_cache:
        return None

    # Create embedding for the question
    question_embedding = create_embedding(question)
    if not question_embedding:
        logger.error(f"Failed to create embedding for question: {question[:50]}...")
        return None

    # Find the best match
    best_match = None
    best_score = -1

    for custom_answer in custom_answers_cache:
        if not custom_answer.embedding:
            continue

        # Calculate similarity
        similarity = cosine_similarity(question_embedding, custom_answer.embedding)

        # Update best match if this is better
        if similarity > best_score:
            best_score = similarity
            best_match = custom_answer

    # Check if the best match exceeds the threshold
    if best_match and best_score >= similarity_threshold:
        logger.info(
            f"Found matching custom answer with similarity score {best_score:.4f}: {best_match.question_pattern[:50]}..."
        )
        return best_match
    else:
        if best_match:
            logger.info(
                f"Best match had similarity score {best_score:.4f}, below threshold {similarity_threshold}"
            )
        else:
            logger.info(
                f"No matching custom answer found for question: {question[:50]}..."
            )
        return None


def refresh_custom_answers() -> bool:
    """
    Refresh the custom answers cache from the Google Sheet.

    Returns:
        True if successful, False otherwise
    """
    try:
        load_custom_answers()
        return True
    except Exception as e:
        logger.error(f"Error refreshing custom answers: {str(e)}")
        return False
