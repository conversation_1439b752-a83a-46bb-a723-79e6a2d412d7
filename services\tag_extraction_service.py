"""
Tag Extraction Service Module

Handles extraction of tags from CVs, coverage calculation, and gap analysis.
Implements the hierarchical tag system for comprehensive candidate assessment.
"""

import logging
import re
import json
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from datetime import datetime
from uuid import UUID

from langchain_openai import ChatOpenAI
from database.models import (
    TagCategory,
    TagDefinition,
    CandidateTag,
    CandidateTagCoverage,
    TagDataType,
    TagSource,
    JobLevel,
)
from database import database_service
from utils.assessment_config import AssessmentConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("tag_extraction_service")


class TagExtractionService:
    """Service for extracting and managing candidate tags from CVs"""

    def __init__(self, db_service, model_name: str = "gpt-4.1"):
        """Initialize the tag extraction service"""
        self.db_service = db_service
        self.model = ChatOpenAI(model=model_name, temperature=0.3, max_tokens=4000)

        # Cache for tag definitions
        self._tag_cache = None
        self._category_cache = None

        # OPTIMIZATION: Request-level data cache to eliminate redundant queries
        self._request_cache = {
            "candidate_tags": {},  # cv_assessment_id -> tags data
            "cv_assessments": {},  # assessment_id -> assessment data
            "coverage_data": {},  # cv_assessment_id -> coverage data
            "gap_analysis": {},  # cv_assessment_id -> gap analysis data
        }

    async def extract_tags_from_cv(
        self,
        cv_text: str,
        cv_assessment_id: UUID,
        job_description: Optional[str] = None,
        refresh_cache: bool = False,
    ) -> Dict[str, Any]:
        """
        Extract all possible tags from CV text using AI with optimized single API call.

        Args:
            cv_text: The CV text to analyze
            cv_assessment_id: The assessment ID to associate tags with
            job_description: Optional job description for context
            refresh_cache: Whether to refresh the tag definition cache

        Returns:
            Dictionary with extraction results and coverage analysis
        """
        try:
            # Ensure tag definitions and categories are loaded
            await self._load_tag_definitions(refresh_cache)

            # Validate that cache is properly loaded
            if not self._category_cache or not self._tag_cache:
                raise ValueError("Failed to load tag definitions - cache is empty")

            logger.info(
                f"Tag extraction starting with {len(self._category_cache)} categories and {sum(len(tags) for tags in self._tag_cache.values())} tags"
            )

            # OPTIMIZATION: Extract tags for ALL categories in a single API call
            extracted_tags = await self._extract_all_tags_single_call(
                cv_text, job_description
            )

            # OPTIMIZATION: Clear request cache for new request
            self._clear_request_cache()

            # OPTIMIZATION: Store extracted tags in database using batch operations
            stored_tags = await self._batch_store_extracted_tags(
                cv_assessment_id, extracted_tags
            )

            # OPTIMIZATION: Calculate coverage using batch operations and cached data
            coverage_analysis = await self._batch_calculate_coverage(cv_assessment_id)

            # OPTIMIZATION: Perform gap analysis with cached data (single run)
            gap_analysis = await self._fast_analyze_gaps(
                cv_assessment_id, coverage_analysis
            )

            return {
                "extracted_tags": stored_tags,
                "coverage_analysis": coverage_analysis,
                "gap_analysis": gap_analysis,
                "extraction_complete": True,
            }

        except Exception as e:
            logger.error(f"Failed to extract tags from CV: {str(e)}")
            return {
                "extracted_tags": {},
                "coverage_analysis": {},
                "gap_analysis": {},
                "extraction_complete": False,
                "error": str(e),
            }

    async def _load_tag_definitions(self, refresh: bool = False):
        """Load tag definitions and categories from database"""
        if refresh or self._tag_cache is None or self._category_cache is None:
            # Load categories
            categories_data = self.db_service.get_all_tag_categories(active_only=True)
            self._category_cache = [TagCategory(**cat) for cat in categories_data]

            # Load tag definitions for each category
            self._tag_cache = {}
            for category in self._category_cache:
                tags_data = self.db_service.get_tag_definitions_by_category(
                    str(category.id)
                )
                self._tag_cache[category.id] = [
                    TagDefinition(**tag) for tag in tags_data
                ]

    def _clear_request_cache(self):
        """Clear request-level cache - call at start of new request"""
        self._request_cache = {
            "candidate_tags": {},
            "cv_assessments": {},
            "coverage_data": {},
            "gap_analysis": {},
        }

    async def _get_cached_candidate_tags(
        self, cv_assessment_id: UUID
    ) -> List[Dict[str, Any]]:
        """Get candidate tags with caching to eliminate redundant queries"""
        cache_key = str(cv_assessment_id)

        if cache_key not in self._request_cache["candidate_tags"]:
            logger.info(
                f"Loading candidate tags for assessment {cv_assessment_id} (cache miss)"
            )
            tags_data = self.db_service.get_candidate_tags(cache_key)
            self._request_cache["candidate_tags"][cache_key] = tags_data
        else:
            logger.info(
                f"Using cached candidate tags for assessment {cv_assessment_id}"
            )

        return self._request_cache["candidate_tags"][cache_key]

    async def _get_cached_cv_assessment(
        self, cv_assessment_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Get CV assessment with caching to eliminate redundant queries"""
        cache_key = str(cv_assessment_id)

        if cache_key not in self._request_cache["cv_assessments"]:
            logger.info(f"Loading CV assessment {cv_assessment_id} (cache miss)")
            assessment_data = self.db_service.get_cv_assessment_by_id(
                int(cv_assessment_id)
            )
            self._request_cache["cv_assessments"][cache_key] = assessment_data
        else:
            logger.info(f"Using cached CV assessment {cv_assessment_id}")

        return self._request_cache["cv_assessments"][cache_key]

    async def _extract_all_tags_single_call(
        self, cv_text: str, job_description: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        OPTIMIZED: Extract tags for ALL categories in a single API call.
        This reduces API calls from 6 sequential calls to 1 call, dramatically improving performance.
        """
        try:
            # Build comprehensive extraction prompt for all categories
            all_categories_info = []
            tag_key_to_definition = {}

            for category in self._category_cache:
                tag_definitions = self._tag_cache.get(category.id, [])
                if not tag_definitions:
                    continue

                # Build tag info for this category
                category_tags_info = []
                for tag in tag_definitions:
                    hints = (
                        ", ".join(tag.extraction_hints)
                        if tag.extraction_hints
                        else "none"
                    )
                    category_tags_info.append(
                        f"    - {tag.tag_key} ({tag.display_name}): {tag.description or 'N/A'}\n"
                        f"      Data type: {tag.data_type}, Hints: {hints}"
                    )
                    tag_key_to_definition[tag.tag_key] = tag

                if category_tags_info:
                    all_categories_info.append(
                        f"  {category.display_name} ({category.name}):\n"
                        f"  Description: {category.description}\n"
                        + "\n".join(category_tags_info)
                    )

            extraction_prompt = f"""
You are an expert CV analyzer extracting comprehensive information across ALL categories in a single analysis.

CATEGORIES AND TAGS TO EXTRACT:
{chr(10).join(all_categories_info)}

CV TEXT:
{cv_text}

{f"JOB CONTEXT: {job_description}" if job_description else ""}

INSTRUCTIONS:
1. Extract values for ALL tags from the CV text in a single comprehensive analysis
2. Only extract information that is explicitly stated or clearly implied
3. For 'list' data types, return comma-separated values
4. For 'number' data types, extract numeric values only
5. For 'date' data types, extract dates in YYYY-MM-DD format when possible
6. If information is not found, use null
7. Be accurate and specific - do not make assumptions

Return a JSON object with tag_key as keys and extracted values. Example structure:
{{
    "degree_level": "Masters",
    "university_name": "MIT", 
    "gpa": 3.8,
    "graduation_year": "2020-06-01",
    "major_field": "Computer Science",
    "job_title": "Software Engineer",
    "company_name": "Google",
    "years_experience": 5,
    "programming_languages": "Python, Java, JavaScript",
    "certifications": "AWS Certified Solutions Architect"
}}

IMPORTANT: Include ALL relevant tags found in the CV, across all categories.
"""

            logger.info("Making single API call for all categories tag extraction")
            response = self.model.invoke(extraction_prompt)
            extracted_data = self._parse_extraction_response(response.content)

            logger.info(
                f"Single API call completed, extracted {len(extracted_data)} tags"
            )

            # Organize extracted data by category
            extracted_tags = {}
            for category in self._category_cache:
                category_tags = []
                tag_definitions = self._tag_cache.get(category.id, [])

                for tag_def in tag_definitions:
                    value = extracted_data.get(tag_def.tag_key)
                    if value is not None and value != "null":
                        # Validate and format based on data type
                        formatted_value = self._format_tag_value(
                            value, tag_def.data_type
                        )
                        if formatted_value is not None:
                            # Calculate dynamic confidence score
                            confidence_score = self._calculate_extraction_confidence(
                                formatted_value, tag_def, response.content, TagSource.CV
                            )

                            category_tags.append(
                                {
                                    "tag_definition_id": tag_def.id,
                                    "tag_key": tag_def.tag_key,
                                    "value": formatted_value,
                                    "confidence_score": confidence_score,
                                    "source": TagSource.CV,
                                }
                            )

                extracted_tags[category.name] = category_tags
                logger.info(
                    f"Category '{category.name}': {len(category_tags)} tags extracted"
                )

            return extracted_tags

        except Exception as e:
            logger.error(f"Failed to extract tags in single call: {str(e)}")
            # Fallback to original method if single call fails
            logger.info("Falling back to sequential category extraction")
            return await self._extract_tags_sequential_fallback(
                cv_text, job_description
            )

    async def _extract_tags_sequential_fallback(
        self, cv_text: str, job_description: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Fallback method using sequential category extraction"""
        extracted_tags = {}
        for category in self._category_cache:
            category_tags = await self._extract_category_tags(
                cv_text, category, job_description
            )
            extracted_tags[category.name] = category_tags
        return extracted_tags

    async def _extract_category_tags(
        self, cv_text: str, category: TagCategory, job_description: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Extract tags for a specific category using AI"""

        # Get tag definitions for this category
        tag_definitions = self._tag_cache.get(category.id, [])

        if not tag_definitions:
            logger.warning(f"No tag definitions found for category: {category.name}")
            return []

        # Build extraction prompt
        tag_info = []
        for tag in tag_definitions:
            hints = ", ".join(tag.extraction_hints) if tag.extraction_hints else "none"
            tag_info.append(
                f"- {tag.tag_key} ({tag.display_name}): {tag.description or 'N/A'}\n"
                f"  Data type: {tag.data_type}, Hints: {hints}"
            )

        extraction_prompt = f"""
You are an expert CV analyzer extracting specific information for the '{category.display_name}' category.

Category Description: {category.description}

Tags to extract:
{chr(10).join(tag_info)}

CV Text:
{cv_text}

{f"Job Context: {job_description}" if job_description else ""}

Instructions:
1. Extract values for each tag from the CV text
2. Only extract information that is explicitly stated or clearly implied
3. For 'list' data types, return comma-separated values
4. For 'number' data types, extract numeric values only
5. For 'date' data types, extract dates in YYYY-MM-DD format
6. If information is not found, use null
7. Be accurate and specific - do not make assumptions

Return a JSON object with tag_key as keys and extracted values. Example:
{{
    "degree_level": "Masters",
    "university_name": "MIT",
    "gpa": 3.8,
    "graduation_year": "2020-06-01",
    "major_field": "Computer Science"
}}
"""

        try:
            response = self.model.invoke(extraction_prompt)
            extracted_data = self._parse_extraction_response(response.content)

            # Convert to list of tag dictionaries
            extracted_tags = []
            for tag_def in tag_definitions:
                value = extracted_data.get(tag_def.tag_key)
                if value is not None and value != "null":
                    # Validate and format based on data type
                    formatted_value = self._format_tag_value(value, tag_def.data_type)
                    if formatted_value is not None:
                        # Calculate dynamic confidence score
                        confidence_score = self._calculate_extraction_confidence(
                            formatted_value, tag_def, response.content, TagSource.CV
                        )

                        extracted_tags.append(
                            {
                                "tag_definition_id": tag_def.id,
                                "tag_key": tag_def.tag_key,
                                "value": formatted_value,
                                "confidence_score": confidence_score,
                                "source": TagSource.CV,
                            }
                        )

            return extracted_tags

        except Exception as e:
            logger.error(
                f"Failed to extract tags for category {category.name}: {str(e)}"
            )
            return []

    def _parse_extraction_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response to extract JSON data"""
        try:
            # Try to find JSON in the response
            json_match = re.search(r"\{[^}]+\}", response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())

            # If no JSON found, try to parse the whole response
            return json.loads(response)

        except json.JSONDecodeError:
            logger.error(f"Failed to parse extraction response: {response}")
            return {}

    def _format_tag_value(self, value: Any, data_type: TagDataType) -> Optional[str]:
        """Format tag value based on data type"""
        try:
            if value is None or value == "null" or value == "":
                return None

            # Convert to string and clean up
            value_str = str(value).strip()
            if not value_str or value_str.lower() in ["null", "none", "n/a", "unknown"]:
                return None

            if data_type == TagDataType.TEXT:
                return value_str

            elif data_type == TagDataType.NUMBER:
                # Extract numeric value
                numeric_match = re.search(r"[\d.]+", value_str)
                if numeric_match:
                    numeric_value = numeric_match.group()
                    # Validate it's a reasonable number
                    try:
                        float(numeric_value)
                        return numeric_value
                    except ValueError:
                        logger.warning(f"Invalid numeric value: {numeric_value}")
                        return None
                return None

            elif data_type == TagDataType.BOOLEAN:
                # Handle various boolean representations
                true_values = ["true", "yes", "1", "on", "enabled", "positive"]
                false_values = ["false", "no", "0", "off", "disabled", "negative"]

                value_lower = value_str.lower()
                if value_lower in true_values:
                    return "true"
                elif value_lower in false_values:
                    return "false"
                else:
                    logger.warning(f"Unrecognized boolean value: {value_str}")
                    return None

            elif data_type == TagDataType.DATE:
                # Try to parse and format date
                # For now, return as is - could add date parsing logic
                # Remove common date prefixes/suffixes
                date_clean = re.sub(
                    r"^(date|on|from|since|until)\s*",
                    "",
                    value_str,
                    flags=re.IGNORECASE,
                )
                date_clean = re.sub(
                    r"\s*(date|ago|before|after)$", "", date_clean, flags=re.IGNORECASE
                )
                return date_clean.strip()

            elif data_type == TagDataType.LIST:
                if isinstance(value, list):
                    # Clean and filter list items
                    cleaned_items = [
                        str(v).strip() for v in value if v and str(v).strip()
                    ]
                    return ", ".join(cleaned_items) if cleaned_items else None
                else:
                    # Handle comma-separated string
                    items = [
                        item.strip() for item in value_str.split(",") if item.strip()
                    ]
                    return ", ".join(items) if items else None

            return value_str

        except Exception as e:
            logger.error(
                f"Failed to format tag value {value} for data type {data_type}: {str(e)}"
            )
            return None

    async def _store_extracted_tags(
        self, cv_assessment_id: UUID, extracted_tags: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, List[CandidateTag]]:
        """Store extracted tags in the database"""
        stored_tags = {}
        total_extracted = 0
        total_stored = 0

        for category_name, tags in extracted_tags.items():
            stored_category_tags = []
            category_extracted = len(tags)
            category_stored = 0

            logger.info(
                f"Processing {category_extracted} tags for category '{category_name}'"
            )

            for tag_data in tags:
                try:
                    # Create candidate tag
                    candidate_tag_data = self.db_service.create_candidate_tag(
                        str(cv_assessment_id),
                        str(tag_data["tag_definition_id"]),
                        tag_data["value"],
                        tag_data["source"].value,
                        float(tag_data["confidence_score"]),
                    )

                    if candidate_tag_data:
                        stored_category_tags.append(CandidateTag(**candidate_tag_data))
                        category_stored += 1
                        logger.info(
                            f"Successfully stored tag '{tag_data['tag_key']}' with value '{tag_data['value']}'"
                        )
                    else:
                        logger.warning(
                            f"Failed to store tag '{tag_data['tag_key']}' - database returned None"
                        )

                except Exception as e:
                    logger.error(
                        f"Error storing tag '{tag_data.get('tag_key', 'unknown')}': {str(e)}"
                    )

            stored_tags[category_name] = stored_category_tags
            total_extracted += category_extracted
            total_stored += category_stored

            logger.info(
                f"Category '{category_name}': {category_stored}/{category_extracted} tags stored successfully"
            )

        logger.info(
            f"Tag storage complete: {total_stored}/{total_extracted} tags stored across all categories"
        )

        # TODO: Update CV assessment to mark tag extraction as complete
        # This would need to be implemented in database_service.py

        return stored_tags

    async def _batch_store_extracted_tags(
        self, cv_assessment_id: UUID, extracted_tags: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, List[CandidateTag]]:
        """
        OPTIMIZED: Store extracted tags using batch database operations.
        This reduces database calls from 24+ individual INSERTs to 1-2 batch operations.
        """
        try:
            # Prepare all tags for batch insertion
            all_tags_data = []
            total_extracted = 0

            for category_name, tags in extracted_tags.items():
                for tag_data in tags:
                    all_tags_data.append(
                        {
                            "cv_assessment_id": str(cv_assessment_id),
                            "tag_definition_id": str(tag_data["tag_definition_id"]),
                            "value": tag_data["value"],
                            "source": tag_data["source"].value,
                            "confidence_score": float(tag_data["confidence_score"]),
                        }
                    )
                    total_extracted += 1

            logger.info(f"Preparing batch insert for {total_extracted} tags")

            if not all_tags_data:
                logger.info("No tags to store")
                return {}

            # Perform batch insert
            stored_tags_data = await self._batch_insert_candidate_tags(all_tags_data)

            # Organize stored tags by category for return value
            stored_tags = {}
            stored_count = 0

            for category_name, tags in extracted_tags.items():
                stored_category_tags = []

                for tag_data in tags:
                    # Find corresponding stored tag
                    for stored_tag in stored_tags_data:
                        if stored_tag.get("tag_definition_id") == str(
                            tag_data["tag_definition_id"]
                        ) and stored_tag.get("cv_assessment_id") == str(
                            cv_assessment_id
                        ):
                            stored_category_tags.append(CandidateTag(**stored_tag))
                            stored_count += 1
                            break

                stored_tags[category_name] = stored_category_tags
                logger.info(
                    f"Category '{category_name}': {len(stored_category_tags)} tags stored"
                )

            logger.info(
                f"Batch storage complete: {stored_count}/{total_extracted} tags stored successfully"
            )
            return stored_tags

        except Exception as e:
            logger.error(f"Batch storage failed: {str(e)}")
            # Fallback to individual storage
            logger.info("Falling back to individual tag storage")
            return await self._store_extracted_tags(cv_assessment_id, extracted_tags)

    async def _batch_insert_candidate_tags(
        self, tags_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Perform batch insert of candidate tags with conflict resolution.
        """
        try:
            # Check if batch insert method exists in database service
            if hasattr(self.db_service, "batch_create_candidate_tags"):
                return self.db_service.batch_create_candidate_tags(tags_data)
            else:
                # Use Supabase batch insert directly
                result = (
                    self.db_service.supabase_client.table("candidate_tags")
                    .upsert(tags_data, on_conflict="cv_assessment_id,tag_definition_id")
                    .execute()
                )
                return result.data if result.data else []

        except Exception as e:
            logger.error(f"Batch insert failed: {str(e)}")
            # Fallback to individual inserts
            stored_tags = []
            for tag_data in tags_data:
                try:
                    stored_tag = self.db_service.create_candidate_tag(
                        tag_data["cv_assessment_id"],
                        tag_data["tag_definition_id"],
                        tag_data["value"],
                        tag_data["source"],
                        tag_data["confidence_score"],
                    )
                    if stored_tag:
                        stored_tags.append(stored_tag)
                except Exception as individual_error:
                    logger.error(f"Individual insert failed: {str(individual_error)}")
                    continue
            return stored_tags

    async def calculate_coverage(
        self,
        cv_assessment_id: UUID,
        threshold_overrides: Optional[Dict[UUID, float]] = None,
    ) -> Dict[str, Any]:
        """
        Calculate tag coverage percentage per category.

        Args:
            cv_assessment_id: The assessment ID
            threshold_overrides: Optional override thresholds per category

        Returns:
            Coverage analysis per category
        """
        try:
            # Get all categories
            categories_data = self.db_service.get_all_tag_categories(active_only=True)
            categories = [TagCategory(**cat) for cat in categories_data]

            # Get all extracted tags for this assessment
            extracted_tags_data = self.db_service.get_candidate_tags(
                str(cv_assessment_id)
            )
            extracted_tags = [CandidateTag(**tag) for tag in extracted_tags_data]

            # Group tags by category
            tags_by_category = {}
            for tag in extracted_tags:
                if tag.tag_definition:
                    cat_id = tag.tag_definition.category_id
                    if cat_id not in tags_by_category:
                        tags_by_category[cat_id] = []
                    tags_by_category[cat_id].append(tag)

            # Calculate coverage per category
            coverage_results = {}

            for category in categories:
                # Get total tags in category
                total_tags = len(self._tag_cache.get(category.id, []))

                # Get filled tags
                filled_tags = len(tags_by_category.get(category.id, []))

                # Calculate percentage
                coverage_percentage = (
                    (filled_tags / total_tags * 100) if total_tags > 0 else 0
                )

                # Get threshold
                threshold = (
                    threshold_overrides.get(category.id)
                    if threshold_overrides
                    else None
                )
                if threshold is None:
                    threshold = float(category.default_threshold * 100)

                # Check if complete
                is_complete = coverage_percentage >= threshold

                # Store coverage in database
                self.db_service.update_tag_coverage(
                    str(cv_assessment_id),
                    str(category.id),
                    total_tags,
                    filled_tags,
                    threshold,
                )

                coverage_results[category.name] = {
                    "category_id": category.id,
                    "display_name": category.display_name,
                    "total_tags": total_tags,
                    "filled_tags": filled_tags,
                    "coverage_percentage": coverage_percentage,
                    "threshold_percentage": threshold,
                    "is_complete": is_complete,
                    "missing_count": total_tags - filled_tags,
                }

            # Calculate overall coverage
            total_all = sum(r["total_tags"] for r in coverage_results.values())
            filled_all = sum(r["filled_tags"] for r in coverage_results.values())
            overall_coverage = (filled_all / total_all * 100) if total_all > 0 else 0

            return {
                "categories": coverage_results,
                "overall_coverage": overall_coverage,
                "complete_categories": sum(
                    1 for r in coverage_results.values() if r["is_complete"]
                ),
                "total_categories": len(categories),
            }

        except Exception as e:
            logger.error(f"Failed to calculate coverage: {str(e)}")
            return {}

    async def analyze_gaps(
        self, cv_assessment_id: UUID, priority_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Analyze gaps in tag coverage and identify missing subtags.

        Args:
            cv_assessment_id: The assessment ID
            priority_threshold: Minimum coverage threshold to prioritize

        Returns:
            Gap analysis with missing tags and priorities
        """
        try:
            # OPTIMIZATION: Use cached coverage data instead of recalculating
            cache_key = str(cv_assessment_id)
            if cache_key in self._request_cache["coverage_data"]:
                coverage = self._request_cache["coverage_data"][cache_key]
                logger.info(f"Using cached coverage data for gap analysis")
            else:
                # Fallback to calculation if not cached
                coverage = await self.calculate_coverage(cv_assessment_id)
                logger.info(f"Coverage not cached, calculated fresh for gap analysis")

            if not coverage:
                return {}

            # Get extracted tags
            extracted_tags_data = self.db_service.get_candidate_tags(
                str(cv_assessment_id)
            )
            extracted_tags = [CandidateTag(**tag) for tag in extracted_tags_data]

            # Only consider tags with meaningful values as "extracted"
            extracted_tag_ids = set()
            for tag_data in extracted_tags_data:
                if "tag_definitions" in tag_data and tag_data["tag_definitions"]:
                    # Check if the tag has a meaningful value (not empty, not just whitespace)
                    tag_value = tag_data.get("value", "").strip()
                    if tag_value and tag_value.lower() not in [
                        "",
                        "none",
                        "null",
                        "n/a",
                        "not applicable",
                    ]:
                        extracted_tag_ids.add(tag_data["tag_definition_id"])

            # Analyze gaps per category
            gap_analysis = {
                "priority_categories": [],
                "missing_tags": {},
                "recommended_questions": [],
            }

            for category_name, coverage_data in coverage["categories"].items():
                if not coverage_data["is_complete"]:
                    # Get category
                    category = next(
                        (c for c in self._category_cache if c.name == category_name),
                        None,
                    )

                    if not category:
                        continue

                    # Find missing tags
                    category_tags = self._tag_cache.get(category.id, [])
                    missing_tags = [
                        tag for tag in category_tags if tag.id not in extracted_tag_ids
                    ]

                    # Calculate priority score
                    coverage_gap = (
                        coverage_data["threshold_percentage"]
                        - coverage_data["coverage_percentage"]
                    )
                    priority_score = coverage_gap * (
                        category.priority / 10
                    )  # Normalize priority

                    gap_analysis["missing_tags"][category_name] = [
                        {
                            "tag_key": tag.tag_key,
                            "display_name": tag.display_name,
                            "questions": tag.question_templates,
                            "is_required": tag.is_required,
                            "weight": float(tag.weight),
                        }
                        for tag in missing_tags
                    ]

                    if coverage_data["coverage_percentage"] < priority_threshold * 100:
                        gap_analysis["priority_categories"].append(
                            {
                                "category": category_name,
                                "display_name": category.display_name,
                                "coverage": coverage_data["coverage_percentage"],
                                "threshold": coverage_data["threshold_percentage"],
                                "gap": coverage_gap,
                                "priority_score": priority_score,
                                "missing_count": coverage_data["missing_count"],
                            }
                        )

                    # Add recommended questions
                    for tag in missing_tags[:3]:  # Top 3 missing tags
                        if tag.question_templates:
                            gap_analysis["recommended_questions"].extend(
                                tag.question_templates[:1]  # First question template
                            )

            # Sort priority categories by priority score
            gap_analysis["priority_categories"].sort(
                key=lambda x: x["priority_score"], reverse=True
            )

            # Limit recommended questions
            gap_analysis["recommended_questions"] = gap_analysis[
                "recommended_questions"
            ][:5]

            return gap_analysis

        except Exception as e:
            logger.error(f"Failed to analyze gaps: {str(e)}")
            return {}

    async def get_missing_tags_for_category(
        self, cv_assessment_id: UUID, category_name: str
    ) -> List[TagDefinition]:
        """Get list of missing tags for a specific category"""
        try:
            # Get category
            category = next(
                (c for c in self._category_cache if c.name == category_name), None
            )

            if not category:
                return []

            # Get extracted tags
            extracted_tags_data = self.db_service.get_candidate_tags(
                str(cv_assessment_id)
            )

            extracted_tag_ids = set()
            for tag_data in extracted_tags_data:
                if "tag_definitions" in tag_data and tag_data["tag_definitions"]:
                    tag_def = tag_data["tag_definitions"]
                    if tag_def.get("category_id") == str(category.id):
                        extracted_tag_ids.add(tag_data["tag_definition_id"])

            # Get all tags for category
            category_tags = self._tag_cache.get(category.id, [])

            # Return missing tags
            return [tag for tag in category_tags if tag.id not in extracted_tag_ids]

        except Exception as e:
            logger.error(f"Failed to get missing tags for category: {str(e)}")
            return []

    # OPTIMIZATION METHODS - Batch operations and caching

    async def _batch_calculate_coverage(
        self,
        cv_assessment_id: UUID,
        threshold_overrides: Optional[Dict[UUID, float]] = None,
    ) -> Dict[str, Any]:
        """
        OPTIMIZED: Calculate coverage using batch operations and cached data.
        This reduces database calls from 12+ individual operations to 1-2 batch operations.
        """
        try:
            # Use cached data instead of fresh queries
            categories_data = self.db_service.get_all_tag_categories(active_only=True)
            categories = [TagCategory(**cat) for cat in categories_data]

            # Use cached candidate tags
            extracted_tags_data = await self._get_cached_candidate_tags(
                cv_assessment_id
            )
            extracted_tags = [CandidateTag(**tag) for tag in extracted_tags_data]

            # Group tags by category
            tags_by_category = {}
            for tag in extracted_tags:
                if tag.tag_definition:
                    cat_id = tag.tag_definition.category_id
                    if cat_id not in tags_by_category:
                        tags_by_category[cat_id] = []
                    tags_by_category[cat_id].append(tag)

            # Prepare batch coverage data
            coverage_updates = []
            coverage_results = {}

            for category in categories:
                # Get total tags in category
                total_tags = len(self._tag_cache.get(category.id, []))

                # Get filled tags
                filled_tags = len(tags_by_category.get(category.id, []))

                # Calculate percentage
                coverage_percentage = (
                    (filled_tags / total_tags * 100) if total_tags > 0 else 0
                )

                # Get threshold
                threshold = (
                    threshold_overrides.get(category.id)
                    if threshold_overrides
                    else None
                )
                if threshold is None:
                    threshold = float(category.default_threshold * 100)

                # Check if complete
                is_complete = coverage_percentage >= threshold

                # Prepare for batch update
                coverage_updates.append(
                    {
                        "cv_assessment_id": str(cv_assessment_id),
                        "category_id": str(category.id),
                        "total_tags": total_tags,
                        "filled_tags": filled_tags,
                        "threshold_percentage": threshold,
                        "coverage_percentage": coverage_percentage,
                        "is_complete": is_complete,
                    }
                )

                coverage_results[category.name] = {
                    "category_id": category.id,
                    "display_name": category.display_name,
                    "total_tags": total_tags,
                    "filled_tags": filled_tags,
                    "coverage_percentage": coverage_percentage,
                    "threshold_percentage": threshold,
                    "is_complete": is_complete,
                    "missing_count": total_tags - filled_tags,
                }

            # OPTIMIZATION: Batch update all coverage data in single operation
            self._batch_update_coverage(coverage_updates)

            # Calculate overall coverage
            total_all = sum(r["total_tags"] for r in coverage_results.values())
            filled_all = sum(r["filled_tags"] for r in coverage_results.values())
            overall_coverage = (filled_all / total_all * 100) if total_all > 0 else 0

            result = {
                "categories": coverage_results,
                "overall_coverage": overall_coverage,
                "complete_categories": sum(
                    1 for r in coverage_results.values() if r["is_complete"]
                ),
                "total_categories": len(categories),
            }

            # Cache the result
            self._request_cache["coverage_data"][str(cv_assessment_id)] = result
            logger.info(
                f"Calculated and cached coverage for assessment {cv_assessment_id}"
            )

            return result

        except Exception as e:
            logger.error(f"Failed to calculate coverage: {str(e)}")
            return {}

    def _batch_update_coverage(self, coverage_updates: List[Dict[str, Any]]):
        """Batch update coverage data to reduce database operations"""
        try:
            if hasattr(self.db_service, "batch_update_tag_coverage"):
                self.db_service.batch_update_tag_coverage(coverage_updates)
            else:
                # Fallback to individual updates if batch method not available
                for update in coverage_updates:
                    self.db_service.update_tag_coverage(
                        update["cv_assessment_id"],
                        update["category_id"],
                        update["total_tags"],
                        update["filled_tags"],
                        update["threshold_percentage"],
                    )
            logger.info(
                f"Batch updated coverage for {len(coverage_updates)} categories"
            )
        except Exception as e:
            logger.error(f"Failed to batch update coverage: {str(e)}")

    async def _fast_analyze_gaps(
        self,
        cv_assessment_id: UUID,
        coverage_analysis: Dict[str, Any],
        priority_threshold: float = 0.7,
    ) -> Dict[str, Any]:
        """
        OPTIMIZED: Analyze gaps using cached coverage data and single gap analysis.
        This eliminates redundant gap analysis runs.
        """
        try:
            # Check if gap analysis is already cached
            cache_key = str(cv_assessment_id)
            if cache_key in self._request_cache["gap_analysis"]:
                logger.info(
                    f"Using cached gap analysis for assessment {cv_assessment_id}"
                )
                return self._request_cache["gap_analysis"][cache_key]

            logger.info(f"Performing gap analysis for assessment {cv_assessment_id}")

            # Use provided coverage analysis instead of recalculating
            coverage = coverage_analysis
            if not coverage:
                return {}

            # Use cached candidate tags
            extracted_tags_data = await self._get_cached_candidate_tags(
                cv_assessment_id
            )

            # Only consider tags with meaningful values as "extracted"
            extracted_tag_ids = set()
            for tag_data in extracted_tags_data:
                if "tag_definitions" in tag_data and tag_data["tag_definitions"]:
                    # Check if the tag has a meaningful value (not empty, not just whitespace)
                    tag_value = tag_data.get("value", "").strip()
                    if tag_value and tag_value.lower() not in [
                        "",
                        "none",
                        "null",
                        "n/a",
                        "not applicable",
                        "unknown",
                    ]:
                        extracted_tag_ids.add(tag_data["tag_definitions"]["id"])

            # Identify priority categories (below threshold)
            priority_categories = []
            missing_tags = {}

            for category_name, category_data in coverage["categories"].items():
                coverage_pct = category_data["coverage_percentage"] / 100
                if coverage_pct < priority_threshold:
                    priority_categories.append(
                        {
                            "name": category_name,
                            "coverage": coverage_pct,
                            "missing_count": category_data["missing_count"],
                            "priority_score": (priority_threshold - coverage_pct)
                            * category_data["missing_count"],
                        }
                    )

                    # Find missing tags for this category
                    category_id = category_data["category_id"]
                    all_category_tags = self._tag_cache.get(category_id, [])
                    missing_category_tags = [
                        {
                            "tag_key": tag.tag_key,
                            "display_name": tag.display_name,
                            "description": tag.description,
                            "data_type": tag.data_type,
                            "is_required": tag.is_required,
                            "weight": tag.weight,
                            "questions": tag.question_templates or [],
                        }
                        for tag in all_category_tags
                        if tag.id not in extracted_tag_ids
                    ]
                    missing_tags[category_name] = missing_category_tags

            # Sort priority categories by priority score (highest first)
            priority_categories.sort(key=lambda x: x["priority_score"], reverse=True)

            result = {
                "priority_categories": priority_categories,
                "missing_tags": missing_tags,
                "total_missing": sum(len(tags) for tags in missing_tags.values()),
                "categories_below_threshold": len(priority_categories),
            }

            # Cache the result
            self._request_cache["gap_analysis"][cache_key] = result
            logger.info(
                f"Completed and cached gap analysis for assessment {cv_assessment_id}"
            )

            return result

        except Exception as e:
            logger.error(f"Failed to analyze gaps: {str(e)}")
            return {}

    async def add_tag_from_answer(
        self,
        cv_assessment_id: UUID,
        tag_key: str,
        value: str,
        category_name: Optional[str] = None,
        confidence_score: float = 0.8,
    ) -> Optional[CandidateTag]:
        """
        Add a tag extracted from a candidate's answer.

        Args:
            cv_assessment_id: The assessment ID
            tag_key: The tag key
            value: The extracted value
            category_name: Optional category name to narrow search
            confidence_score: Confidence in the extraction

        Returns:
            Created candidate tag or None
        """
        try:
            # Find tag definition
            tag_definition = None

            if category_name:
                # Search in specific category
                category = next(
                    (c for c in self._category_cache if c.name == category_name), None
                )
                if category:
                    tags = self._tag_cache.get(category.id, [])
                    tag_definition = next(
                        (t for t in tags if t.tag_key == tag_key), None
                    )
            else:
                # Search all categories
                for tags in self._tag_cache.values():
                    tag_definition = next(
                        (t for t in tags if t.tag_key == tag_key), None
                    )
                    if tag_definition:
                        break

            if not tag_definition:
                logger.warning(f"Tag definition not found for key: {tag_key}")
                return None

            # Format value based on data type
            formatted_value = self._format_tag_value(value, tag_definition.data_type)

            if formatted_value is None:
                return None

            # Calculate dynamic confidence score
            confidence_score = self._calculate_extraction_confidence(
                formatted_value, tag_definition, "", TagSource.QUESTION
            )

            # Create candidate tag
            candidate_tag_data = self.db_service.create_candidate_tag(
                str(cv_assessment_id),
                str(tag_definition.id),
                formatted_value,
                TagSource.QUESTION.value,
                confidence_score,
            )

            return CandidateTag(**candidate_tag_data) if candidate_tag_data else None

        except Exception as e:
            logger.error(f"Failed to add tag from answer: {str(e)}")
            return None

    async def extract_tags_from_answer(
        self,
        cv_assessment_id: UUID,
        answer_text: str,
        question_context: Optional[str] = None,
        refresh_cache: bool = False,
    ) -> Dict[str, Any]:
        """
        Extract tags from a candidate's answer using AI analysis.

        Args:
            cv_assessment_id: The assessment ID
            answer_text: The answer text to analyze
            question_context: Optional question context for better extraction
            refresh_cache: Whether to refresh the tag definition cache

        Returns:
            Dictionary with extraction results
        """
        try:
            # Ensure tag definitions are loaded
            await self._load_tag_definitions(refresh_cache)

            if not self._category_cache or not self._tag_cache:
                logger.warning(
                    "Tag definitions not loaded - skipping answer extraction"
                )
                return {"extracted_tags": [], "error": "Tag definitions not loaded"}

            logger.info(f"Extracting tags from answer: {answer_text[:100]}...")

            # Extract tags for each category
            extracted_tags = {}
            for category in self._category_cache:
                category_tags = await self._extract_category_tags_from_answer(
                    answer_text, category, question_context
                )
                if category_tags:
                    extracted_tags[category.name] = category_tags

            # Store extracted tags in database
            stored_tags = await self._store_extracted_tags(
                cv_assessment_id, extracted_tags
            )

            logger.info(f"Extracted and stored {len(stored_tags)} tags from answer")

            return {
                "extracted_tags": stored_tags,
                "extraction_complete": True,
            }

        except Exception as e:
            logger.error(f"Failed to extract tags from answer: {str(e)}")
            return {
                "extracted_tags": [],
                "extraction_complete": False,
                "error": str(e),
            }

    async def _extract_category_tags_from_answer(
        self,
        answer_text: str,
        category: TagCategory,
        question_context: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """Extract tags for a specific category from an answer using AI"""

        # Get tag definitions for this category
        tag_definitions = self._tag_cache.get(category.id, [])

        if not tag_definitions:
            logger.warning(f"No tag definitions found for category: {category.name}")
            return []

        # Build extraction prompt specifically for answer analysis
        tag_info = []
        for tag in tag_definitions:
            hints = ", ".join(tag.extraction_hints) if tag.extraction_hints else "none"
            tag_info.append(
                f"- {tag.tag_key} ({tag.display_name}): {tag.description or 'N/A'}\n"
                f"  Data type: {tag.data_type}, Hints: {hints}"
            )

        extraction_prompt = f"""
You are an expert at analyzing candidate answers to extract relevant information for the '{category.display_name}' category.

Category Description: {category.description}

Tags to extract:
{chr(10).join(tag_info)}

{f"Question Context: {question_context}" if question_context else ""}

Candidate's Answer:
{answer_text}

Instructions:
1. Extract values for each tag that can be inferred from the candidate's answer
2. Only extract information that is explicitly stated or clearly implied in the answer
3. For 'list' data types, return comma-separated values
4. For 'number' data types, extract numeric values only
5. For 'date' data types, extract dates in YYYY-MM-DD format
6. If information is not found in the answer, use null
7. Be conservative - only extract what you are confident about
8. Consider the question context to better understand the answer
9. If the answer doesn't contain relevant information for a tag, use null for that tag

Return a JSON object with tag_key as keys and extracted values. Example:
{{
    "programming_language": "Python",
    "years_experience": 3,
    "skill_level": "Intermediate"
}}

IMPORTANT: Only include tags that have actual values found in the answer. Use null for missing information.
"""

        try:
            logger.info(
                f"Extracting tags for category '{category.name}' from answer: {answer_text[:100]}..."
            )

            response = self.model.invoke(extraction_prompt)
            extracted_data = self._parse_extraction_response(response.content)

            # Convert to list of tag dictionaries
            extracted_tags = []
            for tag_def in tag_definitions:
                value = extracted_data.get(tag_def.tag_key)

                # Log extraction attempt
                if value is not None and value != "null":
                    logger.info(f"Extracted '{tag_def.tag_key}': '{value}' from answer")
                else:
                    logger.debug(
                        f"No value extracted for '{tag_def.tag_key}' from answer"
                    )

                if value is not None and value != "null":
                    # Validate and format based on data type
                    formatted_value = self._format_tag_value(value, tag_def.data_type)
                    if formatted_value is not None:
                        # Calculate dynamic confidence score
                        confidence_score = self._calculate_extraction_confidence(
                            formatted_value,
                            tag_def,
                            response.content,
                            TagSource.QUESTION,
                        )

                        extracted_tags.append(
                            {
                                "tag_definition_id": tag_def.id,
                                "tag_key": tag_def.tag_key,
                                "value": formatted_value,
                                "confidence_score": confidence_score,
                                "source": TagSource.QUESTION,
                            }
                        )
                        logger.info(
                            f"Successfully formatted tag '{tag_def.tag_key}' with value '{formatted_value}'"
                        )
                    else:
                        logger.warning(
                            f"Failed to format value '{value}' for tag '{tag_def.tag_key}'"
                        )
                else:
                    logger.debug(
                        f"Skipping tag '{tag_def.tag_key}' - no value extracted"
                    )

            logger.info(
                f"Extracted {len(extracted_tags)} tags from category '{category.name}'"
            )
            return extracted_tags

        except Exception as e:
            logger.error(
                f"Failed to extract tags for category {category.name}: {str(e)}"
            )
            return []

    async def extract_experience_level(self, cv_text: str) -> Optional[JobLevel]:
        """
        Extract experience level from CV text.

        Args:
            cv_text: The CV text to analyze

        Returns:
            Detected job level or None
        """
        cv_lower = cv_text.lower()

        # Define patterns for each level
        patterns = {
            JobLevel.ENTRY: [
                r"\b(0-2|zero to two|one|two)\s+years?\s+(?:of\s+)?experience\b",
                r"\b(fresh|recent)\s+graduate\b",
                r"\bintern(?:ship)?\b",
                r"\bjunior\b",
                r"\bentry\s+level\b",
            ],
            JobLevel.JUNIOR: [
                r"\b(2-4|two to four|three|four)\s+years?\s+(?:of\s+)?experience\b",
                r"\bjunior\s+(?!intern)\w+\b",
            ],
            JobLevel.MID: [
                r"\b(4-8|four to eight|five|six|seven|eight)\s+years?\s+(?:of\s+)?experience\b",
                r"\bmid-?(?:level|senior)\b",
                r"\bintermediate\b",
            ],
            JobLevel.SENIOR: [
                r"\b(8-15|eight to fifteen|nine|ten|eleven|twelve)\s+years?\s+(?:of\s+)?experience\b",
                r"\bsenior\b(?!\s+vice|\s+director)",
                r"\blead\b",
                r"\bprincipal\b",
            ],
            JobLevel.EXECUTIVE: [
                r"\b(?:15\+|15\s*\+|over\s+15|more\s+than\s+15)\s+years?\s+(?:of\s+)?experience\b",
                r"\b(?:vp|vice\s+president)\b",
                r"\bdirector\b",
                r"\bexecutive\b",
                r"\bc-?level\b",
                r"\bchief\b",
            ],
        }

        # Check patterns
        for level, level_patterns in patterns.items():
            for pattern in level_patterns:
                if re.search(pattern, cv_lower):
                    logger.info(f"Detected experience level: {level}")
                    return level

        # If no clear pattern, try to count years mentioned
        years_pattern = r"\b(\d+)\s+years?\s+(?:of\s+)?(?:experience|working)\b"
        years_matches = re.findall(years_pattern, cv_lower)

        if years_matches:
            max_years = max(int(y) for y in years_matches)
            if max_years <= 2:
                return JobLevel.ENTRY
            elif max_years <= 4:
                return JobLevel.JUNIOR
            elif max_years <= 8:
                return JobLevel.MID
            elif max_years <= 15:
                return JobLevel.SENIOR
            else:
                return JobLevel.EXECUTIVE

        # Default to mid if unclear
        return JobLevel.MID

    async def get_assessment_length(
        self,
        cv_assessment_id: UUID,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
    ) -> int:
        """
        Get the configured assessment length for an assessment.

        Args:
            cv_assessment_id: The assessment ID
            template_id: Optional template ID
            job_id: Optional job ID (overrides template)

        Returns:
            The number of questions allowed for the assessment
        """
        try:
            job_data = None

            # Get job configuration if job_id provided
            if job_id:
                job_data_list = self.db_service.get_jobs(limit=1)
                for job in job_data_list:
                    if str(job.get("id")) == str(job_id):
                        job_data = job
                        break

            # Use centralized configuration logic
            return AssessmentConfig.get_assessment_length_from_job(job_data or {})

        except Exception as e:
            logger.error(f"Failed to get assessment length: {str(e)}")
            return AssessmentConfig.get_default_assessment_length()

    async def calculate_question_efficiency(
        self, question_template: str, missing_tags: List[TagDefinition]
    ) -> float:
        """
        Calculate how efficient a question is at filling missing tags.

        Args:
            question_template: The question template text
            missing_tags: List of missing tag definitions

        Returns:
            Efficiency score (0-1) based on potential tag coverage
        """
        try:
            # Simple scoring based on how many missing tags this question could fill
            score = 0.0
            question_lower = question_template.lower()

            for tag in missing_tags:
                # Check if question matches tag keywords
                if tag.extraction_hints:
                    for hint in tag.extraction_hints:
                        if hint.lower() in question_lower:
                            score += float(tag.weight)
                            break

                # Check if tag display name is in question
                if tag.display_name.lower() in question_lower:
                    score += float(tag.weight) * 0.5

            # Normalize score
            max_possible_score = sum(float(tag.weight) for tag in missing_tags)
            if max_possible_score > 0:
                return min(score / max_possible_score, 1.0)

            return 0.0

        except Exception as e:
            logger.error(f"Failed to calculate question efficiency: {str(e)}")
            return 0.0

    async def select_optimal_questions(
        self,
        cv_assessment_id: UUID,
        assessment_length: int,
        coverage_thresholds: Optional[Dict[UUID, float]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Select optimal questions to maximize tag coverage within assessment length.

        Args:
            cv_assessment_id: The assessment ID
            assessment_length: Maximum number of questions allowed
            coverage_thresholds: Optional coverage thresholds per category

        Returns:
            List of optimal questions with metadata
        """
        try:
            # OPTIMIZATION: Use cached coverage data instead of recalculating
            cache_key = str(cv_assessment_id)
            if cache_key in self._request_cache["coverage_data"]:
                coverage = self._request_cache["coverage_data"][cache_key]
                logger.info(f"Using cached coverage data for question selection")
            else:
                # Fallback to calculation if not cached
                coverage = await self.calculate_coverage(
                    cv_assessment_id, coverage_thresholds
                )
                logger.info(
                    f"Coverage not cached, calculated fresh for question selection"
                )
            gap_analysis = await self.analyze_gaps(cv_assessment_id)

            if not gap_analysis or not gap_analysis.get("missing_tags"):
                return []

            # Collect all possible questions with their efficiency scores
            question_candidates = []

            for category_name, missing_tags_data in gap_analysis[
                "missing_tags"
            ].items():
                # Get category from cache
                category = next(
                    (c for c in self._category_cache if c.name == category_name), None
                )
                if not category:
                    continue

                # Get coverage data for this category
                coverage_data = coverage["categories"].get(category_name, {})
                coverage_gap = coverage_data.get(
                    "threshold_percentage", 70
                ) - coverage_data.get("coverage_percentage", 0)

                # Get missing tag definitions
                category_tags = self._tag_cache.get(category.id, [])
                missing_tag_keys = {tag["tag_key"] for tag in missing_tags_data}
                missing_tags = [
                    tag for tag in category_tags if tag.tag_key in missing_tag_keys
                ]

                # Generate questions for missing tags
                for tag in missing_tags:
                    if tag.question_templates:
                        for question_template in tag.question_templates:
                            # Calculate efficiency score
                            efficiency = await self.calculate_question_efficiency(
                                question_template, missing_tags
                            )

                            # Calculate priority based on category priority and coverage gap
                            priority_score = (
                                efficiency
                                * (category.priority / 10)
                                * (
                                    1 + coverage_gap / 100
                                )  # Boost priority for larger gaps
                            )

                            question_candidates.append(
                                {
                                    "question": question_template,
                                    "category": category_name,
                                    "category_display": category.display_name,
                                    "tag_key": tag.tag_key,
                                    "tag_display": tag.display_name,
                                    "efficiency_score": efficiency,
                                    "priority_score": priority_score,
                                    "is_required": tag.is_required,
                                    "potential_tags": [tag.tag_key]
                                    + [
                                        t.tag_key
                                        for t in missing_tags
                                        if any(
                                            hint.lower() in question_template.lower()
                                            for hint in t.extraction_hints
                                        )
                                    ],
                                }
                            )

            # Sort by priority (required tags first, then by priority score)
            question_candidates.sort(
                key=lambda x: (not x["is_required"], -x["priority_score"])
            )

            # Select top questions up to assessment length
            selected_questions = []
            selected_tags = set()

            for candidate in question_candidates:
                if len(selected_questions) >= assessment_length:
                    break

                # Check if this question adds new value
                new_tags = set(candidate["potential_tags"]) - selected_tags
                if new_tags or candidate["is_required"]:
                    selected_questions.append(candidate)
                    selected_tags.update(candidate["potential_tags"])

            # Always include salary question if not already covered
            salary_tags = {"salary_expectations", "current_salary"}
            if (
                not selected_tags.intersection(salary_tags)
                and len(selected_questions) < assessment_length
            ):
                # Add a salary question
                selected_questions.append(
                    {
                        "question": "What are your current and expected salary ranges?",
                        "category": "personal",
                        "category_display": "Personal",
                        "tag_key": "salary_expectations",
                        "tag_display": "Salary Expectations",
                        "efficiency_score": 1.0,
                        "priority_score": 1.0,
                        "is_required": True,
                        "potential_tags": ["salary_expectations", "current_salary"],
                    }
                )

            return selected_questions

        except Exception as e:
            logger.error(f"Failed to select optimal questions: {str(e)}")
            return []

    async def get_questions_for_assessment(
        self,
        cv_assessment_id: UUID,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
        custom_length: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Get optimized questions for an assessment based on CV gaps and constraints.

        Args:
            cv_assessment_id: The assessment ID
            template_id: Optional template ID
            job_id: Optional job ID
            custom_length: Optional custom assessment length override

        Returns:
            Dictionary with questions and metadata
        """
        try:
            # Determine assessment length
            assessment_length = custom_length
            if assessment_length is None:
                assessment_length = await self.get_assessment_length(
                    cv_assessment_id, template_id, job_id
                )

            # Get optimal questions
            questions = await self.select_optimal_questions(
                cv_assessment_id, assessment_length
            )

            # OPTIMIZATION: Use cached coverage data instead of recalculating
            cache_key = str(cv_assessment_id)
            if cache_key in self._request_cache["coverage_data"]:
                coverage = self._request_cache["coverage_data"][cache_key]
                logger.info(f"Using cached coverage data for questions metadata")
            else:
                # Fallback to calculation if not cached
                coverage = await self.calculate_coverage(cv_assessment_id)
                logger.info(
                    f"Coverage not cached, calculated fresh for questions metadata"
                )

            return {
                "questions": questions,
                "total_questions": len(questions),
                "assessment_length": assessment_length,
                "coverage_before": coverage["overall_coverage"],
                "estimated_coverage_after": min(
                    coverage["overall_coverage"]
                    + (len(questions) * 5),  # Rough estimate
                    100,
                ),
                "categories_targeted": len(set(q["category"] for q in questions)),
            }

        except Exception as e:
            logger.error(f"Failed to get questions for assessment: {str(e)}")
            return {
                "questions": [],
                "total_questions": 0,
                "assessment_length": AssessmentConfig.get_default_assessment_length(),
                "error": str(e),
            }

    async def get_coverage_thresholds(
        self, template_id: Optional[UUID] = None, job_id: Optional[UUID] = None
    ) -> Dict[UUID, float]:
        """
        Get coverage thresholds for categories from job or template configuration.

        Args:
            template_id: Optional template ID
            job_id: Optional job ID (overrides template)

        Returns:
            Dictionary mapping category IDs to threshold percentages
        """
        try:
            thresholds = {}

            # Get custom thresholds from database
            if job_id:
                # Check job-specific thresholds
                coverage_thresholds = (
                    self.db_service.supabase_client.table("coverage_thresholds")
                    .select("*")
                    .eq("job_id", str(job_id))
                    .execute()
                )

                if coverage_thresholds.data:
                    for threshold in coverage_thresholds.data:
                        cat_id = UUID(threshold["category_id"])
                        thresholds[cat_id] = float(threshold["required_percentage"])
                    return thresholds

            if template_id:
                # Check template-specific thresholds
                coverage_thresholds = (
                    self.db_service.supabase_client.table("coverage_thresholds")
                    .select("*")
                    .eq("template_id", str(template_id))
                    .execute()
                )

                if coverage_thresholds.data:
                    for threshold in coverage_thresholds.data:
                        cat_id = UUID(threshold["category_id"])
                        thresholds[cat_id] = float(threshold["required_percentage"])
                    return thresholds

            # Default to category default thresholds
            for category in self._category_cache:
                thresholds[category.id] = float(category.default_threshold * 100)

            return thresholds

        except Exception as e:
            logger.error(f"Failed to get coverage thresholds: {str(e)}")
            # Return default thresholds on error
            return {cat.id: 70.0 for cat in self._category_cache}

    async def check_assessment_completion(
        self,
        cv_assessment_id: UUID,
        questions_asked: int,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """
        Check if assessment should be completed based on dual stop conditions.

        Stop conditions:
        1. Question limit reached (from job/template/default)
        2. All categories reach their coverage thresholds

        Args:
            cv_assessment_id: The assessment ID
            questions_asked: Number of questions already asked
            template_id: Optional template ID
            job_id: Optional job ID

        Returns:
            Dictionary with completion status and reasons
        """
        try:
            # Get assessment length limit
            max_questions = await self.get_assessment_length(
                cv_assessment_id, template_id, job_id
            )

            # Check if question limit reached
            if questions_asked >= max_questions:
                return {
                    "should_complete": True,
                    "reason": "question_limit_reached",
                    "questions_asked": questions_asked,
                    "max_questions": max_questions,
                    "coverage_status": self._request_cache["coverage_data"].get(
                        str(cv_assessment_id), {}
                    ),
                }

            # Get coverage thresholds
            thresholds = await self.get_coverage_thresholds(template_id, job_id)

            # OPTIMIZATION: Use cached coverage data instead of recalculating
            cache_key = str(cv_assessment_id)
            if cache_key in self._request_cache["coverage_data"]:
                coverage = self._request_cache["coverage_data"][cache_key]
                logger.info(f"Using cached coverage data for completion check")
            else:
                # Fallback to calculation if not cached
                coverage = await self.calculate_coverage(cv_assessment_id, thresholds)
                logger.info(
                    f"Coverage not cached, calculated fresh for completion check"
                )

            # Check if all categories meet thresholds
            all_complete = all(
                cat_data["is_complete"] for cat_data in coverage["categories"].values()
            )

            if all_complete:
                return {
                    "should_complete": True,
                    "reason": "coverage_thresholds_met",
                    "questions_asked": questions_asked,
                    "max_questions": max_questions,
                    "coverage_status": coverage,
                }

            # Assessment should continue
            questions_remaining = max_questions - questions_asked
            incomplete_categories = [
                name
                for name, data in coverage["categories"].items()
                if not data["is_complete"]
            ]

            return {
                "should_complete": False,
                "reason": "continue_assessment",
                "questions_asked": questions_asked,
                "max_questions": max_questions,
                "questions_remaining": questions_remaining,
                "coverage_status": coverage,
                "incomplete_categories": incomplete_categories,
            }

        except Exception as e:
            logger.error(f"Failed to check assessment completion: {str(e)}")
            return {"should_complete": False, "reason": "error", "error": str(e)}

    async def update_coverage_thresholds(
        self,
        category_id: UUID,
        threshold_percentage: float,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
    ) -> bool:
        """
        Update or create coverage threshold for a category.

        Args:
            category_id: The category UUID
            threshold_percentage: Required coverage percentage (0-100)
            template_id: Optional template ID
            job_id: Optional job ID

        Returns:
            True if successful
        """
        try:
            data = {
                "category_id": str(category_id),
                "required_percentage": threshold_percentage,
                "is_mandatory": True,
            }

            if job_id:
                data["job_id"] = str(job_id)
            elif template_id:
                data["template_id"] = str(template_id)
            else:
                logger.error("Either job_id or template_id must be provided")
                return False

            # Insert or update
            result = (
                self.db_service.supabase_client.table("coverage_thresholds")
                .upsert(data)
                .execute()
            )

            return bool(result.data)

        except Exception as e:
            logger.error(f"Failed to update coverage threshold: {str(e)}")
            return False

    async def debug_tag_extraction(self, cv_assessment_id: UUID) -> Dict[str, Any]:
        """
        Debug tag extraction issues for a specific assessment.

        Args:
            cv_assessment_id: The assessment ID to debug

        Returns:
            Dictionary with debugging information
        """
        try:
            # Get all categories and tag definitions
            await self._load_tag_definitions()

            # Get existing tags
            existing_tags_data = self.db_service.get_candidate_tags(
                str(cv_assessment_id)
            )
            existing_tags = [CandidateTag(**tag) for tag in existing_tags_data]

            # Create lookup for existing tags
            existing_tag_lookup = {}
            for tag in existing_tags:
                if tag.tag_definition:
                    existing_tag_lookup[tag.tag_definition.tag_key] = {
                        "value": tag.value,
                        "source": tag.source,
                        "confidence": float(tag.confidence_score),
                        "category": tag.tag_definition.category_id,
                    }

            # Analyze each category
            debug_info = {
                "assessment_id": str(cv_assessment_id),
                "categories": {},
                "summary": {
                    "total_categories": len(self._category_cache),
                    "total_tag_definitions": sum(
                        len(tags) for tags in self._tag_cache.values()
                    ),
                    "total_extracted_tags": len(existing_tags),
                    "categories_with_tags": 0,
                    "categories_without_tags": 0,
                },
            }

            for category in self._category_cache:
                category_tags = self._tag_cache.get(category.id, [])
                category_extracted = []
                category_missing = []

                for tag_def in category_tags:
                    if tag_def.tag_key in existing_tag_lookup:
                        category_extracted.append(
                            {
                                "tag_key": tag_def.tag_key,
                                "display_name": tag_def.display_name,
                                "value": existing_tag_lookup[tag_def.tag_key]["value"],
                                "source": existing_tag_lookup[tag_def.tag_key][
                                    "source"
                                ],
                                "confidence": existing_tag_lookup[tag_def.tag_key][
                                    "confidence"
                                ],
                                "data_type": tag_def.data_type.value,
                                "is_required": tag_def.is_required,
                            }
                        )
                    else:
                        category_missing.append(
                            {
                                "tag_key": tag_def.tag_key,
                                "display_name": tag_def.display_name,
                                "data_type": tag_def.data_type.value,
                                "is_required": tag_def.is_required,
                                "extraction_hints": tag_def.extraction_hints,
                                "question_templates": tag_def.question_templates,
                            }
                        )

                debug_info["categories"][category.name] = {
                    "display_name": category.display_name,
                    "description": category.description,
                    "total_tags": len(category_tags),
                    "extracted_tags": len(category_extracted),
                    "missing_tags": len(category_missing),
                    "coverage_percentage": (
                        (len(category_extracted) / len(category_tags) * 100)
                        if category_tags
                        else 0
                    ),
                    "extracted": category_extracted,
                    "missing": category_missing,
                }

                if category_extracted:
                    debug_info["summary"]["categories_with_tags"] += 1
                else:
                    debug_info["summary"]["categories_without_tags"] += 1

            return debug_info

        except Exception as e:
            logger.error(f"Failed to debug tag extraction: {str(e)}")
            return {"error": str(e)}

    def _calculate_extraction_confidence(
        self,
        extracted_value: Any,
        tag_definition: TagDefinition,
        ai_response: str,
        source: TagSource,
    ) -> float:
        """
        Calculate a meaningful confidence score based on extraction quality.

        Args:
            extracted_value: The extracted value
            tag_definition: The tag definition
            ai_response: The raw AI response
            source: The extraction source

        Returns:
            Confidence score between 0.0 and 1.0
        """
        try:
            base_confidence = 0.5  # Start with neutral confidence

            # Source-based confidence adjustment
            if source == TagSource.CV:
                base_confidence += 0.3  # CV data is generally more reliable
            elif source == TagSource.QUESTION:
                base_confidence += 0.1  # Question data is less reliable
            elif source == TagSource.MANUAL:
                base_confidence += 0.4  # Manual input is most reliable
            elif source == TagSource.INFERENCE:
                base_confidence += 0.0  # Inference is least reliable

            # Value quality assessment
            if (
                extracted_value is None
                or extracted_value == ""
                or extracted_value == "null"
            ):
                return 0.0  # No confidence in null/empty values

            value_str = str(extracted_value).strip()

            # Check for vague or generic values
            vague_indicators = [
                "unknown",
                "n/a",
                "not specified",
                "not mentioned",
                "not available",
                "not provided",
                "none",
                "no information",
            ]
            if any(indicator in value_str.lower() for indicator in vague_indicators):
                base_confidence -= 0.3

            # Check for specific vs generic values
            if tag_definition.data_type == TagDataType.TEXT:
                # For text fields, longer, more specific values are better
                if len(value_str) > 20:
                    base_confidence += 0.1
                elif len(value_str) < 3:
                    base_confidence -= 0.1

            elif tag_definition.data_type == TagDataType.NUMBER:
                # For numbers, check if it's a reasonable value
                try:
                    num_value = float(value_str)
                    if 0 <= num_value <= 100:  # Reasonable range for most metrics
                        base_confidence += 0.1
                    else:
                        base_confidence -= 0.1
                except ValueError:
                    base_confidence -= 0.2

            elif tag_definition.data_type == TagDataType.LIST:
                # For lists, more items suggest better extraction
                items = [item.strip() for item in value_str.split(",") if item.strip()]
                if len(items) > 1:
                    base_confidence += 0.1
                elif len(items) == 0:
                    base_confidence -= 0.3

            elif tag_definition.data_type == TagDataType.DATE:
                # For dates, check if it's a reasonable format
                import re

                date_patterns = [
                    r"\d{4}-\d{2}-\d{2}",  # YYYY-MM-DD
                    r"\d{2}/\d{2}/\d{4}",  # MM/DD/YYYY
                    r"\d{4}",  # Just year
                ]
                if any(re.match(pattern, value_str) for pattern in date_patterns):
                    base_confidence += 0.1
                else:
                    base_confidence -= 0.1

            # AI response quality assessment
            if ai_response:
                # Check if AI provided a structured JSON response
                if "{" in ai_response and "}" in ai_response:
                    base_confidence += 0.1

                # Check for confidence indicators in AI response
                confidence_indicators = [
                    "confident",
                    "certain",
                    "clear",
                    "explicit",
                    "specifically",
                ]
                uncertainty_indicators = [
                    "maybe",
                    "possibly",
                    "perhaps",
                    "likely",
                    "probably",
                    "not sure",
                    "unclear",
                    "vague",
                    "approximate",
                ]

                response_lower = ai_response.lower()
                if any(
                    indicator in response_lower for indicator in confidence_indicators
                ):
                    base_confidence += 0.1
                if any(
                    indicator in response_lower for indicator in uncertainty_indicators
                ):
                    base_confidence -= 0.2

            # Tag-specific confidence adjustments
            if tag_definition.extraction_hints:
                # Check if the extracted value matches any extraction hints
                hints_lower = [hint.lower() for hint in tag_definition.extraction_hints]
                value_lower = value_str.lower()
                if any(
                    hint in value_lower or value_lower in hint for hint in hints_lower
                ):
                    base_confidence += 0.1

            # Clamp confidence to valid range
            return max(0.0, min(1.0, base_confidence))

        except Exception as e:
            logger.warning(
                f"Error calculating confidence for {tag_definition.tag_key}: {str(e)}"
            )
            return 0.5  # Return neutral confidence on error
