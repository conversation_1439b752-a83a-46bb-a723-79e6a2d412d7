# LeadersScout AI Chat Bot

A sophisticated AI-powered chat bot designed for executive search and talent intelligence, featuring CV assessment, lead capture, and intelligent question answering capabilities. Built with OpenAI's GPT-4, LangChain, Pinecone, and FastAPI, it provides a comprehensive solution for talent acquisition and client engagement.

## Core Features

### Lead Management

- 🎯 Intelligent lead capture with type classification (company or recruitment partner)
- 📊 Automated lead information storage in Supabase database
- 📧 Multi-channel notifications (Email, WhatsApp) for new leads
- 📈 Google Sheets integration for lead tracking and analysis

### CV Assessment

- 📄 PDF and Word document CV processing
- 🤖 Dynamic question generation based on CV content
- 💡 Adaptive assessment based on experience level
- 📊 Detailed scoring and assessment reports
- 🔄 Multi-turn assessment with context awareness

### Knowledge Base

- 🔍 RAG (Retrieval-Augmented Generation) using LangChain and Pinecone
- 🌐 Web search integration via Perplexity API for current market information
- 📚 Custom answers management through Google Sheets
- 🔄 Real-time knowledge base updates

### Multilingual Support

- 🌍 Full support for English and Arabic
- 🔄 Dynamic language switching during conversations
- ⚡ Optimized translation architecture (67% fewer API calls)
- 📝 Proper RTL text handling for Arabic

### User Experience

- 💬 Modern, responsive chat interface
- 📱 Mobile-optimized design
- 🔄 Session management with conversation history
- 💾 Persistent conversation storage

## Features

- 👨‍💼 Customer service representative named <PERSON><PERSON> (أمين in Arabic)
- 💬 Seamless chat interface that integrates with any WordPress site
- 🧠 Powered by OpenAI's GPT-4.1-mini for intelligent responses
- 🔍 Retrieval-Augmented Generation (RAG) using LangChain and Pinecone
- 🔒 API access restricted to your domain only (configurable)
- 💾 Persistent conversation storage using Supabase
- 🧩 Full conversation history retrieval for better context understanding
- 📊 Complete conversation history for future analysis
- 🔄 Easy refresh for starting new conversations
- 📱 Enhanced responsive design works on all devices (desktop, tablet, mobile)
- 🌐 Multilingual support with Arabic language detection and proper RTL text display
- 💬 Concise responses with option to provide more details
- 📝 Intelligent lead capture with agent-based architecture and database storage for lead information
- 🔄 Optimized translation architecture with agent-based internal translation
- 📧 Email notifications to sales team when new leads are captured
- 📱 WhatsApp follow-up messages to leads via Twilio
- 📊 Google Sheets integration for lead tracking
- 🔄 Improved error handling with exponential backoff retry functionality
- 🤔 LLM-based question detection for more accurate hybrid responses
- 🔀 Intelligent hybrid processing for messages that contain both questions and lead information
- 💬 Interactive responses that ask follow-up questions to engage users
- 🔄 Natural, integrated responses that combine information and lead capture seamlessly
- 📝 Custom answers management via Google Sheets with semantic matching
- 🔄 Real-time updates to custom answers through a refresh endpoint
- ⚡ Optimized translation architecture with 67% fewer API calls and significantly faster processing
- 🌐 Intelligent web search integration using Perplexity API for current information and competitor analysis
- 🎯 Domain-focused search filtering to provide relevant business information while excluding social media noise
- 🧪 Comprehensive test suite with 15 different test categories ensuring reliability (93% success rate)

## Tech Stack

- **Backend**: FastAPI (Python)
- **AI**: OpenAI GPT-4.1
- **RAG**: LangChain + Pinecone Vector Database
- **Web Search**: Perplexity API for current information and competitor analysis
- **Database**: Supabase (PostgreSQL)
- **Deployment**: VPS with Nginx and SSL
- **Frontend**: Vanilla JavaScript (no dependencies)
- **Integration**: WordPress-compatible HTML/JS snippet
- **Translation**: Agent-based internal translation with prompt instructions (no separate API calls)
- **Lead Capture**: LangChain tool-calling agents with database storage for information extraction
- **Email Notifications**: SendGrid for email delivery
- **WhatsApp Messaging**: Twilio API for WhatsApp follow-up messages
- **Lead Tracking**: Google Sheets API for spreadsheet integration
- **Custom Answers**: Embedding-based semantic matching with OpenAI embeddings

## Performance Optimization and Reliability

The chatbot includes several optimizations to reduce API costs, improve performance, and enhance reliability:

### Translation Architecture Optimization (Major Performance Improvement)

The translation system has been completely redesigned for optimal performance:

- **API Call Reduction**: **67% fewer OpenAI API calls** (reduced from 3 to 1 call per message)
- **Performance Improvement**: **Significantly faster response times** due to single agent call
- **Cost Efficiency**: Major reduction in OpenAI API usage costs
- **Enhanced Reliability**: Fewer API dependencies reduce points of failure
- **Better Context Preservation**: Agent sees original message with full context

**Optimized Translation Flow:**

- **Legacy Approach**: Translate input → Process → Translate output (3 API calls)
- **Optimized Approach**: Agent handles translation internally via prompts (1 API call)
- **Smart Processing**: Agent translates Arabic to English for RAG search, then responds in user's language
- **Context Awareness**: Agent maintains full conversation context for better translation quality

**Pure Agent Architecture:**

- **Legacy Approach**: Used multiple separate tools and complex routing logic
- **Pure Agent Approach**: Single intelligent agent with 2 specialized tools (`rag_search`, `save_lead`)
- **Unified Processing**: Agent handles lead detection, question answering, and response generation in one call
- **Context Awareness**: Agent maintains full conversation context for better decision making

### Additional Optimizations

- **Database-Based Language Preferences**: Language preferences are stored in the database instead of in-memory dictionaries
- **Reduced Vector Retrieval**: The RAG system retrieves 3 documents per query (reduced from 5) to decrease memory usage while maintaining answer quality
- **Direct Service Integration**: Services like Google Sheets are called directly rather than through background tasks for improved reliability
- **Centralized Service Management**: Services are initialized and managed through a dedicated service_init.py module
- **Efficient Database Queries**: Database queries are optimized to retrieve only necessary data
- **Comprehensive Error Handling**: All external service calls include robust error handling with retry logic
- **Detailed Logging**: Consistent logging throughout the application for better debugging and monitoring
- **Optimized Memory Usage**: Reduced memory footprint through efficient data structures and resource management

## Project Structure

```
├── api/
│   └── myapi.py                # Main FastAPI application with API endpoints
├── services/
│   ├── email_service.py        # Email notification service for new leads
│   ├── whatsapp_service.py     # WhatsApp messaging service for lead follow-up via Twilio
│   ├── google_sheets_service.py # Google Sheets integration for lead tracking
│   ├── custom_answers_service.py # Custom answers management with semantic matching
│   ├── memory_service.py       # Memory management for conversation history
│   ├── service_init.py         # Service initialization and management
│   ├── rag_service.py          # RAG functionality (English-only processing)
│   ├── perplexity_service.py   # Web search functionality using Perplexity API
│   ├── pinecone_service.py     # Pinecone vector database service
│   ├── lead_service.py         # Lead database operations
│   └── language_service.py     # Language detection and database management
├── database/
│   ├── database_service.py     # Database operations
│   ├── models.py              # Shared data models
│   └── create_database.sql    # Database schema
├── utils/
│   ├── message_processor.py    # Message processing with optimized translation flow
│   ├── background_tasks.py     # Background task management
│   ├── error_handling.py       # Error handling utilities with retry logic
│   └── utils.py               # Utility functions used across the application
├── scripts/
│   ├── embed_documents.py      # Script to embed documents from WordPress into Pinecone
│   └── clear_pinecone.py       # Script to manage vectors in Pinecone
├── static/
│   ├── footer.html            # Footer template
│   └── loading.html           # Loading screen template
├── templates/
│   ├── cv_assessment_detail.html
│   └── cv_assessments_list.html
├── tests/
│   └── test_chatbot.py        # Comprehensive test suite
├── agents/                    # Multi-Agent Architecture
│   ├── __init__.py           # Agent exports and backward compatibility
│   ├── coordinator_agent/    # Request routing and agent coordination
│   │   ├── __init__.py
│   │   ├── coordinator_agent.py
│   │   └── tools/
│   │       ├── __init__.py
│   │       └── agent_selection_tool.py
│   ├── cv_assessment_agent/  # CV analysis and assessment
│   │   ├── __init__.py
│   │   ├── cv_assessment_agent.py
│   │   └── tools/
│   │       └── __init__.py
│   └── conversation_agent/   # General chat and lead capture
│       ├── __init__.py
│       ├── conversation_agent.py
│       └── tools/
│           ├── __init__.py
│           ├── rag_search_tool.py
│           ├── save_lead_tool.py
│           └── web_search_tool.py
├── Pipfile                   # Python package dependencies
└── README.md                 # Project documentation
```

The codebase is organized into logical modules:

- **API Layer**: FastAPI application and endpoints
- **Services**: Core business logic and external service integrations
- **Database**: Data models and database operations
- **Utils**: Shared utility functions and helpers
- **Scripts**: Standalone maintenance scripts
- **Static**: HTML templates and assets
- **Templates**: Jinja2 templates for web views
- **Tests**: Comprehensive test suite
- **Agents**: Multi-Agent Architecture with specialized agents:
  - **Coordinator Agent**: Routes requests to appropriate specialized agents
  - **CV Assessment Agent**: Handles all CV-related operations (upload, analysis, scoring)
  - **Conversation Agent**: Manages general chat interactions and lead capture
- **Tools**: Agent-specific tools organized within each agent's directory

## Setup Instructions

1. Clone the repository:

```bash
git clone https://github.com/yourusername/leadersscout-ai-chat-bot.git
cd leadersscout-ai-chat-bot
```

2. Install dependencies:

```bash
# Using pipenv (recommended)
pipenv install
```

3. Create a `.env` file with required credentials:

```env
# Core API Keys
OPENAI_API_KEY=your_openai_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_env
PINECONE_INDEX_NAME=your_index_name
PERPLEXITY_API_KEY=your_perplexity_key

# Domain Configuration
HOST_DOMAIN=your_domain.com
HOST_DOMAIN_ARABIC=your_arabic_domain_name

# Email Configuration
SENDGRID_API_KEY=your_sendgrid_key
SALES_TEAM_EMAIL=<EMAIL>
EMAIL_FROM_ADDRESS=<EMAIL>

# WhatsApp Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_WHATSAPP_NUMBER=your_whatsapp_number

# Google Sheets
GOOGLE_SHEETS_ID=your_sheet_id
GOOGLE_SHEETS_CREDENTIALS=your_credentials_json

# Custom Answers Configuration
CUSTOM_ANSWERS_REFRESH_URL=your_refresh_url
```

4. Initialize the database:

```bash
# Run the SQL script in your Supabase project
cat database/create_database.sql | psql your_database_url
```

5. Start the development server:

```bash
uvicorn myapi:app --reload
```

## Testing

Run the comprehensive test suite:

```bash
python -m pytest tests/test_chatbot.py
```

The test suite covers:

- Lead capture functionality
- CV assessment process
- RAG and web search integration
- Multilingual support
- Error handling
- Session management
- Database operations

## Deployment

### A) Clone & Prepare the Application

1. SSH into your VPS and clone the repository:

```bash
cd /root
git clone https://github.com/yourusername/leadersscout-ai-chat-bot.git
cd leadersscout-ai-chat-bot
```

2. Install dependencies using pipenv:

```bash
# Install pipenv if not already installed
pip install pipenv

# Install project dependencies
pipenv install
```

3. Test the application manually:

```bash
pipenv run uvicorn myapi:app --host 0.0.0.0 --port 8000
```

Visit http://your-server-ip:8000/ to verify the application responds, then press CTRL+C to stop.

### B) Create systemd Service

1. Create a systemd service file:

```bash
nano /etc/systemd/system/leadersscout.service
```

2. Add the following configuration (adjust paths as needed):

```ini
[Unit]
Description=LeadersScout AI Chat Bot
After=network.target

[Service]
User=root
WorkingDirectory=/root/leadersscout-ai-chat-bot
ExecStart=/root/.local/bin/pipenv run uvicorn myapi:app --host 0.0.0.0 --port 8000
Restart=always
Environment=PATH=/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

[Install]
WantedBy=multi-user.target
```

3. Enable and start the service:

```bash
systemctl daemon-reload
systemctl enable leadersscout
systemctl start leadersscout
systemctl status leadersscout
```

### C) Configure Domain

1. Set up your domain (e.g., api.leadersscout.com) by adding an A record pointing to your server's IP.

2. Verify DNS propagation:

```bash
dig api.leadersscout.com +short
# Should return your server's IP
```

### D) Configure Nginx

1. Create Nginx configuration:

```bash
nano /etc/nginx/sites-available/leadersscout
```

2. Add the following configuration:

```nginx
server {
    listen 80;
    server_name api.leadersscout.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
    }
}
```

3. Enable the site and reload Nginx:

```bash
ln -s /etc/nginx/sites-available/leadersscout /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

4. Test HTTP access:

```bash
curl -I http://api.leadersscout.com/
# Should return HTTP/1.1 200 OK
```

### E) SSL Configuration

1. Install and run Certbot:

```bash
apt install certbot python3-certbot-nginx
certbot --nginx -d api.leadersscout.com
```

2. Follow the prompts:

   - Enter your email address
   - Agree to terms
   - Choose whether to redirect HTTP to HTTPS (recommended)

3. Verify HTTPS access:

```bash
curl -I https://api.leadersscout.com/
# Should return HTTP/1.1 200 OK
```

### F) Environment Setup

1. Create and edit the .env file:

```bash
nano /root/leadersscout-ai-chat-bot/.env
```

2. Add all required environment variables (see Setup Instructions section).

3. Restart the service to apply changes:

```bash
systemctl restart leadersscout
```

### G) Monitoring and Maintenance

1. View application logs:

```bash
journalctl -u leadersscout -f
```

2. Monitor system resources:

```bash
htop
```

3. Set up log rotation:

```bash
nano /etc/logrotate.d/leadersscout
```

Add:

```
/var/log/leadersscout/*.log {
    daily
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data adm
    sharedscripts
    postrotate
        systemctl reload leadersscout
    endscript
}
```

### H) Security Considerations

1. Configure UFW firewall:

```bash
ufw allow ssh
ufw allow http
ufw allow https
ufw enable
```

2. Set up fail2ban:

```bash
apt install fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

3. Regular system updates:

```bash
apt update
apt upgrade
```

## Integration

Add the chat widget to your website by including this snippet before the closing `</body>` tag:

```html
<script src="https://your-domain.com/static/chat-widget.js"></script>
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is proprietary software. All rights reserved.
