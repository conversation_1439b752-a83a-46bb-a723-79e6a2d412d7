import os
import logging
import pinecone
from dotenv import load_dotenv

# Import functions from other scripts
from scripts.clear_pinecone import clear_index
from scripts.embed_documents import (
    setup_pinecone,
    crawl_website,
    load_from_url,
    process_documents,
    embed_and_store,
    HOST_URL,  # Assuming HOST_URL is defined in embed_documents.py
)
from langchain_core.documents import Document


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("pinecone_service")

# Load environment variables
load_dotenv()

PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")


async def refresh_pinecone_index(crawl: bool = True, max_pages: int = None) -> bool:
    """
    Refresh the Pinecone index by clearing it and re-embedding documents.

    Args:
        crawl: Whether to crawl the website (default: True)
        max_pages: Maximum number of pages to crawl (default: None)

    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info("Starting Pinecone index refresh...")

        # Initialize Pinecone
        pc = setup_pinecone()
        if not pc:
            logger.error("Failed to setup Pinecone.")
            return False

        # Clear the Pinecone index
        logger.info(f"Clearing Pinecone index: {PINECONE_INDEX_NAME}")
        if not clear_index(PINECONE_INDEX_NAME, confirm=True):
            logger.error(f"Failed to clear Pinecone index: {PINECONE_INDEX_NAME}")
            return False
        logger.info(f"Successfully cleared Pinecone index: {PINECONE_INDEX_NAME}")

        # Load documents
        all_documents = []
        if crawl:
            logger.info(f"Crawling website starting from {HOST_URL}")
            urls = crawl_website(HOST_URL, max_pages=max_pages)
            for url in urls:
                docs = load_from_url(url)
                all_documents.extend(docs)
                logger.info(f"Loaded {len(docs)} documents from {url}")
        else:
            # Load from the single HOST_URL if not crawling
            # This part might need adjustment based on how embed_documents.py handles single URLs
            logger.info(f"Loading documents from single URL: {HOST_URL}")
            all_documents = load_from_url(HOST_URL)

        if not all_documents:
            logger.warning("No documents were loaded. Index will be empty.")
            # Still return True as clearing was successful and no error occurred during loading.
            # Or, decide if this should be an error.
            # For now, proceeding to embed what we have (which is nothing).
        else:
            logger.info(f"Total documents loaded: {len(all_documents)}")

            # Add a special document with contact information (mimicking embed_documents.py)
            # Ensure HOST_DOMAIN is accessible here or passed appropriately
            HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
            if HOST_DOMAIN:
                contact_doc = Document(
                    page_content=f"""
    {HOST_DOMAIN.capitalize()} Contact Information:
    Email: <EMAIL>

    If you need to get in touch with {HOST_DOMAIN.capitalize()}, please use the email address above or visit our contact page.
                    """.strip(),
                    metadata={
                        "source": "hardcoded_contact_info",
                        "title": f"{HOST_DOMAIN.capitalize()} Contact Information",
                        "page_type": "contact_info",
                        "category": "contact",
                        "has_full_content": True,
                        "is_contact_info": True,
                    },
                )
                all_documents.append(contact_doc)
                logger.info("Added special contact information document")
            else:
                logger.warning(
                    "HOST_DOMAIN not found, skipping contact information document."
                )

        # Process documents into chunks
        chunks = process_documents(all_documents)

        # Embed and store in Pinecone
        embed_and_store(chunks, pc)

        logger.info("Pinecone index refresh completed successfully!")
        return True

    except Exception as e:
        logger.error(f"Error refreshing Pinecone index: {str(e)}")
        return False


if __name__ == "__main__":
    # Example usage (no longer directly callable with async without an event loop runner):
    # import asyncio
    # asyncio.run(refresh_pinecone_index(crawl=True, max_pages=10))
    pass
