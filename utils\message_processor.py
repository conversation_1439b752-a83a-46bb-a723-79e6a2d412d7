"""Message Processor Module - Handles message processing and response generation"""

import logging

# Import the new agent-based implementation
from agents import CoordinatorAgent
from services import language_service
from database import database_service, DatabaseService
from utils.utils import is_arabic

logger = logging.getLogger("message_processor")


def _setup_conversation(supabase, session_id: str, host_name: str):
    """Setup or update the conversation in the database"""
    # Create or update conversation
    database_service.create_or_update_conversation(
        supabase, session_id, host_name, "English"
    )


def process_message(
    message_content: str,
    session_id: str,
    supabase,
    host_name: str = None,
    host_domain: str = None,
) -> str:
    """Process a message and generate a response"""
    # Setup the conversation
    _setup_conversation(supabase, session_id, host_name)

    # Handle language detection for database tracking only
    previous_language = language_service.get_language_preference(supabase, session_id)
    current_language = language_service.detect_user_language(
        message_content, previous_language
    )

    # Update language preference if changed
    if current_language != previous_language:
        language_service.update_language_preference(
            supabase, session_id, current_language
        )
        logger.info(f"Updated language from {previous_language} to {current_language}")

    # Add user message to database
    database_service.add_user_message(supabase, session_id, message_content)

    # Create database service wrapper
    db_service = DatabaseService(supabase)

    # Initialize coordinator agent with memory (memory loads history but doesn't save)
    coordinator_agent = CoordinatorAgent(
        host_domain=host_domain,
        session_id=session_id,
        supabase_client=supabase,
        db_service=db_service,
    )

    # Log the language detection state
    logger.info(
        f"Session: {session_id}, Message: '{message_content}', "
        f"Is Arabic: {is_arabic(message_content)}, Language: {current_language}"
    )

    # Agent handles everything including translation internally
    ai_response = coordinator_agent.process_message(message=message_content)

    # Add assistant response to history
    database_service.add_assistant_message(supabase, session_id, ai_response)
    logger.info(f"Response length: {len(ai_response.split())} words")

    return ai_response
