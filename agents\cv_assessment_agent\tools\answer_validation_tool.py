"""
Answer Validation Tool

A pure AI-driven tool for validating candidate answers during interviews.
All validation logic is handled by AI, no hardcoded rules.
"""

import logging
import json
import re
from typing import Dict, Any, Optional
from uuid import UUID

from langchain_openai import ChatOpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("answer_validation_tool")


class AnswerValidationTool:
    """Tool for validating candidate answers using pure AI logic"""

    def __init__(self, model_name: str = "gpt-4.1"):
        """Initialize the answer validation tool"""
        self.model = ChatOpenAI(model=model_name, temperature=0.2, max_tokens=2000)

    async def validate_answer(
        self,
        question: str,
        answer: str,
        question_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Validate a candidate's answer using AI only.

        Args:
            question: The question that was asked
            answer: The candidate's answer
            question_context: Optional context about the question

        Returns:
            Dictionary with validation results
        """
        try:
            logger.info(
                f"Validating answer: '{answer[:50]}...' for question: '{question[:50]}...'"
            )

            # Clean and normalize the answer
            clean_answer = self._clean_answer(answer)

            # Perform AI-based validation
            ai_validation = await self._ai_validate_answer(
                question, clean_answer, question_context
            )

            logger.info(
                f"Validation result: valid={ai_validation['is_valid']}, confidence={ai_validation['confidence_score']}"
            )
            return ai_validation

        except Exception as e:
            logger.error(f"Error validating answer: {str(e)}")
            return {
                "is_valid": False,
                "confidence_score": 0.0,
                "feedback": f"Validation error: {str(e)}",
                "needs_follow_up": True,
                "is_confusion": False,
                "validation_complete": False,
                "error": str(e),
            }

    def _clean_answer(self, answer: str) -> str:
        """Clean and normalize the answer text"""
        if not answer:
            return ""

        # Remove extra whitespace and normalize
        clean = re.sub(r"\s+", " ", answer.strip())
        return clean

    async def _ai_validate_answer(
        self,
        question: str,
        answer: str,
        question_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Use AI to validate the answer quality and relevance"""

        category = (
            question_context.get("category", "general")
            if question_context
            else "general"
        )

        prompt = f"""You are an expert interviewer evaluating a candidate's answer during a job interview.

QUESTION: {question}
CATEGORY: {category}
CANDIDATE'S ANSWER: {answer}

Please evaluate this answer comprehensively and respond with ONLY a JSON object:

{{
    "is_valid": true/false,
    "confidence_score": 0.0-1.0,
    "feedback": "brief explanation of your evaluation",
    "needs_follow_up": true/false,
    "is_confusion": true/false,
    "follow_up_reason": "reason for follow-up if needed",
    "relevance_score": 0.0-1.0,
    "completeness_score": 0.0-1.0,
    "validation_complete": true
}}

EVALUATION CRITERIA:
1. RELEVANCE: Does the answer directly address what was asked?
2. COMPLETENESS: Does it provide sufficient information for assessment?
3. CLARITY: Is the answer clear and understandable?
4. USEFULNESS: Does it help evaluate the candidate's qualifications?

SPECIAL CASES TO DETECT:
- CONFUSION: If the candidate indicates they don't understand the question, set "is_confusion": true
- MINIMAL ANSWERS: Very brief answers that lack substance (but may still be valid)
- OFF-TOPIC: Answers that don't relate to the question at all
- TECHNICAL ANSWERS: Be generous with technical responses that demonstrate knowledge

VALIDATION GUIDELINES:
- Mark as INVALID only if the answer is completely irrelevant, nonsensical, or empty
- Mark as CONFUSION if the candidate explicitly indicates they don't understand
- Mark as needing follow-up if the answer is relevant but incomplete or could be expanded
- Consider the context - some questions naturally have brief but valid answers
- Technical terms and frameworks mentioned are usually valid even if brief
- Be encouraging and supportive in your feedback"""

        try:
            response = self.model.invoke(prompt)

            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response.content, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())

                # Ensure all required fields with defaults
                return {
                    "is_valid": result.get("is_valid", True),
                    "confidence_score": float(result.get("confidence_score", 0.5)),
                    "feedback": result.get("feedback", ""),
                    "needs_follow_up": result.get("needs_follow_up", False),
                    "is_confusion": result.get("is_confusion", False),
                    "follow_up_reason": result.get("follow_up_reason", ""),
                    "relevance_score": float(result.get("relevance_score", 0.5)),
                    "completeness_score": float(result.get("completeness_score", 0.5)),
                    "validation_complete": True,
                }
            else:
                # Fallback if JSON parsing fails - be generous
                content_lower = response.content.lower()

                # Look for clear negative indicators
                is_invalid = any(
                    phrase in content_lower
                    for phrase in [
                        "completely irrelevant",
                        "makes no sense",
                        "not related",
                        "doesn't answer",
                        "off-topic",
                        "invalid",
                    ]
                )

                is_confusion = any(
                    phrase in content_lower
                    for phrase in [
                        "confusion",
                        "don't understand",
                        "doesn't understand",
                        "unclear",
                    ]
                )

                return {
                    "is_valid": not is_invalid,
                    "confidence_score": 0.6,
                    "feedback": "AI validation completed with fallback parsing",
                    "needs_follow_up": "follow" in content_lower
                    or "more" in content_lower,
                    "is_confusion": is_confusion,
                    "follow_up_reason": (
                        "Could benefit from more detail" if not is_invalid else ""
                    ),
                    "relevance_score": 0.6 if not is_invalid else 0.2,
                    "completeness_score": 0.6,
                    "validation_complete": True,
                }

        except Exception as e:
            logger.error(f"AI validation failed: {str(e)}")
            # Default to accepting answer with low confidence when AI fails
            return {
                "is_valid": True,
                "confidence_score": 0.4,
                "feedback": f"AI validation error: {str(e)}",
                "needs_follow_up": False,
                "is_confusion": False,
                "follow_up_reason": "",
                "relevance_score": 0.4,
                "completeness_score": 0.4,
                "validation_complete": False,
            }

    async def generate_follow_up_question(
        self, original_question: str, answer: str, validation_result: Dict[str, Any]
    ) -> Optional[str]:
        """Generate a follow-up question based on validation results"""

        if not validation_result.get("needs_follow_up", False):
            return None

        try:
            is_confusion = validation_result.get("is_confusion", False)
            follow_up_reason = validation_result.get("follow_up_reason", "")

            if is_confusion:
                # Rephrase the original question for clarity
                prompt = f"""The candidate didn't understand this interview question. Please rephrase it to be clearer and easier to understand.

ORIGINAL QUESTION: {original_question}
CANDIDATE'S RESPONSE: {answer}

Please provide a clearer, simpler version of the SAME question that:
- Uses simpler, more conversational language
- Provides brief context or examples if helpful
- Maintains the same intent and purpose
- Is encouraging and supportive in tone
- Avoids jargon or complex terminology

Return only the rephrased question, nothing else."""
            else:
                # Generate follow-up for incomplete or unclear answers
                prompt = f"""Generate a helpful follow-up question to get more information from the candidate.

ORIGINAL QUESTION: {original_question}
CANDIDATE'S ANSWER: {answer}
REASON FOR FOLLOW-UP: {follow_up_reason}

Create a polite, encouraging follow-up question that:
- Builds on their existing answer positively
- Asks for more specific details or examples
- Uses a conversational, supportive tone
- Helps them provide more complete information
- Shows you're interested in learning more

Return only the follow-up question, nothing else."""

            response = self.model.invoke(prompt)
            follow_up = response.content.strip()

            # Clean up the response
            if follow_up.startswith('"') and follow_up.endswith('"'):
                follow_up = follow_up[1:-1]

            return follow_up

        except Exception as e:
            logger.error(f"Error generating follow-up question: {str(e)}")
            return None

    def is_answer_relevant_to_question(self, question: str, answer: str) -> bool:
        """
        Quick relevance check using simple keyword matching.
        This is a lightweight backup method, main validation should use AI.
        """
        if not answer or len(answer.strip()) < 2:
            return False

        # Extract key terms from question
        question_words = set(re.findall(r"\b\w+\b", question.lower()))
        answer_words = set(re.findall(r"\b\w+\b", answer.lower()))

        # Remove common words
        common_words = {
            "the",
            "a",
            "an",
            "and",
            "or",
            "but",
            "in",
            "on",
            "at",
            "to",
            "for",
            "of",
            "with",
            "by",
            "is",
            "are",
            "was",
            "were",
            "be",
            "been",
            "have",
            "has",
            "had",
            "do",
            "does",
            "did",
            "will",
            "would",
            "could",
            "should",
            "may",
            "might",
            "can",
            "you",
            "your",
            "i",
            "my",
            "me",
            "we",
            "our",
            "they",
            "their",
            "them",
            "this",
            "that",
            "these",
            "those",
        }

        question_keywords = question_words - common_words
        answer_keywords = answer_words - common_words

        # Check for overlap
        overlap = len(question_keywords & answer_keywords)
        relevance_ratio = overlap / max(len(question_keywords), 1)

        return relevance_ratio > 0.1  # At least 10% keyword overlap
