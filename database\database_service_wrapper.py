"""
Database Service Wrapper Module

Provides a class-based interface to the database service functions.
This wrapper is used to pass database functionality to agents and services.
"""

from typing import List, Dict, Any, Optional
from . import database_service


class DatabaseService:
    """Wrapper class to provide database functionality to agents and services"""

    def __init__(self, supabase_client):
        """Initialize with a Supabase client"""
        self.supabase_client = supabase_client

    # Tag Category Methods
    def get_all_tag_categories(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all tag categories"""
        return database_service.get_all_tag_categories(
            self.supabase_client, active_only
        )

    def get_tag_definitions_by_category(self, category_id: str) -> List[Dict[str, Any]]:
        """Get tag definitions for a category"""
        return database_service.get_tag_definitions_by_category(
            self.supabase_client, category_id
        )

    # Candidate Tag Methods
    def create_candidate_tag(
        self,
        cv_assessment_id: str,
        tag_definition_id: str,
        value: str,
        source: str,
        confidence_score: float = 1.0,
    ) -> Optional[Dict[str, Any]]:
        """Create a candidate tag"""
        return database_service.create_candidate_tag(
            self.supabase_client,
            cv_assessment_id,
            tag_definition_id,
            value,
            source,
            confidence_score,
        )

    def batch_create_candidate_tags(
        self, tags_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """OPTIMIZED: Create multiple candidate tags in a single batch operation"""
        return database_service.batch_create_candidate_tags(
            self.supabase_client, tags_data
        )

    def batch_update_tag_coverage(self, coverage_updates: List[Dict[str, Any]]) -> bool:
        """OPTIMIZED: Update multiple tag coverage records in a single batch operation"""
        return database_service.batch_update_tag_coverage(
            self.supabase_client, coverage_updates
        )

    def get_candidate_tags(self, cv_assessment_id: str) -> List[Dict[str, Any]]:
        """Get candidate tags for an assessment"""
        return database_service.get_candidate_tags(
            self.supabase_client, cv_assessment_id
        )

    def update_tag_coverage(
        self,
        cv_assessment_id: str,
        category_id: str,
        total_tags: int,
        filled_tags: int,
        threshold_percentage: float = 70.0,
    ) -> bool:
        """Update tag coverage for a category"""
        return database_service.update_tag_coverage(
            self.supabase_client,
            cv_assessment_id,
            category_id,
            total_tags,
            filled_tags,
            threshold_percentage,
        )

    # Scoring Template Methods
    def get_scoring_templates(
        self,
        active_only: bool = True,
        industry: str = None,
        job_level: str = None,
    ) -> List[Dict[str, Any]]:
        """Get scoring templates"""
        return database_service.get_scoring_templates(
            self.supabase_client, active_only, industry, job_level
        )

    def create_scoring_template(
        self, template_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Create a scoring template"""
        return database_service.create_scoring_template(
            self.supabase_client, template_data
        )

    def update_scoring_template(
        self, template_id: str, update_data: Dict[str, Any]
    ) -> bool:
        """Update a scoring template"""
        return database_service.update_scoring_template(
            self.supabase_client, template_id, update_data
        )

    def get_scoring_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific scoring template by ID"""
        templates = database_service.get_scoring_templates(
            self.supabase_client, active_only=False
        )
        for template in templates:
            if template.get("id") == template_id:
                return template
        return None

    def get_default_scoring_template(self) -> Optional[Dict[str, Any]]:
        """Get the default scoring template"""
        return database_service.get_default_scoring_template(self.supabase_client)

    # Job Methods
    def get_jobs(
        self,
        active_only: bool = True,
        recruiter_id: str = None,
        limit: int = None,
    ) -> List[Dict[str, Any]]:
        """Get job postings"""
        return database_service.get_jobs(
            self.supabase_client, active_only, recruiter_id, limit
        )

    def create_job(self, job_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a job posting"""
        return database_service.create_job(self.supabase_client, job_data)

    def get_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific job posting by ID"""
        jobs = database_service.get_jobs(self.supabase_client, active_only=False)
        for job in jobs:
            if job.get("id") == job_id:
                return job
        return None

    def update_job(self, job_id: str, update_data: Dict[str, Any]) -> bool:
        """Update a job posting"""
        return database_service.update_job(self.supabase_client, job_id, update_data)

    # CV Assessment Methods
    def get_cv_assessment_by_id(self, assessment_id: int) -> Optional[Dict[str, Any]]:
        """Get CV assessment by ID"""
        return database_service.get_cv_assessment_by_id(
            self.supabase_client, assessment_id
        )

    def get_all_cv_assessments(self) -> List[Dict[str, Any]]:
        """Get all CV assessments"""
        return database_service.get_all_cv_assessments(self.supabase_client)

    # Candidate Score Methods
    def get_candidate_score_by_assessment_id(
        self, cv_assessment_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get candidate score by assessment ID"""
        return database_service.get_candidate_score(
            self.supabase_client, cv_assessment_id
        )

    def create_or_update_candidate_score(
        self, cv_assessment_id: str, score_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Create or update candidate score"""
        return database_service.create_or_update_candidate_score(
            self.supabase_client, cv_assessment_id, score_data
        )
