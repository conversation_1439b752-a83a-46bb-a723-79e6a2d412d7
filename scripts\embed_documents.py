#!/usr/bin/env python
"""
Document Embedding Script for WordPress Content

This script loads content from a WordPress site, processes it into chunks,
creates embeddings using OpenAI, and stores them in a Pinecone vector database
for retrieval in a RAG system.

Usage:
    # Crawl an entire website and embed all discovered pages
    python embed_documents.py --url https://example.com --crawl

    # Crawl a website but limit the number of pages processed
    python embed_documents.py --url https://example.com --crawl --max-pages 20

    # Embed content from a single URL only (no crawling)
    python embed_documents.py --url https://example.com

    # Embed content from a specific page
    python embed_documents.py --url https://example.com/about

    # Embed content from a local HTML or text file
    python embed_documents.py --file content.html

Command Options:
    --url URL          URL of the website or specific page to embed
    --file PATH        Path to a local HTML or text file to embed
    --crawl            Enable web crawling to discover and embed all pages
    --max-pages N      Limit crawling to a maximum of N pages (default: unlimited)
"""

# =============================================================================
# IMPORTS AND SETUP
# =============================================================================

import os
import re
import argparse
import time
from datetime import datetime
from urllib.parse import urlparse, urljoin
from typing import List
from dotenv import load_dotenv
from bs4 import BeautifulSoup
import requests
from langchain_openai import OpenAIEmbeddings
from langchain_community.document_loaders import WebBaseLoader, TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
import pinecone

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Load environment variables
load_dotenv()

# Get API keys from environment
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")

# Get the host domain from environment variables (required)
HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
if not HOST_DOMAIN:
    raise ValueError("HOST_DOMAIN environment variable must be set")
HOST_URL = f"https://{HOST_DOMAIN}"

# =============================================================================
# PINECONE SETUP AND MANAGEMENT
# =============================================================================


def setup_pinecone():
    """Initialize Pinecone and create index if it doesn't exist."""
    # Initialize Pinecone
    pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)

    # Check if index exists
    existing_indexes = [index.name for index in pc.list_indexes()]

    if PINECONE_INDEX_NAME not in existing_indexes:
        print(f"Creating new Pinecone index: {PINECONE_INDEX_NAME}")
        # Create a new index with appropriate settings for text embeddings
        pc.create_index(
            name=PINECONE_INDEX_NAME,
            dimension=1536,  # OpenAI embeddings dimension
            metric="cosine",
        )
        print(f"Index {PINECONE_INDEX_NAME} created successfully")
    else:
        print(f"Using existing Pinecone index: {PINECONE_INDEX_NAME}")

    return pc


# =============================================================================
# WEB CRAWLING AND CONTENT LOADING
# =============================================================================


def is_valid_url(url: str, base_domain: str) -> bool:
    """Check if the URL is valid and belongs to the same domain."""
    try:
        # Parse the URL
        parsed = urlparse(url)

        # Check if the URL belongs to the same domain
        if not parsed.netloc or base_domain not in parsed.netloc:
            return False

        # Exclude URLs with file extensions that are not web pages
        excluded_extensions = [
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".zip",
            ".tar",
            ".gz",
            ".mp3",
            ".mp4",
            ".avi",
        ]
        if any(url.lower().endswith(ext) for ext in excluded_extensions):
            return False

        # Exclude URLs with certain patterns (like calendar dates, pagination, etc.)
        excluded_patterns = [
            "/tag/",
            "/category/",
            "/author/",
            "/page/",
            "/feed/",
            "/comment-page-",
            "/trackback/",
            "/wp-json/",
            "/wp-admin/",
            # Exclude Cloudflare email protection pages
            "/cdn-cgi/l/email-protection",
            # Exclude other utility pages
            "/wp-content/",
            "/wp-includes/",
            "/assets/",
            "/images/",
            "/js/",
            "/css/",
            "/fonts/",
            "/favicon",
            "/sitemap",
            "/robots.txt",
            "/wp-login",
        ]
        if any(pattern in url for pattern in excluded_patterns):
            return False

        # Exclude URLs with fragments (like #about, #pricing, etc.) as they create duplicate content
        if "#" in url:
            return False

        return True
    except:
        return False


def crawl_website(start_url: str, max_pages: int = None) -> List[str]:
    """Crawl a website starting from the given URL and return all discovered URLs.
    If max_pages is None, crawl all discoverable pages."""
    # Extract the base domain from the start URL
    parsed_url = urlparse(start_url)
    base_domain = parsed_url.netloc

    # Set to store discovered URLs
    discovered_urls = set([start_url])
    urls_to_visit = [start_url]
    visited_urls = set()

    print(f"Starting crawl from {start_url}")
    print(f"Base domain: {base_domain}")
    print(
        f"Crawling with no page limit"
        if max_pages is None
        else f"Crawling with limit of {max_pages} pages"
    )

    # Crawl until we've visited all URLs or reached the maximum number of pages (if specified)
    while urls_to_visit and (max_pages is None or len(visited_urls) < max_pages):
        # Get the next URL to visit
        current_url = urls_to_visit.pop(0)

        # Skip if we've already visited this URL
        if current_url in visited_urls:
            continue

        # Show progress
        progress = f"[{len(visited_urls) + 1}/{len(discovered_urls)}]"
        print(f"{progress} Crawling: {current_url}")

        try:
            # Add a delay to avoid overwhelming the server
            time.sleep(1)

            # Send a GET request to the URL with retry logic
            max_retries = 3
            retry_count = 0
            response = None

            while retry_count < max_retries:
                try:
                    response = requests.get(current_url, timeout=10)

                    # Check if the request was successful
                    if response.status_code == 200:
                        break

                    # If we got a permanent error (4xx), don't retry
                    if 400 <= response.status_code < 500:
                        print(f"  Skipping: HTTP {response.status_code}")
                        break

                    # For server errors (5xx), retry
                    retry_count += 1
                    if retry_count < max_retries:
                        print(
                            f"  Retry {retry_count}/{max_retries} for {current_url}: HTTP {response.status_code}"
                        )
                        time.sleep(2)  # Wait before retrying
                    else:
                        print(
                            f"  Failed after {max_retries} retries: {current_url} - HTTP {response.status_code}"
                        )

                except (
                    requests.exceptions.RequestException,
                    requests.exceptions.Timeout,
                ) as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(
                            f"  Retry {retry_count}/{max_retries} for {current_url}: {str(e)}"
                        )
                        time.sleep(2)  # Wait before retrying
                    else:
                        print(
                            f"  Failed after {max_retries} retries: {current_url} - {str(e)}"
                        )
                        response = None

            # Skip if no successful response
            if not response or response.status_code != 200:
                continue

            # Mark the URL as visited
            visited_urls.add(current_url)

            # Parse the HTML content
            soup = BeautifulSoup(response.text, "html.parser")

            # Find all links on the page
            for link in soup.find_all("a", href=True):
                href = link["href"]

                # Convert relative URLs to absolute URLs
                absolute_url = urljoin(current_url, href)

                # Check if the URL is valid and belongs to the same domain
                if (
                    is_valid_url(absolute_url, base_domain)
                    and absolute_url not in discovered_urls
                ):
                    discovered_urls.add(absolute_url)
                    urls_to_visit.append(absolute_url)
                    print(f"  Discovered: {absolute_url}")

        except Exception as e:
            print(f"  Error crawling {current_url}: {str(e)}")

    print(
        f"Crawl complete. Discovered {len(discovered_urls)} URLs, visited {len(visited_urls)} pages."
    )
    return list(discovered_urls)


def load_from_url(url: str) -> List[Document]:
    """Load content from a URL and convert to documents."""
    print(f"Loading content from URL: {url}")

    # Skip Cloudflare email protection pages
    if "/cdn-cgi/l/email-protection" in url:
        print(f"  Skipping Cloudflare email protection page: {url}")
        return []

    try:
        loader = WebBaseLoader(url)
        documents = loader.load()

        # Extract metadata from the page with retry logic
        max_retries = 3
        retry_count = 0
        response = None

        while retry_count < max_retries:
            try:
                response = requests.get(url, timeout=10)

                # Check if the request was successful
                if response.status_code == 200:
                    break

                # For server errors (5xx), retry
                retry_count += 1
                if retry_count < max_retries:
                    print(f"  Retry {retry_count}/{max_retries} for metadata: {url}")
                    time.sleep(2)  # Wait before retrying
                else:
                    print(f"  Failed after {max_retries} retries for metadata: {url}")

            except (
                requests.exceptions.RequestException,
                requests.exceptions.Timeout,
            ) as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(
                        f"  Retry {retry_count}/{max_retries} for metadata: {url} - {str(e)}"
                    )
                    time.sleep(2)  # Wait before retrying
                else:
                    print(
                        f"  Failed after {max_retries} retries for metadata: {url} - {str(e)}"
                    )
                    response = None

        # If we couldn't get a response, return empty list
        if not response or response.status_code != 200:
            print(f"  Could not fetch metadata for {url}")
            return documents  # Return whatever documents we have from the loader

        soup = BeautifulSoup(response.text, "html.parser")

        # Check if this is a Cloudflare email protection page by looking at the content
        if soup.title and "email protection" in soup.title.text.lower():
            print(f"  Skipping detected Cloudflare email protection page: {url}")
            return []

        # Extract the main content (remove navigation, footer, etc.) for regular pages
        # For the homepage and contact page, keep the full content including navigation and footer
        is_homepage = url == f"{HOST_URL}/" or url == HOST_URL
        is_contact_page = "contact" in url.lower() and (
            url.lower().endswith("contact/") or url.lower().endswith("contact")
        )

        if is_homepage or is_contact_page:
            # Keep full content for both homepage and contact page
            page_type = "homepage" if is_homepage else "contact page"
            print(
                f"  Processing {page_type} with full content including navigation/footer: {url}"
            )
            # For the homepage, we'll keep the full content to capture navigation and footer
            full_content = soup.get_text(strip=True)

            # Create a new document with the full content
            full_doc = Document(
                page_content=full_content,
                metadata={
                    "source": url,
                    "title": soup.title.string if soup.title else url,
                    "page_type": page_type,
                    "crawl_date": datetime.now().isoformat(),
                    "has_full_content": True,
                },
            )
            # Add this document to the list
            documents.append(full_doc)
        else:
            # For non-homepage pages, extract just the main content
            main_content = ""

            # Try to find the main content container
            main_elements = soup.find_all(
                ["main", "article", "div", "section"],
                class_=lambda c: c
                and any(
                    term in str(c).lower()
                    for term in ["content", "main", "article", "entry", "post"]
                ),
            )

            if main_elements:
                # Use the largest content block
                main_element = max(main_elements, key=lambda el: len(str(el)))
                main_content = main_element.get_text(strip=True)
            else:
                # If no main content container found, use the body but try to exclude navigation and footer
                body = soup.find("body")
                if body:
                    # Try to remove navigation and footer elements
                    for nav in body.find_all(["nav", "header", "footer"]):
                        nav.decompose()
                    main_content = body.get_text(strip=True)

            # Replace the content of existing documents with the main content
            if documents and len(documents) > 0 and main_content:
                for doc in documents:
                    doc.page_content = main_content

        # Update metadata for all documents
        for doc in documents:
            # Determine page type for regular pages
            page_type = "unknown"
            if "blog" in url.lower() or "/blog/" in url.lower():
                page_type = "blog"
            elif "about" in url.lower():
                page_type = "about"
            elif "pricing" in url.lower():
                page_type = "pricing"
            elif (
                "solution" in url.lower()
                or "product" in url.lower()
                or "service" in url.lower()
            ):
                page_type = "solution"
            else:
                page_type = "content"

            # Extract page category from URL path
            path_parts = urlparse(url).path.strip("/").split("/")
            category = path_parts[0] if path_parts else "root"

            doc.metadata.update(
                {
                    "source": url,
                    "title": soup.title.string if soup.title else url,
                    "page_type": page_type,
                    "category": category,
                    "crawl_date": datetime.now().isoformat(),
                    "has_full_content": is_homepage or is_contact_page,
                }
            )

        return documents
    except Exception as e:
        print(f"Error loading {url}: {str(e)}")
        return []


def load_from_file(file_path: str) -> List[Document]:
    """Load content from a local file and convert to documents."""
    print(f"Loading content from file: {file_path}")
    loader = TextLoader(file_path)
    documents = loader.load()

    # Update metadata for all documents
    for doc in documents:
        doc.metadata.update(
            {
                "source": file_path,
                "title": os.path.basename(file_path),
            }
        )

    return documents


# =============================================================================
# DOCUMENT PROCESSING AND EMBEDDING
# =============================================================================


def clean_text(text: str) -> str:
    """Clean text by removing excessive whitespace and formatting characters."""
    # Replace multiple newlines with a single newline
    text = re.sub(r"\n{3,}", "\n\n", text)

    # Replace multiple spaces with a single space
    text = re.sub(r" {2,}", " ", text)

    # Remove excessive dots or dashes (often used for formatting)
    text = re.sub(r"\.{4,}", "...", text)
    text = re.sub(r"-{4,}", "---", text)

    # Remove excessive whitespace around punctuation
    text = re.sub(r"\s+([.,;:!?])", r"\1", text)

    # Trim whitespace
    text = text.strip()

    return text


def process_documents(documents: List[Document]) -> List[Document]:
    """Clean and split documents into chunks for better retrieval."""
    print(f"Processing {len(documents)} documents into chunks")

    # Clean the documents first
    cleaned_documents = []
    for doc in documents:
        # Clean the text
        cleaned_text = clean_text(doc.page_content)

        # Create a new document with cleaned text
        cleaned_doc = Document(page_content=cleaned_text, metadata=doc.metadata)
        cleaned_documents.append(cleaned_doc)

    print(f"Cleaned {len(documents)} documents")

    # Create a text splitter with optimized parameters
    # Using a larger chunk size for better context and moderate overlap
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=800,  # Increased from 500 for better context
        chunk_overlap=150,  # Increased from 100 for better continuity
        length_function=len,
        separators=["\n\n", "\n", ". ", " ", ""],  # Added period+space as a separator
    )

    # Split documents into chunks
    chunks = text_splitter.split_documents(cleaned_documents)

    # Keep all chunks regardless of size to ensure we don't miss any important information
    print(f"Created {len(chunks)} chunks")

    return chunks


def embed_and_store(documents: List[Document], pc) -> None:
    """Create embeddings and store in Pinecone."""
    total_docs = len(documents)
    print(f"Creating embeddings and storing {total_docs} documents in Pinecone")

    # Initialize OpenAI embeddings
    embeddings = OpenAIEmbeddings()

    # Get Pinecone index
    index = pc.Index(PINECONE_INDEX_NAME)

    # Prepare vectors for upsert
    vectors = []
    processed_count = 0
    batch_size = 100

    for i, doc in enumerate(documents):
        # Show progress
        processed_count += 1
        if processed_count % 10 == 0 or processed_count == total_docs:
            print(
                f"Processing document {processed_count}/{total_docs} ({(processed_count/total_docs)*100:.1f}%)"
            )

        try:
            # Create embedding
            embedding = embeddings.embed_query(doc.page_content)

            # Create vector with enhanced metadata for better filtering
            metadata = {
                "text": doc.page_content,
                "source": doc.metadata.get("source", "unknown"),
                "title": doc.metadata.get("title", "unknown"),
                "page_type": doc.metadata.get("page_type", "unknown"),
                "category": doc.metadata.get("category", "unknown"),
                "crawl_date": doc.metadata.get(
                    "crawl_date", datetime.now().isoformat()
                ),
                "has_full_content": doc.metadata.get("has_full_content", False),
                "chunk_index": i,  # Add chunk index for ordering if needed
                # Contact information
                "contact_email": "<EMAIL>",
            }

            # Extract additional metadata for filtering
            url = doc.metadata.get("source", "")

            # Add section information based on URL structure
            if url:
                path = urlparse(url).path
                sections = [p for p in path.split("/") if p]
                if sections:
                    metadata["section"] = sections[0]
                    if len(sections) > 1:
                        metadata["subsection"] = sections[1]

            vector = {"id": f"doc_{i}", "values": embedding, "metadata": metadata}
            vectors.append(vector)

            # Upsert in batches to avoid hitting API limits
            if len(vectors) >= batch_size:
                index.upsert(vectors=vectors)
                print(
                    f"Upserted batch of {len(vectors)} vectors ({processed_count}/{total_docs})"
                )
                vectors = []
        except Exception as e:
            print(f"Error embedding document {i}: {str(e)}")

    # Upsert any remaining vectors
    if vectors:
        index.upsert(vectors=vectors)
        print(f"Upserted final batch of {len(vectors)} vectors")

    print(f"Successfully embedded and stored {processed_count} documents in Pinecone")


# =============================================================================
# MAIN EXECUTION
# =============================================================================


def main():
    """Main function to run the embedding process."""
    parser = argparse.ArgumentParser(
        description="Embed WordPress content into Pinecone for RAG retrieval",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
Examples:
  # Crawl and embed an entire website
  python embed_documents.py --url https://{HOST_DOMAIN} --crawl

  # Embed a single page only
  python embed_documents.py --url https://{HOST_DOMAIN}/about

  # Embed content from a local file
  python embed_documents.py --file content.html
        """,
    )

    # Create a group for mutually exclusive source options (--url or --file)
    group = parser.add_mutually_exclusive_group(required=True)

    # Source options
    group.add_argument(
        "--url", help="URL of the WordPress site or specific page to embed"
    )
    group.add_argument("--file", help="Path to a local HTML or text file to embed")

    # Crawling options
    parser.add_argument(
        "--crawl",
        action="store_true",
        help="Crawl the website to find and embed all linked pages (only works with --url)",
    )
    parser.add_argument(
        "--max-pages",
        type=int,
        default=None,
        help="Maximum number of pages to crawl and embed (default: unlimited)",
    )

    args = parser.parse_args()

    # Initialize Pinecone
    pc = setup_pinecone()

    # Load documents
    all_documents = []

    if args.url:
        if args.crawl:
            print(f"Crawling website starting from {args.url}")
            urls = crawl_website(args.url, max_pages=args.max_pages)

            # Load documents from each URL
            for url in urls:
                docs = load_from_url(url)
                all_documents.extend(docs)
                print(f"Loaded {len(docs)} documents from {url}")
        else:
            # Just load from the single URL
            all_documents = load_from_url(args.url)
    else:
        all_documents = load_from_file(args.file)

    if not all_documents:
        print("No documents were loaded. Exiting.")
        return

    print(f"Total documents loaded: {len(all_documents)}")

    # Add a special document with contact information
    contact_doc = Document(
        page_content=f"""
{HOST_DOMAIN.capitalize()} Contact Information:
Email: <EMAIL>

If you need to get in touch with {HOST_DOMAIN.capitalize()}, please use the email address above or visit our contact page.
        """.strip(),
        metadata={
            "source": "hardcoded_contact_info",
            "title": f"{HOST_DOMAIN.capitalize()} Contact Information",
            "page_type": "contact_info",
            "category": "contact",
            "has_full_content": True,
            "is_contact_info": True,
        },
    )
    all_documents.append(contact_doc)
    print("Added special contact information document")

    # Process documents into chunks
    chunks = process_documents(all_documents)

    # Embed and store in Pinecone
    embed_and_store(chunks, pc)

    print("Document embedding complete!")


if __name__ == "__main__":
    main()
