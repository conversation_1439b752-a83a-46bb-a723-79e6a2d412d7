-- Initial Data Population for Phase 2
-- Run this after phase2_migration.sql to populate default data
-- ================================================
-- CLEAN EXISTING DATA BEFORE INSERTING DEFAULTS
-- ================================================
TRUNCATE TABLE tag_definitions CASCADE;
TRUNCATE TABLE tag_categories CASCADE;
TRUNCATE TABLE scoring_templates CASCADE;

-- Insert default tag categories
INSERT INTO tag_categories (name, display_name, description, default_threshold, priority) VALUES
('education', 'Education', 'Academic background, degrees, and educational achievements', 0.70, 1),
('experience', 'Experience', 'Professional work history and career progression', 0.75, 2),
('skills', 'Skills', 'Technical abilities, tools, and competencies', 0.80, 3),
('achievements', 'Achievements', 'Awards, recognitions, and measurable accomplishments', 0.60, 4),
('personal', 'Personal', 'Career goals, preferences, and availability', 0.65, 5),
('cultural', 'Cultural Fit', 'Values, work style, and organizational alignment', 0.60, 6)
ON CONFLICT (name) DO NOTHING;

-- Helper function to insert tag definitions
DO $$
DECLARE
    cat_id UUID;
BEGIN
    -- Education tags
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'education';
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates) VALUES
    (cat_id, 'degree_level', 'Degree Level', 'text', 
     ARRAY['bachelor', 'master', 'phd', 'doctorate', 'diploma', 'degree'], 
     ARRAY['What is your highest degree level?', 'Which degree did you complete?']),
    (cat_id, 'university_name', 'University Name', 'text', 
     ARRAY['university', 'college', 'institute', 'school', 'graduated from'], 
     ARRAY['Which university did you attend?', 'What is your university name?']),
    (cat_id, 'gpa', 'GPA/Grade', 'number', 
     ARRAY['gpa', 'cgpa', 'grade', 'percentage', 'marks', 'score'], 
     ARRAY['What was your GPA?', 'What was your final grade?']),
    (cat_id, 'graduation_year', 'Graduation Year', 'date', 
     ARRAY['graduated', 'graduation', 'completed', 'batch', 'year'], 
     ARRAY['When did you graduate?', 'What year did you graduate?']),
    (cat_id, 'major_field', 'Major/Field of Study', 'text', 
     ARRAY['major', 'specialization', 'field', 'studied', 'degree in'], 
     ARRAY['What was your major field?', 'Which field did you study?'])
    ON CONFLICT (category_id, tag_key) DO NOTHING;

    -- Experience tags
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'experience';
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates) VALUES
    (cat_id, 'total_years', 'Total Years of Experience', 'number', 
     ARRAY['years of experience', 'experience', 'worked for', 'years', 'career'], 
     ARRAY['How many years of experience do you have?', 'What is your total work experience?']),
    (cat_id, 'industry_type', 'Industry Type', 'text', 
     ARRAY['industry', 'sector', 'domain', 'field', 'worked in'], 
     ARRAY['Which industries have you worked in?', 'What is your primary industry?']),
    (cat_id, 'company_size', 'Company Size', 'text', 
     ARRAY['company size', 'employees', 'organization', 'startup', 'enterprise'], 
     ARRAY['What size companies have you worked for?', 'How many employees at your companies?']),
    (cat_id, 'role_level', 'Role Level', 'text', 
     ARRAY['position', 'level', 'senior', 'junior', 'manager', 'lead'], 
     ARRAY['What was your highest role level?', 'Which level positions have you held?']),
    (cat_id, 'management_scope', 'Management Scope', 'text', 
     ARRAY['managed', 'team size', 'direct reports', 'supervised', 'led'], 
     ARRAY['How many people have you managed?', 'What was your largest team size?']),
    (cat_id, 'project_scale', 'Project Scale', 'text', 
     ARRAY['project', 'budget', 'scale', 'million', 'implemented'], 
     ARRAY['What was your largest project budget?', 'What was your biggest project scale?']),
    (cat_id, 'team_size', 'Team Size Worked With', 'number', 
     ARRAY['team', 'worked with', 'collaborated', 'members'], 
     ARRAY['What team size do you typically work with?', 'How many team members usually?']),
    (cat_id, 'budget_responsibility', 'Budget Responsibility', 'text', 
     ARRAY['budget', 'managed budget', 'financial', 'revenue'], 
     ARRAY['What budget have you managed?', 'What was your financial responsibility amount?'])
    ON CONFLICT (category_id, tag_key) DO NOTHING;

    -- Skills tags
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'skills';
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates) VALUES
    (cat_id, 'core_competencies', 'Core Competencies', 'list', 
     ARRAY['competencies', 'skills', 'abilities', 'expertise', 'proficient'], 
     ARRAY['What are your core competencies?', 'Which key skills do you possess?']),
    (cat_id, 'professional_skills', 'Professional Skills', 'list', 
     ARRAY['professional', 'job-related', 'work skills', 'expertise'], 
     ARRAY['What are your main professional skills?', 'Which work-related skills do you have?']),
    (cat_id, 'industry_knowledge', 'Industry Knowledge', 'list', 
     ARRAY['industry', 'domain', 'sector knowledge', 'field expertise'], 
     ARRAY['What industry knowledge do you have?', 'Which sectors are you familiar with?']),
    (cat_id, 'tools_equipment', 'Tools & Equipment', 'list', 
     ARRAY['tools', 'equipment', 'systems', 'machinery', 'software'], 
     ARRAY['What tools and equipment can you use?', 'Which systems are you familiar with?']),
    (cat_id, 'soft_skills', 'Soft Skills', 'list', 
     ARRAY['communication', 'leadership', 'teamwork', 'analytical', 'problem solving'], 
     ARRAY['What are your key soft skills?', 'Which interpersonal skills do you have?']),
    (cat_id, 'language_proficiency', 'Language Proficiency', 'list', 
     ARRAY['language', 'speak', 'fluent', 'proficient', 'multilingual'], 
     ARRAY['What languages do you speak?', 'Which languages are you fluent in?']),
    (cat_id, 'certifications_held', 'Certifications & Licenses', 'list', 
     ARRAY['certified', 'certification', 'certificate', 'licensed', 'qualified'], 
     ARRAY['What certifications do you hold?', 'Which licenses or certifications do you have?'])
    ON CONFLICT (category_id, tag_key) DO NOTHING;

    -- Achievements tags
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'achievements';
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates) VALUES
    (cat_id, 'awards_received', 'Awards & Recognition', 'list', 
     ARRAY['award', 'recognition', 'honor', 'prize', 'achievement'], 
     ARRAY['What awards have you received?', 'Which awards do you have?']),
    (cat_id, 'publications', 'Publications', 'list', 
     ARRAY['published', 'publication', 'paper', 'article', 'journal'], 
     ARRAY['Have you published any papers?', 'What have you published?']),
    (cat_id, 'patents', 'Patents', 'list', 
     ARRAY['patent', 'invention', 'intellectual property'], 
     ARRAY['Do you hold any patents?', 'What patents do you have?']),
    (cat_id, 'performance_metrics', 'Performance Metrics', 'text', 
     ARRAY['increased', 'improved', 'reduced', 'achieved', 'metrics'], 
     ARRAY['What measurable results have you achieved?', 'What are your key performance metrics?']),
    (cat_id, 'growth_impact', 'Growth & Impact', 'text', 
     ARRAY['growth', 'revenue', 'expansion', 'impact'], 
     ARRAY['What growth impact have you made?', 'What business impact have you achieved?']),
    (cat_id, 'innovation_examples', 'Innovation Examples', 'text', 
     ARRAY['innovation', 'innovative', 'created', 'developed'], 
     ARRAY['What innovative solutions have you created?', 'What innovations have you developed?'])
    ON CONFLICT (category_id, tag_key) DO NOTHING;

    -- Personal tags
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'personal';
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates) VALUES
    (cat_id, 'career_goals', 'Career Goals', 'text', 
     ARRAY['career goal', 'aspiration', 'objective', 'future'], 
     ARRAY['What are your career goals?', 'What is your career objective?']),
    (cat_id, 'work_preferences', 'Work Preferences', 'text', 
     ARRAY['prefer', 'preference', 'remote', 'office', 'hybrid'], 
     ARRAY['What are your work preferences?', 'Do you prefer remote or office work?']),
    (cat_id, 'availability', 'Availability', 'text', 
     ARRAY['available', 'start', 'notice period', 'immediately'], 
     ARRAY['When can you start?', 'What is your notice period?']),
    (cat_id, 'location_flexibility', 'Location Flexibility', 'text', 
     ARRAY['relocate', 'location', 'travel', 'flexible'], 
     ARRAY['Are you willing to relocate?', 'Are you flexible with location?']),
    (cat_id, 'salary_expectations', 'Salary Expectations', 'text', 
     ARRAY['salary', 'compensation', 'package', 'expectation'], 
     ARRAY['What are your salary expectations?', 'What compensation do you expect?']),
    (cat_id, 'leadership_style', 'Leadership Style', 'text', 
     ARRAY['leadership', 'management style', 'lead', 'mentor'], 
     ARRAY['What is your leadership style?', 'Which leadership approach do you use?'])
    ON CONFLICT (category_id, tag_key) DO NOTHING;

    -- Cultural tags
    SELECT id INTO cat_id FROM tag_categories WHERE name = 'cultural';
    INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates) VALUES
    (cat_id, 'values_alignment', 'Values Alignment', 'text', 
     ARRAY['values', 'believe', 'important', 'principle'], 
     ARRAY['What values are important to you?', 'What are your core professional values?']),
    (cat_id, 'communication_style', 'Communication Style', 'text', 
     ARRAY['communication', 'communicate', 'style', 'approach'], 
     ARRAY['What is your communication style?', 'Which communication approach do you use?']),
    (cat_id, 'problem_solving_approach', 'Problem Solving Approach', 'text', 
     ARRAY['problem solving', 'approach', 'analytical', 'creative'], 
     ARRAY['What is your problem-solving approach?', 'Which problem-solving method do you use?']),
    (cat_id, 'learning_agility', 'Learning Agility', 'text', 
     ARRAY['learn', 'learning', 'adapt', 'new skills'], 
     ARRAY['What is your learning method?', 'Which learning approach do you use?']),
    (cat_id, 'adaptability', 'Adaptability', 'text', 
     ARRAY['adapt', 'flexible', 'change', 'resilient'], 
     ARRAY['Are you adaptable to change?', 'What is your adaptability level?']),
    (cat_id, 'team_collaboration', 'Team Collaboration', 'text', 
     ARRAY['team', 'collaborate', 'teamwork', 'cooperation'], 
     ARRAY['What is your team collaboration style?', 'Which teamwork approach do you use?'])
    ON CONFLICT (category_id, tag_key) DO NOTHING;
END $$;

-- Insert a default scoring template
INSERT INTO scoring_templates (
    name, 
    description, 
    is_default, 
    is_active
) VALUES (
    'Default Balanced Template',
    'A balanced scoring template suitable for most positions',
    true,
    true
) ON CONFLICT DO NOTHING;