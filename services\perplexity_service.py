"""
Perplexity Service Module

This module provides web search functionality using Perplexity API
for the agent to use when internal knowledge is insufficient.
"""

import os
import logging
import requests
import json
from typing import Optional
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger("perplexity_service")


class PerplexityService:
    """Service for interacting with Perplexity API for web search"""

    def __init__(self):
        self.api_key = os.environ.get("PERPLEXITY_API_KEY")
        self.base_url = "https://api.perplexity.ai/chat/completions"

        if not self.api_key:
            logger.warning("PERPLEXITY_API_KEY not found in environment variables")

    def is_available(self) -> bool:
        """Check if Perplexity service is available"""
        return bool(self.api_key)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        reraise=True,
    )
    def search_web(
        self, query: str, model: str = "sonar-pro", max_tokens: int = 400
    ) -> Optional[str]:
        """
        Search the web using Perplexity API

        Args:
            query: The search query (always in English from agent)
            model: Perplexity model to use (sonar-pro, sonar)
            max_tokens: Maximum tokens in response

        Returns:
            Search result or None if failed
        """
        if not self.is_available():
            logger.error("Perplexity API key not available")
            return None

        try:
            # System prompt for web search (always in English)
            system_prompt = """
            You are an AI assistant for LeadersScout, a Saudi Arabia-based executive 
            search and talent intelligence firm. LeadersScout specializes in connecting 
            visionary organizations with transformative leaders across the region, 
            focusing on quality, discretion, and long-term value in executive placements.

            SEARCH FOCUS:
            - Executive search and leadership recruitment strategies
            - Talent intelligence and market mapping services
            - Succession planning and leadership development
            - Trends in executive leadership within Saudi Arabia and the Gulf region
            - Comparative analyses of executive search firms
            - Insights into leadership challenges and solutions in various industries
            - General information not present in our existing knowledge base

            IMPORTANT GUIDELINES:
            - Prioritize information relevant to the Saudi Arabian and Gulf markets
            - Focus on business-to-business (B2B) executive search and leadership solutions
            - Include industry standards and best practices when available
            - Provide current, factual information with credible sources
            - Disregard unrelated services or companies with similar names

            Keep responses concise and business-focused
            """

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            payload = {
                "model": model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": query},
                ],
                "temperature": 0.2,
                "max_tokens": max_tokens,
                "search_domain_filter": [
                    # Exclude irrelevant domains
                    "-pinterest.com",
                    "-reddit.com",
                    "-quora.com",
                    "-facebook.com",
                    "-instagram.com",
                    "-twitter.com",
                ],
            }

            logger.info(f"Sending web search request to Perplexity: {query[:50]}...")

            response = requests.post(
                self.base_url, headers=headers, json=payload, timeout=30
            )

            response.raise_for_status()
            result = response.json()

            if result and "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"Received web search response: {len(content)} characters")
                return content
            else:
                logger.warning("No content in Perplexity response")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error in Perplexity search: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in Perplexity response: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in Perplexity search: {e}")
            return None


# Global instance
perplexity_service = PerplexityService()


def search_web(query: str) -> Optional[str]:
    """
    Convenience function for web search

    Args:
        query: Search query (always in English from agent)

    Returns:
        Search result or None
    """
    return perplexity_service.search_web(query)
