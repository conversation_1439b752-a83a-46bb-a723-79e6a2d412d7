<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Assessments</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f7f6;
            color: #333;
            margin: 0;
            padding: 2em;
        }
        .container {
            max-width: 1200px;
            margin: auto;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 1.5em;
        }
        .assessments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5em;
        }
        .assessment-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 1.5em;
            transition: transform 0.2s ease-in-out;
        }
        .assessment-card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            font-size: 1.1em;
            font-weight: 600;
            color: #34495e;
            margin-bottom: 1em;
        }
        .card-body p {
            margin: 0.5em 0;
            color: #555;
        }
        .card-body strong {
            color: #333;
        }
        .view-btn {
            display: inline-block;
            background-color: #3498db;
            color: #fff;
            padding: 0.6em 1.2em;
            border-radius: 5px;
            text-decoration: none;
            text-align: center;
            margin-top: 1em;
            transition: background-color 0.2s;
        }
        .view-btn:hover {
            background-color: #2980b9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1.5em;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        th, td {
            padding: 1em;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        th {
            background-color: #f4f7f6;
            color: #2c3e50;
            font-weight: 600;
        }
        tbody tr:hover {
            background-color: #f9f9f9;
        }
        th.sortable {
            cursor: pointer;
        }
        th.sortable:hover {
            background-color: #e8eef1;
        }
        .th-name { width: 25%; }
        .th-email { width: 30%; }
        .th-date { width: 20%; }
        .th-score { width: 10%; }
        .th-action { width: 15%; text-align: center; }
        .td-action { text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CV Assessments</h1>
        <table>
            <thead>
                <tr>
                    <th class="th-name">Name</th>
                    <th class="th-email">Email</th>
                    <th class="th-date sortable" id="sort-by-date" data-sort-dir="default">Date Submitted</th>
                    <th class="th-score sortable" id="sort-by-score" data-sort-dir="default">Score</th>
                    <th class="th-action">Action</th>
                </tr>
            </thead>
            <tbody id="assessments-tbody">
                {% for assessment in assessments %}
                <tr>
                    <td>{{ assessment.name or 'N/A' }}</td>
                    <td>{{ assessment.email or 'N/A' }}</td>
                    <td class="date-cell">{{ assessment.created_at }}</td>
                    <td>{{ assessment.score if assessment.score is not none else 'Not Scored' }}</td>
                    <td class="td-action"><a href="/cv-assessments/{{ assessment.id }}" class="view-btn">View Details</a></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tableBody = document.getElementById('assessments-tbody');
        const initialRows = Array.from(tableBody.querySelectorAll('tr'));
        const sortHeaders = document.querySelectorAll('th.sortable');

        // Format dates on initial load
        document.querySelectorAll('.date-cell').forEach(cell => {
            if (cell.textContent.trim()) {
                const date = new Date(cell.textContent.trim());
                const options = { year: 'numeric', month: 'long', day: 'numeric' };
                cell.textContent = date.toLocaleDateString('en-US', options);
                cell.dataset.timestamp = date.getTime();
            }
        });

        const sortStates = { 'default': 'desc', 'desc': 'asc', 'asc': 'default' };

        sortHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const sortDir = header.dataset.sortDir = sortStates[header.dataset.sortDir];
                const columnType = header.id.includes('date') ? 'date' : 'number';
                const columnIndex = Array.from(header.parentNode.children).indexOf(header);
                
                // Reset other headers
                sortHeaders.forEach(h => {
                    if (h !== header) {
                        h.dataset.sortDir = 'default';
                        h.innerHTML = h.textContent.replace(/ (↑|↓)/, '');
                    }
                });

                if (sortDir === 'default') {
                    header.innerHTML = header.textContent.replace(/ (↑|↓)/, '');
                    tableBody.innerHTML = '';
                    initialRows.forEach(row => tableBody.appendChild(row));
                } else {
                    const arrow = sortDir === 'asc' ? ' &uarr;' : ' &darr;';
                    header.innerHTML = header.textContent.replace(/ (↑|↓)/, '') + arrow;
                    sortTable(columnIndex, sortDir, columnType);
                }
            });
        });

        const sortTable = (columnIndex, sortDir, type) => {
            const rows = Array.from(tableBody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                const valA = getValue(a.children[columnIndex], type);
                const valB = getValue(b.children[columnIndex], type);

                if (sortDir === 'asc') {
                    return valA - valB;
                } else {
                    return valB - valA;
                }
            });

            tableBody.innerHTML = '';
            rows.forEach(row => tableBody.appendChild(row));
        };

        const getValue = (cell, type) => {
            if (type === 'date') {
                return parseInt(cell.dataset.timestamp, 10) || 0;
            }
            return parseFloat(cell.textContent.trim()) || -1;
        };
    });
</script>
</body>
</html>
