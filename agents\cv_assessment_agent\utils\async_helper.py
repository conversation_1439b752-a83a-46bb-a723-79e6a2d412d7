"""
Async Helper Module

Handles async/sync coordination patterns to simplify the complex
async/sync mixing that was scattered throughout the original agent.
"""

import asyncio
import concurrent.futures
import logging
from typing import Any, Callable, TypeVar, Awaitable

logger = logging.getLogger("async_helper")

T = TypeVar("T")


class AsyncHelper:
    """Helper class for managing async/sync coordination patterns."""

    @staticmethod
    def run_async_in_sync(
        async_func: Callable[..., Awaitable[T]], *args, **kwargs
    ) -> T:
        """
        Run an async function in a sync context, handling event loop management.

        This replaces the complex executor patterns from the original code.
        """
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If there's already a running loop, use thread pool executor
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_func(*args, **kwargs))
                    return future.result()
            else:
                # If no loop is running, use asyncio.run
                return asyncio.run(async_func(*args, **kwargs))
        except Exception as e:
            logger.error(
                f"Error running async function {async_func.__name__}: {str(e)}"
            )
            raise

    @staticmethod
    def run_multiple_async_in_sync(*async_funcs_with_args) -> list:
        """
        Run multiple async functions concurrently in a sync context.

        Args:
            async_funcs_with_args: Tuples of (async_func, args, kwargs)

        Returns:
            List of results in the same order as input functions
        """

        async def run_all():
            tasks = []
            for func_with_args in async_funcs_with_args:
                if len(func_with_args) == 1:
                    func, args, kwargs = func_with_args[0], (), {}
                elif len(func_with_args) == 2:
                    func, args, kwargs = func_with_args[0], func_with_args[1], {}
                else:
                    func, args, kwargs = (
                        func_with_args[0],
                        func_with_args[1],
                        func_with_args[2],
                    )

                tasks.append(func(*args, **kwargs))

            return await asyncio.gather(*tasks)

        return AsyncHelper.run_async_in_sync(run_all)

    @staticmethod
    def safe_async_call(
        async_func: Callable[..., Awaitable[T]],
        default_value: T = None,
        *args,
        **kwargs,
    ) -> T:
        """
        Safely call an async function with error handling and default fallback.

        Args:
            async_func: The async function to call
            default_value: Value to return if the async call fails
            *args, **kwargs: Arguments to pass to the async function

        Returns:
            Result of async function or default_value on error
        """
        try:
            return AsyncHelper.run_async_in_sync(async_func, *args, **kwargs)
        except Exception as e:
            logger.error(f"Async call to {async_func.__name__} failed: {str(e)}")
            return default_value

    @staticmethod
    def create_async_wrapper(
        sync_func: Callable[..., T],
    ) -> Callable[..., Awaitable[T]]:
        """
        Create an async wrapper for a sync function.

        Args:
            sync_func: The synchronous function to wrap

        Returns:
            Async version of the function
        """

        async def async_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: sync_func(*args, **kwargs))

        return async_wrapper
