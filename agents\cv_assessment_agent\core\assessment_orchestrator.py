"""
Assessment Orchestrator Module

Manages the CV assessment workflow and state transitions.
This is the main coordinator that orchestrates the entire assessment process.
"""

import logging
from typing import Optional, Dict, Any, List
from uuid import UUID

from .cv_analyzer import <PERSON><PERSON>nalyzer
from .scoring_coordinator import ScoringCoordinator
from ..utils.async_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..utils.language_handler import LanguageHandler
from utils.assessment_config import AssessmentConfig

logger = logging.getLogger("assessment_orchestrator")


class AssessmentOrchestrator:
    """Orchestrates the entire CV assessment workflow."""

    def __init__(
        self,
        model_name: str = "gpt-4.1",
        tag_extraction_service=None,
        scoring_engine_service=None,
    ):
        """Initialize the assessment orchestrator."""
        self.cv_analyzer = CVAnalyzer(model_name)
        self.scoring_coordinator = ScoringCoordinator(model_name)

        # External services
        self.tag_extraction_service = tag_extraction_service
        self.scoring_engine_service = scoring_engine_service

        # Current assessment state
        self.current_assessment_length = (
            AssessmentConfig.get_default_assessment_length()
        )

    def run_assessment(
        self,
        cv_text: str,
        qa_history: Optional[List[Dict[str, Any]]] = None,
        job_description: Optional[str] = None,
        industry: Optional[str] = None,
        template_id: Optional[str] = None,
        cv_assessment_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
        template_selection_tool=None,
    ) -> Dict[str, Any]:
        """
        Run the complete CV assessment workflow.

        Args:
            cv_text: The CV text to assess
            qa_history: Previous Q&A history (if continuing assessment)
            job_description: Optional job description for context
            industry: Optional industry for template selection
            template_id: Optional specific template ID
            cv_assessment_id: Optional CV assessment ID for tag-based processing
            job_id: Optional job ID for configuration
            template_selection_tool: Optional template selection tool

        Returns:
            Assessment result dictionary
        """
        qa_history = qa_history or []

        try:
            # Get assessment length configuration
            self.current_assessment_length = self._get_assessment_length(
                cv_assessment_id, template_id, job_id
            )

            # If no questions asked yet, start new assessment
            if not qa_history:
                return self._start_new_assessment(
                    cv_text,
                    job_description,
                    industry,
                    template_id,
                    cv_assessment_id,
                    job_id,
                    template_selection_tool,
                )

            # Continue existing assessment
            return self._continue_assessment(
                cv_text, qa_history, cv_assessment_id, template_id, job_id
            )

        except Exception as e:
            logger.error(f"Error in assessment workflow: {str(e)}")
            return self._generate_error_response(e)

    def _start_new_assessment(
        self,
        cv_text: str,
        job_description: Optional[str],
        industry: Optional[str],
        template_id: Optional[str],
        cv_assessment_id: Optional[UUID],
        job_id: Optional[UUID],
        template_selection_tool,
    ) -> Dict[str, Any]:
        """Start a new CV assessment."""
        try:
            # Analyze CV and extract basic information
            cv_analysis = self.cv_analyzer.extract_key_information(cv_text)

            # Select appropriate template
            template_info = self._select_template(
                cv_text,
                job_description,
                industry,
                cv_analysis.get("experience_level"),
                template_id,
                template_selection_tool,
            )

            # Extract tags for data collection (if tag service available)
            if self.tag_extraction_service and cv_assessment_id:
                logger.info("Extracting tags for data collection")
                try:
                    extraction_result = AsyncHelper.safe_async_call(
                        self.tag_extraction_service.extract_tags_from_cv,
                        {"extraction_complete": False},
                        cv_text,
                        cv_assessment_id,
                        job_description,
                    )

                    if extraction_result.get("extraction_complete", False):
                        logger.info("Tag extraction completed successfully")
                    else:
                        logger.warning(
                            f"Tag extraction failed: {extraction_result.get('error', 'Unknown error')}"
                        )

                except Exception as tag_error:
                    logger.warning(
                        f"Tag extraction error (continuing with AI questions): {str(tag_error)}"
                    )

            # Try to use the question generation tool first
            try:
                from ..tools.question_generation_tool import QuestionGenerationService

                # If we have cv_assessment_id, use the question generation service
                if cv_assessment_id:
                    logger.info("Using QuestionGenerationService for questions")

                    # Create an instance of the service
                    # Use the tag extraction service's db_service since AssessmentOrchestrator doesn't have db_service directly
                    db_service = (
                        self.tag_extraction_service.db_service
                        if self.tag_extraction_service
                        else None
                    )
                    question_service = QuestionGenerationService(
                        db_service, self.tag_extraction_service
                    )

                    # Generate questions using the new service
                    question_result = AsyncHelper.safe_async_call(
                        question_service.generate_questions,
                        {"questions": [], "success": False},
                        cv_assessment_id,
                        job_description=job_description,
                        question_limit=self.current_assessment_length,
                        category_weights=template_info.get("category_weights"),
                    )

                    if question_result.get("success", False) and question_result.get(
                        "questions"
                    ):
                        logger.info(
                            f"Successfully generated {len(question_result['questions'])} questions with new service"
                        )

                        # Format questions for assessment
                        questions = [
                            q["question"] for q in question_result["questions"]
                        ]

                        # Start assessment with these questions
                        current_language = LanguageHandler.detect_language(cv_text)
                        first_question = questions[0]
                        formatted_question = LanguageHandler.format_question_with_count(
                            first_question,
                            1,
                            self.current_assessment_length,
                            current_language,
                        )

                        qa_history = [
                            {
                                "question": first_question,
                                "answer": "",
                                "tag_based": True,
                            }
                        ]

                        return {
                            "name": cv_analysis.get("name"),
                            "email": cv_analysis.get("email"),
                            "question": first_question,
                            "user_message": formatted_question,
                            "remaining_questions": questions[1:],
                            "qa_history": qa_history,
                            "template_info": template_info,
                            "tag_based": True,
                            "cv_assessment_id": str(cv_assessment_id),
                            "question_metadata": question_result.get("questions", []),
                        }

                # If new service failed or not available, fall back to standard method
                logger.info("Falling back to standard AI-generated questions")
                return self._start_standard_assessment(
                    cv_text, template_info, cv_analysis
                )
            except Exception as ai_error:
                logger.warning(f"AI-generated questions failed: {str(ai_error)}")

                # Fallback to tag-based approach if available
                if self.tag_extraction_service and cv_assessment_id:
                    logger.info("Falling back to tag-based approach")
                    return self._start_tag_based_assessment(
                        cv_text,
                        cv_assessment_id,
                        template_info,
                        job_description,
                        job_id,
                        cv_analysis,
                    )
                else:
                    # No fallback available, re-raise the AI error
                    raise ai_error

        except Exception as e:
            logger.error(f"Error starting new assessment: {str(e)}")
            return self._generate_error_response(e)

    def _start_tag_based_assessment(
        self,
        cv_text: str,
        cv_assessment_id: UUID,
        template_info: Dict[str, Any],
        job_description: Optional[str],
        job_id: Optional[UUID],
        cv_analysis: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Start assessment using tag extraction and optimized questions."""
        try:
            # Extract tags from CV
            extraction_result = AsyncHelper.safe_async_call(
                self.tag_extraction_service.extract_tags_from_cv,
                {"extraction_complete": False},
                cv_text,
                cv_assessment_id,
                job_description,
            )

            if not extraction_result.get("extraction_complete", False):
                logger.error(
                    f"Tag extraction failed: {extraction_result.get('error', 'Unknown error')}"
                )
                return self._start_standard_assessment(
                    cv_text, template_info, cv_analysis
                )

            # Generate optimized questions using the question generation tool
            from ..tools.question_generation_tool import QuestionGenerationService

            # Use the question generation service
            db_service = (
                self.tag_extraction_service.db_service
                if self.tag_extraction_service
                else None
            )
            question_service = QuestionGenerationService(
                db_service, self.tag_extraction_service
            )

            question_result = AsyncHelper.safe_async_call(
                question_service.generate_questions,
                {"questions": [], "success": False},
                cv_assessment_id,
                job_description=job_description,
                question_limit=self.current_assessment_length,
                category_weights=template_info.get("category_weights"),
            )

            questions_data = (
                question_result.get("questions", [])
                if question_result.get("success", False)
                else None
            )

            if not questions_data:
                logger.warning(
                    "No tag-based questions generated, using standard approach"
                )
                return self._start_standard_assessment(
                    cv_text, template_info, cv_analysis
                )

            # Extract questions and start assessment
            questions = [q["question"] for q in questions_data]

            if questions:
                first_question = questions[0]
                current_language = LanguageHandler.detect_language(cv_text)
                formatted_question = LanguageHandler.format_question_with_count(
                    first_question, 1, self.current_assessment_length, current_language
                )

                qa_history = [{"question": first_question, "answer": ""}]

                return {
                    "name": cv_analysis.get("name"),
                    "email": cv_analysis.get("email"),
                    "question": first_question,
                    "user_message": formatted_question,
                    "remaining_questions": questions[1:],
                    "qa_history": qa_history,
                    "template_info": template_info,
                    "tag_based": True,
                    "cv_assessment_id": str(cv_assessment_id),
                    "extraction_result": extraction_result,
                    "question_metadata": questions_data,
                }
            else:
                raise ValueError("No questions generated from tag optimization")

        except Exception as e:
            logger.error(f"Error in tag-based assessment start: {str(e)}")
            return self._start_standard_assessment(cv_text, template_info, cv_analysis)

    def _start_standard_assessment(
        self, cv_text: str, template_info: Dict[str, Any], cv_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Start assessment using standard question generation."""
        try:
            logger.info("Starting assessment with AI-generated questions")
            # Generate questions using the question generation helper
            from ..tools.question_generation_tool import QuestionGenerationHelper

            question_helper = QuestionGenerationHelper()
            current_language = LanguageHandler.detect_language(cv_text)

            # Generate fallback questions since we don't have the old generate_initial_questions method
            questions = question_helper._generate_fallback_questions(
                cv_text, self.current_assessment_length, current_language
            )

            if questions:
                logger.info(f"Generated {len(questions)} AI questions for assessment")
                first_question = questions[0]
                formatted_question = LanguageHandler.format_question_with_count(
                    first_question, 1, self.current_assessment_length, current_language
                )

                qa_history = [{"question": first_question, "answer": ""}]

                return {
                    "name": cv_analysis.get("name"),
                    "email": cv_analysis.get("email"),
                    "question": first_question,
                    "user_message": formatted_question,
                    "remaining_questions": questions[1:],
                    "qa_history": qa_history,
                    "template_info": template_info,
                    "tag_based": False,
                }
            else:
                raise ValueError("No questions generated")

        except Exception as e:
            logger.error(f"Error in standard assessment start: {str(e)}")
            raise

    def _continue_assessment(
        self,
        cv_text: str,
        qa_history: List[Dict[str, Any]],
        cv_assessment_id: Optional[UUID],
        template_id: Optional[str],
        job_id: Optional[UUID],
    ) -> Dict[str, Any]:
        """Continue an existing assessment."""
        try:
            # Get template info from QA history if available
            template_info = None
            if qa_history and qa_history[0].get("template_info"):
                template_info = qa_history[0]["template_info"]

            # Extract tags from recent Q&A answers (if tag service available)
            if self.tag_extraction_service and cv_assessment_id and qa_history:
                logger.info("Extracting tags from Q&A answers")
                try:
                    # Update tags from recent answers
                    AsyncHelper.run_async_in_sync(
                        self._update_tags_from_answers,
                        cv_assessment_id,
                        qa_history,
                    )
                    logger.info("Q&A tag extraction completed successfully")

                    # Check if the last answer was invalid and has a follow-up question
                    last_qa = qa_history[-1] if qa_history else {}
                    if (
                        last_qa.get("answer")
                        and last_qa.get("answer_accepted") == False
                        and last_qa.get("follow_up_question")
                    ):
                        logger.info(
                            "Invalid answer detected, follow-up question will be presented in normal flow"
                        )
                        # Don't return here - let the normal flow handle the follow-up question
                        # The follow-up question is already stored in qa["follow_up_question"]
                        # and will be presented by the question generation logic

                except Exception as tag_error:
                    logger.warning(
                        f"Q&A tag extraction error (continuing with assessment): {str(tag_error)}"
                    )

            # Check if we started with the new QuestionGenerationService
            last_qa = qa_history[-1] if qa_history else {}

            # First priority: Check if there's a follow-up question for an invalid answer
            if (
                last_qa.get("answer")
                and last_qa.get("answer_accepted") == False
                and last_qa.get("follow_up_question")
            ):
                logger.info("Presenting follow-up question for invalid answer")

                # Format the follow-up question properly like other questions
                # For follow-up questions, count only accepted answers to get the current question number
                accepted_count = sum(
                    1 for qa in qa_history if qa.get("answer_accepted") == True
                )
                current_question_num = accepted_count + 1  # Next question number
                current_language = LanguageHandler.extract_language_from_qa_history(
                    qa_history
                )
                formatted_question = LanguageHandler.format_question_with_count(
                    last_qa["follow_up_question"],
                    current_question_num,
                    self.current_assessment_length,
                    current_language,
                )

                return {
                    "question": last_qa["follow_up_question"],
                    "user_message": formatted_question,
                    "question_type": "follow_up",
                    "original_question": last_qa.get("question", ""),
                    "feedback": last_qa.get("validation_result", {}).get(
                        "feedback", "Please provide a more specific answer."
                    ),
                    "answer_rejected": True,
                    "assessment_status": "continuing",
                    "qa_history": qa_history,
                }

            # If we have remaining questions from the new service, use them
            if last_qa.get("remaining_questions") and any(
                qa.get("tag_based") for qa in qa_history
            ):
                logger.info(
                    "Continuing with remaining questions from QuestionGenerationService"
                )
                return self._ask_next_question_from_list(qa_history, last_qa)

            # If we started with new service but no remaining questions, try to generate more
            if cv_assessment_id and any(qa.get("tag_based") for qa in qa_history):
                # Check if we've reached the question limit
                # Only count explicitly accepted answers
                answered_count = sum(
                    1
                    for qa in qa_history
                    if qa.get("answer") and qa.get("answer_accepted") == True
                )
                if answered_count >= self.current_assessment_length:
                    logger.info(
                        f"Assessment complete: {answered_count}/{self.current_assessment_length} questions answered"
                    )
                    return self.scoring_coordinator.generate_final_assessment(
                        cv_text,
                        qa_history,
                        template_info,
                        cv_assessment_id,
                        UUID(template_id) if template_id else None,
                        job_id,
                        self.scoring_engine_service,
                        self.cv_analyzer,
                    )

                logger.info("Using QuestionGenerationService for continuation")
                try:
                    from ..tools.question_generation_tool import (
                        QuestionGenerationService,
                    )

                    # Create service instance
                    db_service = (
                        self.tag_extraction_service.db_service
                        if self.tag_extraction_service
                        else None
                    )
                    question_service = QuestionGenerationService(
                        db_service, self.tag_extraction_service
                    )

                    # Calculate remaining questions needed
                    remaining_needed = max(
                        1, self.current_assessment_length - answered_count
                    )

                    # Generate more questions
                    question_result = AsyncHelper.safe_async_call(
                        question_service.generate_questions,
                        {"questions": [], "success": False},
                        cv_assessment_id,
                        job_description=None,
                        question_limit=remaining_needed,
                        category_weights=(
                            template_info.get("category_weights")
                            if template_info
                            else None
                        ),
                    )

                    if question_result.get("success", False) and question_result.get(
                        "questions"
                    ):
                        questions = [
                            q["question"] for q in question_result["questions"]
                        ]

                        # Ask the first new question
                        current_question_num = len(qa_history) + 1
                        first_question = questions[0]

                        current_language = (
                            LanguageHandler.extract_language_from_qa_history(qa_history)
                        )
                        formatted_question = LanguageHandler.format_question_with_count(
                            first_question,
                            current_question_num,
                            self.current_assessment_length,
                            current_language,
                        )

                        qa_history.append(
                            {
                                "question": first_question,
                                "answer": "",
                                "tag_based": True,
                            }
                        )

                        return {
                            "question": first_question,
                            "user_message": formatted_question,
                            "remaining_questions": questions[1:],
                            "qa_history": qa_history,
                            "template_info": template_info,
                            "tag_based": True,
                            "cv_assessment_id": str(cv_assessment_id),
                            "question_metadata": question_result.get("questions", []),
                        }

                except Exception as new_service_error:
                    logger.warning(
                        f"QuestionGenerationService continuation failed: {str(new_service_error)}"
                    )

            # Fall back to AI-generated questions for continuation
            try:
                logger.info("Continuing with AI-generated questions")
                return self._continue_standard_assessment(
                    cv_text, qa_history, template_info
                )
            except Exception as ai_error:
                logger.warning(
                    f"AI-generated question continuation failed: {str(ai_error)}"
                )

                # Fallback to tag-based assessment if available
                if self.tag_extraction_service and cv_assessment_id:
                    logger.info("Falling back to tag-based continuation")
                    return AsyncHelper.run_async_in_sync(
                        self._continue_tag_based_assessment,
                        cv_text,
                        qa_history,
                        cv_assessment_id,
                        template_info,
                        template_id,
                        job_id,
                    )
                else:
                    # No fallback available, re-raise the AI error
                    raise ai_error

        except Exception as e:
            logger.error(f"Error continuing assessment: {str(e)}")
            return self._generate_error_response(e)

    def _clean_qa_history(
        self, qa_history: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Clean up QA history by removing invalid answers and follow-up questions."""
        cleaned_history = []

        for qa in qa_history:
            # Only include questions with accepted answers
            if qa.get("answer_accepted") == True:
                # Create a clean copy without internal tracking fields
                clean_qa = {
                    "question": qa.get("question", ""),
                    "answer": qa.get("answer", ""),
                }
                cleaned_history.append(clean_qa)

        return cleaned_history

    async def _continue_tag_based_assessment(
        self,
        cv_text: str,
        qa_history: List[Dict[str, Any]],
        cv_assessment_id: UUID,
        template_info: Optional[Dict[str, Any]],
        template_id: Optional[str],
        job_id: Optional[UUID],
    ) -> Dict[str, Any]:
        """Continue assessment using tag-based logic."""
        try:
            # Check if assessment should be completed
            answered_count = sum(
                1
                for qa in qa_history
                if qa.get("answer") and qa.get("answer_accepted") == True
            )
            completion_result = (
                await self.tag_extraction_service.check_assessment_completion(
                    cv_assessment_id,
                    answered_count,
                    template_id=(
                        template_info.get("template_id") if template_info else None
                    ),
                    job_id=job_id,
                )
            )

            if completion_result.get("should_complete", False):
                logger.info(
                    f"Assessment completion triggered: {completion_result.get('reason', 'unknown')}"
                )
                return self.scoring_coordinator.generate_final_assessment(
                    cv_text,
                    qa_history,
                    template_info,
                    cv_assessment_id,
                    UUID(template_id) if template_id else None,
                    job_id,
                    self.scoring_engine_service,
                    self.cv_analyzer,
                )

            # Continue with more questions
            return await self._generate_next_question(
                cv_text,
                qa_history,
                cv_assessment_id,
                template_info,
                template_id,
                job_id,
            )

        except Exception as e:
            logger.error(f"Error in tag-based assessment continuation: {str(e)}")
            return self._continue_standard_assessment(
                cv_text, qa_history, template_info
            )

    def _continue_standard_assessment(
        self,
        cv_text: str,
        qa_history: List[Dict[str, Any]],
        template_info: Optional[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Continue assessment using standard logic."""
        try:
            # Check for confusion in the last answer
            last_qa = qa_history[-1] if qa_history else {}
            validation_result = last_qa.get("validation_result", {})

            # If the user didn't understand the question, rephrase it instead of moving on
            if validation_result.get("is_confusion", False) and validation_result.get(
                "follow_up_question"
            ):
                logger.info(
                    "User didn't understand the question - providing a rephrased version"
                )

                # Get the rephrased question
                rephrased_question = validation_result["follow_up_question"]

                # Keep the same question number
                current_question_num = len(qa_history)
                current_language = LanguageHandler.extract_language_from_qa_history(
                    qa_history
                )

                # Format the rephrased question with clear indication
                formatted_question = LanguageHandler.format_question_with_count(
                    rephrased_question,
                    current_question_num,
                    self.current_assessment_length,
                    current_language,
                )

                # Replace the last question with the rephrased one
                qa_history[-1] = {
                    "question": rephrased_question,
                    "answer": "",
                    "original_question": last_qa.get("question", ""),
                    "is_rephrased": True,
                }

                # Create clear rephrased message
                rephrased_message = f"Let me rephrase that question to make it clearer:\n\n{formatted_question}"

                return {
                    "question": rephrased_question,
                    "user_message": rephrased_message,
                    "remaining_questions": last_qa.get("remaining_questions", []),
                    "qa_history": qa_history,
                    "template_info": (
                        template_info if template_info else last_qa.get("template_info")
                    ),
                    "is_rephrased": True,
                }

            # Check remaining questions from last QA
            if last_qa.get("remaining_questions"):
                return self._ask_next_question_from_list(qa_history, last_qa)

            # Check if required Q&A count met
            answered_count = sum(
                1
                for qa in qa_history
                if qa.get("answer") and qa.get("answer_accepted") == True
            )
            if answered_count < self.current_assessment_length:
                return self._generate_followup_questions(
                    cv_text, qa_history, template_info
                )

            # All questions answered - provide final assessment
            return self.scoring_coordinator.generate_final_assessment(
                cv_text,
                qa_history,
                template_info,
                None,  # cv_assessment_id
                None,  # template_id
                None,  # job_id
                self.scoring_engine_service,
                self.cv_analyzer,
            )

        except Exception as e:
            logger.error(f"Error in standard assessment continuation: {str(e)}")
            return self._generate_error_response(e)

    async def _generate_next_question(
        self,
        cv_text: str,
        qa_history: List[Dict[str, Any]],
        cv_assessment_id: UUID,
        template_info: Optional[Dict[str, Any]],
        template_id: Optional[str],
        job_id: Optional[UUID],
    ) -> Dict[str, Any]:
        """Generate the next optimized question."""
        try:
            # Check if we have remaining questions from initial optimization
            last_qa = qa_history[-1] if qa_history else {}
            if last_qa.get("remaining_questions"):
                return self._ask_next_question_from_list(qa_history, last_qa)

            # Update tags from recent answers
            await self._update_tags_from_answers(cv_assessment_id, qa_history)

            # Generate new optimized questions
            answered_count = sum(
                1
                for qa in qa_history
                if qa.get("answer") and qa.get("answer_accepted") == True
            )
            remaining_length = max(1, self.current_assessment_length - answered_count)

            question_result = (
                await self.tag_extraction_service.get_questions_for_assessment(
                    cv_assessment_id,
                    template_id=(
                        template_info.get("template_id") if template_info else None
                    ),
                    job_id=job_id,
                    custom_length=remaining_length,
                )
            )

            questions_data = question_result.get("questions", [])
            if questions_data:
                questions = [q["question"] for q in questions_data]
                current_question_num = len(qa_history) + 1
                first_question = questions[0]

                current_language = LanguageHandler.extract_language_from_qa_history(
                    qa_history
                )
                formatted_question = LanguageHandler.format_question_with_count(
                    first_question,
                    current_question_num,
                    self.current_assessment_length,
                    current_language,
                )

                qa_history.append({"question": first_question, "answer": ""})

                result = {
                    "question": first_question,
                    "user_message": formatted_question,
                    "remaining_questions": questions[1:],
                    "qa_history": qa_history,
                    "tag_based": True,
                }

                if template_info:
                    result["template_info"] = template_info

                return result
            else:
                # No more optimized questions, complete assessment
                logger.info(
                    "No more optimized questions available, completing assessment"
                )
                return self.scoring_coordinator.generate_final_assessment(
                    cv_text,
                    qa_history,
                    template_info,
                    cv_assessment_id,
                    UUID(template_id) if template_id else None,
                    job_id,
                    self.scoring_engine_service,
                    self.cv_analyzer,
                )

        except Exception as e:
            logger.error(f"Error generating next question: {str(e)}")
            # Fallback to standard question generation
            return self._generate_followup_questions(cv_text, qa_history, template_info)

    def _ask_next_question_from_list(
        self, qa_history: List[Dict[str, Any]], last_qa: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Ask the next question from the remaining questions list."""
        next_question = last_qa["remaining_questions"][0]

        # Count only accepted answers to get the correct question number
        accepted_count = sum(
            1 for qa in qa_history if qa.get("answer_accepted") == True
        )
        current_question_num = accepted_count + 1

        current_language = LanguageHandler.extract_language_from_qa_history(qa_history)
        formatted_question = LanguageHandler.format_question_with_count(
            next_question,
            current_question_num,
            self.current_assessment_length,
            current_language,
        )

        # Preserve tag_based flag in the new QA entry
        new_qa_entry = {"question": next_question, "answer": ""}
        if last_qa.get("tag_based"):
            new_qa_entry["tag_based"] = True
        qa_history.append(new_qa_entry)

        result = {
            "question": next_question,
            "user_message": formatted_question,
            "remaining_questions": last_qa["remaining_questions"][1:],
            "qa_history": qa_history,
        }

        # Preserve existing metadata
        for key in ["template_info", "tag_based", "cv_assessment_id"]:
            if key in last_qa:
                result[key] = last_qa[key]

        return result

    def _generate_followup_questions(
        self,
        cv_text: str,
        qa_history: List[Dict[str, Any]],
        template_info: Optional[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Generate follow-up questions using AI."""
        try:
            logger.info("Generating follow-up questions using AI-generated approach")
            from ..tools.question_generation_tool import QuestionGenerationHelper

            question_helper = QuestionGenerationHelper()
            questions = question_helper.generate_followup_questions(
                cv_text, qa_history, template_info, count=1
            )

            if questions:
                current_question_num = len(qa_history) + 1
                current_language = LanguageHandler.extract_language_from_qa_history(
                    qa_history
                )

                first_question = questions[0]
                formatted_question = LanguageHandler.format_question_with_count(
                    first_question,
                    current_question_num,
                    self.current_assessment_length,
                    current_language,
                )

                qa_history.append({"question": first_question, "answer": ""})

                result = {
                    "question": first_question,
                    "user_message": formatted_question,
                    "remaining_questions": questions[1:] if len(questions) > 1 else [],
                    "qa_history": qa_history,
                }

                if template_info:
                    result["template_info"] = template_info

                return result
            else:
                # No more questions, complete assessment
                logger.info(
                    "No more AI-generated questions available, completing assessment"
                )
                return self.scoring_coordinator.generate_final_assessment(
                    cv_text,
                    qa_history,
                    template_info,
                    None,
                    None,
                    None,
                    self.scoring_engine_service,
                    self.cv_analyzer,
                )

        except Exception as e:
            logger.error(f"Error generating follow-up questions: {str(e)}")
            return self._generate_error_response(e)

    async def _update_tags_from_answers(
        self, cv_assessment_id: UUID, qa_history: List[Dict[str, Any]]
    ) -> None:
        """Update tags from recent answers using answer validation and tag extraction."""
        try:
            if not self.tag_extraction_service:
                return

            # Initialize the new answer validation tool
            try:
                from agents.cv_assessment_agent.tools.answer_validation_tool import (
                    AnswerValidationTool,
                )

                answer_validation_tool = AnswerValidationTool()
                logger.info("Answer validation tool initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing answer validation tool: {str(e)}")
                answer_validation_tool = None

            # Process only new answers that haven't been processed for tag extraction
            for qa in qa_history:
                if (
                    qa.get("answer")
                    and qa.get("answer").strip()
                    and not qa.get("tags_extracted", False)
                ):
                    answer_text = qa["answer"]
                    question_text = qa.get("question", "")
                    question_context = qa.get("question_metadata", {})

                    # If no specific metadata, create basic context
                    if not question_context:
                        question_context = {
                            "category": qa.get("category", "general"),
                            "tag_key": qa.get("tag_key", "general"),
                        }

                    # Use answer validation tool if available
                    if answer_validation_tool:
                        logger.info(f"Validating answer: {answer_text[:50]}...")
                        try:
                            # Validate the answer using the new tool
                            validation_result = (
                                await answer_validation_tool.validate_answer(
                                    question_text,
                                    answer_text,
                                    question_context,
                                )
                            )
                            logger.info("Answer validation completed successfully")

                            # Generate follow-up question if needed
                            if validation_result.get("needs_follow_up", False):
                                follow_up = await answer_validation_tool.generate_follow_up_question(
                                    question_text, answer_text, validation_result
                                )
                                if follow_up:
                                    validation_result["follow_up_question"] = follow_up

                        except Exception as validation_error:
                            logger.error(
                                f"Error during answer validation: {str(validation_error)}"
                            )
                            validation_result = {
                                "is_valid": True,  # Default to accepting the answer
                                "confidence_score": 0.5,
                                "feedback": "Error during validation",
                                "needs_follow_up": False,
                                "validation_complete": False,
                            }

                        # Store validation result in QA history
                        qa["validation_result"] = validation_result

                        # Special handling for confusion cases
                        if validation_result.get("is_confusion", False):
                            logger.warning(
                                f"Answer validation failed: {validation_result.get('feedback', 'User confusion detected')}"
                            )

                            # Don't mark as processed for tag extraction since we'll be rephrasing the question
                            continue

                        # If answer is valid, extract tags and count toward assessment
                        if validation_result.get("is_valid", False):
                            logger.info(
                                "Answer validated successfully, proceeding with tag extraction"
                            )

                            # Clear any previous follow-up question since we have a valid answer
                            if "follow_up_question" in qa:
                                del qa["follow_up_question"]

                            # Extract tags from the validated answer
                            logger.info(
                                f"Extracting tags from validated answer: {answer_text[:50]}..."
                            )
                            await self.tag_extraction_service.extract_tags_from_answer(
                                cv_assessment_id,
                                answer_text,
                                question_context=question_text,
                            )

                            # Mark as processed - this answer counts toward assessment
                            qa["tags_extracted"] = True
                            qa["answer_accepted"] = True
                            continue
                        else:
                            logger.warning(
                                f"Answer validation failed: {validation_result.get('feedback', 'Unknown reason')}"
                            )

                            # TASK 3.2 COMPLIANCE: Skip invalid answers completely
                            # Do NOT extract tags from invalid answers
                            # Do NOT count them toward assessment length
                            # Generate follow-up/rephrased question instead

                            qa["answer_accepted"] = False
                            qa["tags_extracted"] = False  # Not processed, doesn't count

                            # Generate follow-up or rephrased question
                            if validation_result.get("needs_follow_up", False):
                                follow_up = await answer_validation_tool.generate_follow_up_question(
                                    question_text, answer_text, validation_result
                                )
                                if follow_up:
                                    qa["follow_up_question"] = follow_up
                                    logger.info(
                                        f"Generated follow-up question for invalid answer: {follow_up[:50]}..."
                                    )

                            # Skip this invalid answer - it doesn't count toward assessment
                            continue

                    # Fallback to traditional tag extraction if validation failed or not available
                    logger.info(
                        f"Extracting tags from Q&A answer: {answer_text[:50]}..."
                    )
                    await self.tag_extraction_service.extract_tags_from_answer(
                        cv_assessment_id, answer_text, question_context=question_text
                    )

                    # Mark this answer as processed for tag extraction
                    qa["tags_extracted"] = True

        except Exception as e:
            logger.error(f"Error updating tags from answers: {str(e)}")

    def _get_assessment_length(
        self,
        cv_assessment_id: Optional[UUID],
        template_id: Optional[str],
        job_id: Optional[UUID],
    ) -> int:
        """Get the configured assessment length."""
        try:
            if self.tag_extraction_service and cv_assessment_id:
                return AsyncHelper.safe_async_call(
                    self.tag_extraction_service.get_assessment_length,
                    AssessmentConfig.get_default_assessment_length(),
                    cv_assessment_id,
                    template_id,
                    job_id,
                )
        except Exception as e:
            logger.error(f"Error getting assessment length: {str(e)}")

        return AssessmentConfig.get_default_assessment_length()

    def _select_template(
        self,
        cv_text: str,
        job_description: Optional[str],
        industry: Optional[str],
        experience_level: Optional[str],
        template_id: Optional[str],
        template_selection_tool,
    ) -> Dict[str, Any]:
        """Select the appropriate scoring template."""
        try:
            if template_selection_tool:
                # Use the template selection tool if available
                # Note: Tool only accepts job_id parameter for now
                template_info = AsyncHelper.safe_async_call(
                    template_selection_tool.coroutine,
                    self._get_default_template_info(),
                    job_id=str(template_id) if template_id else None,
                )

                if template_info.get("success") and "error" not in template_info:
                    # Convert tool response to expected format
                    weights = template_info.get("weights", {})
                    return {
                        "template_id": template_info.get("job_id", "default"),
                        "template_name": f"Template from {template_info.get('source', 'default')}",
                        "scoring_weights": weights,
                        "ai_percentage": 0.70,
                        "human_percentage": 0.30,
                        "category_weights": {},
                        "reason": f"Selected using {template_info.get('source', 'default')} weights",
                    }

            # Fallback to default template
            logger.warning("Template selection failed or unavailable, using defaults")
            return self._get_default_template_info()

        except Exception as e:
            logger.error(f"Error selecting template: {str(e)}")
            return self._get_default_template_info()

    def _get_default_template_info(self) -> Dict[str, Any]:
        """Get default template information."""
        return {
            "template_id": "default",
            "template_name": "Default Template",
            "scoring_weights": {
                "cv_analysis": 0.30,
                "questionnaire": 0.30,
                "case_study": 0.20,
                "psychometric": 0.10,
                "background_check": 0.10,
            },
            "ai_percentage": 0.70,
            "human_percentage": 0.30,
            "category_weights": {},
            "reason": "Using default template configuration",
        }

    def _generate_error_response(self, error: Exception) -> Dict[str, Any]:
        """Generate an error response for assessment failures."""
        return {
            "error": True,
            "message": "Assessment could not be completed due to a technical issue.",
            "user_message": "I apologize, but I encountered an error during the assessment. Please try again or contact support for assistance.",
            "details": str(error),
        }
