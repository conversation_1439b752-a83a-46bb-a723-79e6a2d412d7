"""
Job Service

Provides business logic for job management operations including:
- Basic job CRUD operations
- Template assignment to jobs
- Job-candidate matching configuration
- Search criteria persistence
"""

import logging
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime
from database import database_service
from database.models import Job, JobType, JobLevel, ScoringTemplate
from services.scoring_service import get_scoring_weights

logger = logging.getLogger("job_service")


class JobService:
    """Service class for job management operations"""

    def __init__(self, supabase_client):
        self.supabase_client = supabase_client

    def create_job(self, job_data: Dict[str, Any]) -> Optional[Job]:
        """
        Create a new job posting.

        Args:
            job_data: Dictionary containing job information

        Returns:
            Created Job object or None if failed
        """
        try:
            # Validate required fields
            if not job_data.get("title") or not job_data.get("description"):
                logger.error("Job title and description are required")
                return None

            # Set default values
            job_data.setdefault("is_active", True)
            job_data.setdefault("applications_count", 0)
            job_data.setdefault("created_at", datetime.now().isoformat())

            # Create job in database
            created_job = database_service.create_job(self.supabase_client, job_data)

            if created_job:
                logger.info(f"Created job: {created_job.get('id')}")
                return Job(**created_job)

            return None

        except Exception as e:
            logger.error(f"Error creating job: {e}")
            return None

    def get_job(self, job_id: UUID) -> Optional[Job]:
        """
        Get a single job by ID.

        Args:
            job_id: Job UUID

        Returns:
            Job object or None if not found
        """
        try:
            jobs = database_service.get_jobs(self.supabase_client, active_only=False)

            for job_data in jobs:
                if job_data.get("id") == str(job_id):
                    return Job(**job_data)

            return None

        except Exception as e:
            logger.error(f"Error retrieving job {job_id}: {e}")
            return None

    def get_jobs(
        self,
        active_only: bool = True,
        recruiter_id: Optional[UUID] = None,
        limit: Optional[int] = None,
    ) -> List[Job]:
        """
        Get multiple jobs with optional filters.

        Args:
            active_only: Whether to return only active jobs
            recruiter_id: Filter by recruiter
            limit: Maximum number of jobs to return

        Returns:
            List of Job objects
        """
        try:
            jobs_data = database_service.get_jobs(
                self.supabase_client,
                active_only=active_only,
                recruiter_id=str(recruiter_id) if recruiter_id else None,
                limit=limit,
            )

            return [Job(**job_data) for job_data in jobs_data]

        except Exception as e:
            logger.error(f"Error retrieving jobs: {e}")
            return []

    def update_job(self, job_id: UUID, update_data: Dict[str, Any]) -> bool:
        """
        Update a job posting.

        Args:
            job_id: Job UUID
            update_data: Dictionary of fields to update

        Returns:
            True if successful, False otherwise
        """
        try:
            # Add updated timestamp
            update_data["updated_at"] = datetime.now().isoformat()

            success = database_service.update_job(
                self.supabase_client, str(job_id), update_data
            )

            if success:
                logger.info(f"Updated job: {job_id}")

            return success

        except Exception as e:
            logger.error(f"Error updating job {job_id}: {e}")
            return False

    def delete_job(self, job_id: UUID) -> bool:
        """
        Soft delete a job by setting is_active to False.

        Args:
            job_id: Job UUID

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.update_job(
                job_id, {"is_active": False, "closed_at": datetime.now().isoformat()}
            )

        except Exception as e:
            logger.error(f"Error deleting job {job_id}: {e}")
            return False

    def assign_template(self, job_id: UUID, template_id: UUID) -> bool:
        """
        Assign a scoring template to a job.

        Args:
            job_id: Job UUID
            template_id: Template UUID

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.update_job(job_id, {"template_id": str(template_id)})

        except Exception as e:
            logger.error(f"Error assigning template {template_id} to job {job_id}: {e}")
            return False

    def set_custom_weights(
        self, job_id: UUID, custom_weights: Dict[str, float]
    ) -> bool:
        """
        Set custom scoring weights for a job (overrides template weights).

        Args:
            job_id: Job UUID
            custom_weights: Dictionary of custom weights

        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate weights sum to 1.0
            total_weight = sum(custom_weights.values())
            if abs(total_weight - 1.0) > 0.01:  # Allow small floating point errors
                logger.error(f"Custom weights must sum to 1.0, got {total_weight}")
                return False

            return self.update_job(job_id, {"custom_weights": custom_weights})

        except Exception as e:
            logger.error(f"Error setting custom weights for job {job_id}: {e}")
            return False

    def get_job_scoring_weights(self, job_id: UUID) -> Dict[str, float]:
        """
        Get the effective scoring weights for a job.
        Uses job's custom weights, template weights, or default weights in that order.

        Args:
            job_id: Job UUID

        Returns:
            Dictionary of scoring weights
        """
        try:
            return get_scoring_weights(self.supabase_client, job_id)

        except Exception as e:
            logger.error(f"Error getting scoring weights for job {job_id}: {e}")
            return {}

    def set_assessment_length(self, job_id: UUID, assessment_length: int) -> bool:
        """
        Set custom assessment length for a job.

        Args:
            job_id: Job UUID
            assessment_length: Number of questions (1-50)

        Returns:
            True if successful, False otherwise
        """
        try:
            if not (1 <= assessment_length <= 50):
                logger.error("Assessment length must be between 1 and 50")
                return False

            return self.update_job(job_id, {"assessment_length": assessment_length})

        except Exception as e:
            logger.error(f"Error setting assessment length for job {job_id}: {e}")
            return False

    def increment_applications(self, job_id: UUID) -> bool:
        """
        Increment the applications count for a job.

        Args:
            job_id: Job UUID

        Returns:
            True if successful, False otherwise
        """
        try:
            return database_service.increment_job_applications(
                self.supabase_client, str(job_id)
            )

        except Exception as e:
            logger.error(f"Error incrementing applications for job {job_id}: {e}")
            return False

    def get_jobs_by_template(self, template_id: UUID) -> List[Job]:
        """
        Get all jobs using a specific template.

        Args:
            template_id: Template UUID

        Returns:
            List of Job objects
        """
        try:
            all_jobs = self.get_jobs(active_only=False)
            return [job for job in all_jobs if job.template_id == template_id]

        except Exception as e:
            logger.error(f"Error getting jobs by template {template_id}: {e}")
            return []

    def search_jobs(
        self,
        query: Optional[str] = None,
        job_type: Optional[JobType] = None,
        experience_level: Optional[JobLevel] = None,
        location: Optional[str] = None,
        company: Optional[str] = None,
    ) -> List[Job]:
        """
        Search jobs with various filters.

        Args:
            query: Text search in title/description
            job_type: Filter by job type
            experience_level: Filter by experience level
            location: Filter by location
            company: Filter by company

        Returns:
            List of matching Job objects
        """
        try:
            jobs = self.get_jobs(active_only=True)

            # Apply filters
            if query:
                query_lower = query.lower()
                jobs = [
                    job
                    for job in jobs
                    if (
                        query_lower in job.title.lower()
                        or (job.description and query_lower in job.description.lower())
                    )
                ]

            if job_type:
                jobs = [job for job in jobs if job.job_type == job_type]

            if experience_level:
                jobs = [job for job in jobs if job.experience_level == experience_level]

            if location:
                location_lower = location.lower()
                jobs = [
                    job
                    for job in jobs
                    if job.location and location_lower in job.location.lower()
                ]

            if company:
                company_lower = company.lower()
                jobs = [
                    job
                    for job in jobs
                    if job.company and company_lower in job.company.lower()
                ]

            return jobs

        except Exception as e:
            logger.error(f"Error searching jobs: {e}")
            return []


# Convenience functions for backward compatibility
def create_job_service(supabase_client) -> JobService:
    """Create a JobService instance"""
    return JobService(supabase_client)


def get_job_by_id(supabase_client, job_id: UUID) -> Optional[Job]:
    """Get a job by ID (convenience function)"""
    service = JobService(supabase_client)
    return service.get_job(job_id)


def get_active_jobs(supabase_client, limit: Optional[int] = None) -> List[Job]:
    """Get active jobs (convenience function)"""
    service = JobService(supabase_client)
    return service.get_jobs(active_only=True, limit=limit)
