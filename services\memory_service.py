"""
Memory Service Module

This module provides memory implementations for the chatbot.
It includes a SupabaseMemory class that stores conversation history in Supabase.
"""

from langchain.memory.chat_memory import BaseChatMemory
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from typing import List, Dict, Any, Optional
import logging

# Configure logging
logger = logging.getLogger("memory_service")


class SupabaseMemory(BaseChatMemory):
    """
    Custom memory implementation that stores chat history in Supabase.
    Extends LangChain's BaseChatMemory for persistent storage.
    """

    conversation_id: str
    supabase_client: Any
    return_messages: bool = True
    max_token_limit: int = 2000
    max_message_limit: int = 15  # Increased from 5 to 15 messages

    @property
    def memory_variables(self) -> List[str]:
        """Define the memory variables used by this memory implementation."""
        return ["chat_history"]

    def load_memory_variables(self, inputs: Dict) -> Dict:
        """
        Load chat history from Supabase for the current conversation.

        Args:
            inputs: Dictionary of input values (unused)

        Returns:
            Dict containing chat history as <PERSON><PERSON><PERSON><PERSON> messages
        """
        try:
            # Get all messages for conversation
            messages_result = (
                self.supabase_client.table("messages")
                .select("role, content")
                .eq("session_id", self.conversation_id)
                .order("created_at")
                .execute()
            )

            # Convert to LangChain message format
            chat_history = []
            for msg in messages_result.data:
                if msg["role"] == "human" or msg["role"] == "user":
                    chat_history.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    chat_history.append(AIMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    chat_history.append(SystemMessage(content=msg["content"]))

            # Apply limits if needed
            if self.max_message_limit and len(chat_history) > self.max_message_limit:
                # Keep only the most recent messages
                chat_history = chat_history[-self.max_message_limit :]

            return {"chat_history": chat_history}
        except Exception as e:
            logger.error(f"Error loading memory variables: {e}")
            return {"chat_history": []}

    def save_context(self, inputs: Dict, outputs: Dict) -> None:
        """
        Save the conversation context to Supabase.

        Note: This is disabled to prevent duplicate saving since message_processor
        handles saving messages to the database.

        Args:
            inputs: Dictionary containing the human input
            outputs: Dictionary containing the AI output
        """
        # Disabled to prevent duplicate saving
        # Messages are saved by message_processor.py instead
        pass

    def clear(self) -> None:
        """
        Delete all messages for the current conversation.
        """
        try:
            if self.conversation_id:
                self.supabase_client.table("messages").delete().eq(
                    "session_id", self.conversation_id
                ).execute()
        except Exception as e:
            logger.error(f"Error clearing memory: {e}")
