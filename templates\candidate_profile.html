<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Candidate Profile - Testing Interface</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1,
      h2,
      h3 {
        color: #333;
      }
      .nav-links {
        text-align: center;
        margin-bottom: 20px;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 500;
      }
      .nav-links a:hover {
        text-decoration: underline;
      }
      .profile-header {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 30px;
        align-items: start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
      }
      .profile-info h1 {
        margin: 0 0 10px 0;
        font-size: 28px;
      }
      .profile-meta {
        color: #666;
        font-size: 16px;
        line-height: 1.6;
      }
      .profile-scores {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
      }
      .score-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        border: 2px solid #e9ecef;
      }
      .score-value {
        font-size: 24px;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 5px;
      }
      .score-label {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        font-weight: 600;
      }
      .profile-section {
        margin-bottom: 30px;
      }
      .section-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
        border-left: 4px solid #007bff;
        padding-left: 15px;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
      }
      .info-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
      }
      .info-label {
        font-weight: 600;
        color: #666;
        font-size: 14px;
        margin-bottom: 5px;
      }
      .info-value {
        color: #333;
        font-size: 16px;
      }
      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
      }
      .tag {
        background: #007bff;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .tag-category {
        background: #28a745;
      }
      .tag-skill {
        background: #ffc107;
        color: #333;
      }
      .tag-certification {
        background: #dc3545;
      }
      .summary-box {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }
      .qa-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin: 15px 0;
      }
      .question {
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
      }
      .answer {
        color: #555;
        line-height: 1.6;
        padding-left: 15px;
        border-left: 3px solid #007bff;
      }
      .loading {
        text-align: center;
        padding: 50px;
        color: #666;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 20px;
        border-radius: 6px;
        text-align: center;
      }
      .search-form {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #555;
      }
      input,
      select {
        width: 100%;
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        box-sizing: border-box;
      }
      button {
        background: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
      }
      button:hover {
        background: #0056b3;
      }
      .similar-candidates {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
        margin-top: 20px;
      }
      .similar-card {
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
      }
      .similar-name {
        font-weight: 600;
        margin-bottom: 10px;
      }
      .similar-score {
        color: #007bff;
        font-weight: 600;
        font-size: 14px;
      }
      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }
      .status-complete {
        background: #28a745;
      }
      .status-incomplete {
        background: #ffc107;
      }
      .status-missing {
        background: #dc3545;
      }
    </style>
  </head>
  <body>
    <div class="nav-links">
      <a href="/candidate-search">Candidate Search</a>
      <a href="/job-creation">Job Creation</a>
      <a href="/cv-assessments/">CV Assessments</a>
    </div>

    <div class="container">
      <h1>👤 Candidate Profile</h1>

      <div class="search-form">
        <div class="form-group">
          <label for="candidateId">Candidate ID or Assessment ID:</label>
          <input
            type="text"
            id="candidateId"
            placeholder="Enter candidate ID or CV assessment ID"
          />
        </div>
        <button onclick="loadCandidate()">Load Profile</button>
      </div>
    </div>

    <div id="loading" class="container loading" style="display: none">
      <p>🔍 Loading candidate profile...</p>
    </div>

    <div id="error" class="container error" style="display: none"></div>

    <div id="profileContainer" style="display: none">
      <!-- Profile Header -->
      <div class="container">
        <div class="profile-header">
          <div class="profile-info">
            <h1 id="candidateName">Loading...</h1>
            <div class="profile-meta">
              <div id="candidateLocation"></div>
              <div id="candidateExperience"></div>
              <div id="candidateRole"></div>
              <div id="candidateContact"></div>
            </div>
          </div>
          <div class="profile-scores" id="profileScores">
            <!-- Scores will be populated here -->
          </div>
        </div>
      </div>

      <!-- Basic Information -->
      <div class="container">
        <div class="profile-section">
          <h2 class="section-title">Basic Information</h2>
          <div class="info-grid" id="basicInfo">
            <!-- Basic info will be populated here -->
          </div>
        </div>
      </div>

      <!-- Skills & Competencies -->
      <div class="container">
        <div class="profile-section">
          <h2 class="section-title">Skills & Competencies</h2>
          <div id="skillsSection">
            <!-- Skills will be populated here -->
          </div>
        </div>
      </div>

      <!-- Assessment Status -->
      <div class="container">
        <div class="profile-section">
          <h2 class="section-title">Assessment Status</h2>
          <div id="assessmentStatus">
            <!-- Assessment status will be populated here -->
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentCandidateId = null;

      async function loadCandidate() {
        const candidateId = document.getElementById("candidateId").value.trim();
        if (!candidateId) {
          alert("Please enter a candidate ID or assessment ID");
          return;
        }

        const loading = document.getElementById("loading");
        const error = document.getElementById("error");
        const profileContainer = document.getElementById("profileContainer");

        // Show loading, hide others
        loading.style.display = "block";
        error.style.display = "none";
        profileContainer.style.display = "none";

        try {
          // Try to get candidate by ID first
          let response = await fetch(`/candidates/${candidateId}`);

          if (!response.ok) {
            // If not found, try to get by assessment ID
            const assessmentResponse = await fetch(
              `/api/cv-assessments/${candidateId}`
            );
            if (!assessmentResponse.ok) {
              const errorText = await assessmentResponse.text();
              throw new Error(`Assessment not found with ID: ${candidateId}`);
            }

            const contentType = assessmentResponse.headers.get("content-type");
            if (!contentType || !contentType.includes("application/json")) {
              const errorText = await assessmentResponse.text();
              throw new Error(
                `Invalid response format. Expected JSON but got HTML error page.`
              );
            }

            const assessmentData = await assessmentResponse.json();
            // Use assessment data to build profile
            displayAssessmentProfile(assessmentData);
          } else {
            const contentType = response.headers.get("content-type");
            if (!contentType || !contentType.includes("application/json")) {
              const errorText = await response.text();
              throw new Error(
                `Invalid response format. Expected JSON but got HTML error page.`
              );
            }

            const responseData = await response.json();
            const candidateData = responseData.candidate || responseData;
            displayCandidateProfile(candidateData);
          }

          currentCandidateId = candidateId;
          profileContainer.style.display = "block";
        } catch (err) {
          console.error("Load candidate error:", err);
          error.textContent = `Failed to load candidate: ${err.message}`;
          error.style.display = "block";
        } finally {
          loading.style.display = "none";
        }
      }

      function displayScores(data) {
        const scoresContainer = document.getElementById("profileScores");
        let html = "";

        // Overall AI Score
        if (data.ai_total_score !== undefined) {
          html += `
            <div class="score-card">
                <div class="score-value">${data.ai_total_score}%</div>
                <div class="score-label">AI Score</div>
            </div>
          `;
        }

        // Assessment Completion
        if (data.assessment_completion_percentage !== undefined) {
          html += `
            <div class="score-card">
                <div class="score-value">${data.assessment_completion_percentage}%</div>
                <div class="score-label">Completion</div>
            </div>
          `;
        }

        scoresContainer.innerHTML = html || "<p>No scores available</p>";
      }

      function displayBasicInfo(data) {
        const basicInfoContainer = document.getElementById("basicInfo");
        let html = "";

        // Always show all fields, even if empty, in the order from the user's example, except Profile Text Length and Profile Version
        const infoItems = [
          { label: "Email", value: data.email },
          { label: "Current Role", value: data.current_role },
          { label: "Experience Level", value: data.experience_level },
          { label: "Location", value: data.location_city && data.location_country ? `${data.location_city}, ${data.location_country}` : (data.location_city || data.location_country || "") },
          { label: "Degree Level", value: data.degree_level },
          { label: "Major Field", value: data.major_field },
          { label: "GPA", value: data.gpa },
          { label: "Industry Primary", value: data.industry_primary },
          { label: "Industry Secondary", value: data.industry_secondary },
          { label: "Current Salary Range", value: data.current_salary_range },
          { label: "Expected Salary Range", value: data.expected_salary_range },
          { label: "Currency", value: data.currency },
          { label: "Remote Work Preference", value: data.remote_work_preference },
          { label: "Relocation Willingness", value: data.relocation_willingness === false ? "No" : (data.relocation_willingness === true ? "Yes" : "N/A") },
          { label: "Visa Sponsorship Needed", value: data.visa_sponsorship_needed === false ? "No" : (data.visa_sponsorship_needed === true ? "Yes" : "N/A") },
          { label: "Profile Created", value: data.created_at ? new Date(data.created_at).toLocaleDateString() : "" },
          { label: "Last Active", value: data.last_active ? new Date(data.last_active).toLocaleDateString() : "" }
        ];

        infoItems.forEach((item) => {
          let val = item.value;
          if (val === undefined || val === null || val === "") val = "N/A";
          html += `
            <div class="info-item">
                <div class="info-label">${item.label}</div>
                <div class="info-value">${val}</div>
            </div>
          `;
        });

        basicInfoContainer.innerHTML = html;
      }

      function displayBasicInfoFromAssessment(data) {
        // For now, just call displayBasicInfo
        displayBasicInfo(data);
      }

      function displayCandidateProfile(data) {
        // Update header
        document.getElementById("candidateName").textContent =
          data.candidate_name || data.name || "Unknown Candidate";
        document.getElementById("candidateLocation").textContent = data.location_city && data.location_country
          ? `📍 ${data.location_city}, ${data.location_country}`
          : data.location_city
          ? `📍 ${data.location_city}`
          : "";
        document.getElementById("candidateExperience").textContent =
          data.total_years_experience
            ? `💼 ${data.total_years_experience} years experience`
            : "";
        document.getElementById("candidateRole").textContent = data.current_role
          ? `🎯 ${data.current_role}`
          : "";
        document.getElementById("candidateContact").textContent = data.email
          ? `📧 ${data.email}`
          : "";

        // Update scores
        displayScores(data);

        // No summary section to update

        // Update basic info
        displayBasicInfo(data);

        // Update skills
        displaySkills(data);

        // Update assessment status
        displayAssessmentStatus(data);
      }

      function displayAssessmentProfile(data) {
        // Just call displayCandidateProfile for now
        displayCandidateProfile(data);
      }

      function displaySkills(data) {
        const skillsContainer = document.getElementById("skillsSection");
        let html = "";

        // Technical Skills
        if (data.technical_skills) {
          try {
            const technicalSkills = JSON.parse(data.technical_skills);
            if (technicalSkills && technicalSkills.length > 0) {
              html += '<h3>Technical Skills</h3><div class="tags-container">';
              technicalSkills.forEach((skill) => {
                html += `<span class="tag tag-skill">${skill}</span>`;
              });
              html += "</div>";
            }
          } catch (e) {
            console.error("Error parsing technical skills:", e);
          }
        }

        // Soft Skills
        if (data.soft_skills) {
          try {
            const softSkills = JSON.parse(data.soft_skills);
            if (softSkills && softSkills.length > 0) {
              html += '<h3>Soft Skills</h3><div class="tags-container">';
              softSkills.forEach((skill) => {
                html += `<span class="tag tag-skill">${skill}</span>`;
              });
              html += "</div>";
            }
          } catch (e) {
            console.error("Error parsing soft skills:", e);
          }
        }

        // Key Skills
        if (data.key_skills) {
          try {
            const keySkills = JSON.parse(data.key_skills);
            if (keySkills && keySkills.length > 0) {
              html += '<h3>Key Skills</h3><div class="tags-container">';
              keySkills.forEach((skill) => {
                html += `<span class="tag tag-skill">${skill}</span>`;
              });
              html += "</div>";
            }
          } catch (e) {
            console.error("Error parsing key skills:", e);
          }
        }

        // Languages
        if (data.languages_spoken) {
          try {
            const languages = JSON.parse(data.languages_spoken);
            if (languages && languages.length > 0) {
              html += '<h3>Languages</h3><div class="tags-container">';
              languages.forEach((language) => {
                html += `<span class="tag tag-category">${language}</span>`;
              });
              html += "</div>";
            }
          } catch (e) {
            console.error("Error parsing languages:", e);
          }
        }

        // Certifications
        if (data.certifications) {
          try {
            const certifications = JSON.parse(data.certifications);
            if (certifications && certifications.length > 0) {
              html += '<h3>Certifications</h3><div class="tags-container">';
              certifications.forEach((cert) => {
                html += `<span class="tag tag-certification">${cert}</span>`;
              });
              html += "</div>";
            }
          } catch (e) {
            console.error("Error parsing certifications:", e);
          }
        }

        skillsContainer.innerHTML =
          html || "<p>No skills or competencies available</p>";
      }

      function displayAssessmentStatus(data) {
        const statusContainer = document.getElementById("assessmentStatus");
        let html = "";

        const statusItems = [
          { label: "Has CV", value: data.has_cv, status: data.has_cv ? "complete" : "missing" },
          { label: "Has Photo", value: data.has_photo, status: data.has_photo ? "complete" : "missing" },
          { label: "Has Portfolio", value: data.has_portfolio, status: data.has_portfolio ? "complete" : "missing" },
          { label: "Has Q&A Responses", value: data.has_qa_responses, status: data.has_qa_responses ? "complete" : "missing" },
          { label: "Education Tags", value: data.has_education_tags, status: data.has_education_tags ? "complete" : "missing" },
          { label: "Experience Tags", value: data.has_experience_tags, status: data.has_experience_tags ? "complete" : "missing" },
          { label: "Skills Tags", value: data.has_skills_tags, status: data.has_skills_tags ? "complete" : "missing" },
          { label: "Achievements Tags", value: data.has_achievements_tags, status: data.has_achievements_tags ? "complete" : "missing" },
          { label: "Personal Tags", value: data.has_personal_tags, status: data.has_personal_tags ? "complete" : "missing" },
          { label: "Cultural Tags", value: data.has_cultural_tags, status: data.has_cultural_tags ? "complete" : "missing" },
        ];

        html += '<div class="info-grid">';
        statusItems.forEach((item) => {
          const statusClass = item.status === "complete" ? "status-complete" : 
                             item.status === "incomplete" ? "status-incomplete" : "status-missing";
          html += `
            <div class="info-item">
                <div class="info-label">
                    <span class="status-indicator ${statusClass}"></span>
                    ${item.label}
                </div>
                <div class="info-value">${item.value ? "Yes" : "No"}</div>
            </div>
          `;
        });
        html += "</div>";

        statusContainer.innerHTML = html || "<p>No assessment status available</p>";
      }

      // Load candidate from URL parameter if provided
      window.addEventListener("load", function () {
        const urlParams = new URLSearchParams(window.location.search);
        const candidateId = urlParams.get("id");
        const pathId = window.location.pathname.split("/").pop();

        // Use URL parameter, path parameter, or default
        const idToLoad =
          candidateId ||
          (pathId !== "candidate-profile" &&
          pathId !== "candidate-profile-viewer"
            ? pathId
            : null);

        if (
          idToLoad &&
          idToLoad !== "candidate-profile" &&
          idToLoad !== "candidate-profile-viewer"
        ) {
          document.getElementById("candidateId").value = idToLoad;
          loadCandidate();
        }
      });
    </script>
  </body>
</html>
