"""
Assessment Configuration Module

Centralized configuration for CV assessment parameters.
This module provides a single source of truth for assessment configuration.
"""

# Default assessment length - the only place this value should be defined
DEFAULT_ASSESSMENT_LENGTH = 15


class AssessmentConfig:
    """Centralized configuration for CV assessments."""

    @staticmethod
    def get_default_assessment_length() -> int:
        """
        Get the default assessment length.

        Returns:
            The default number of questions for an assessment
        """
        return DEFAULT_ASSESSMENT_LENGTH

    @staticmethod
    def get_assessment_length_from_job(job_data: dict) -> int:
        """
        Get assessment length from job configuration.

        Job settings override system defaults.

        Args:
            job_data: Job configuration data

        Returns:
            The configured assessment length
        """
        # Job-specific assessment length overrides default
        if job_data and job_data.get("assessment_length"):
            return job_data["assessment_length"]

        # Default fallback
        return DEFAULT_ASSESSMENT_LENGTH
