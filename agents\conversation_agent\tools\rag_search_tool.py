from langchain.tools import tool
import logging
from services import rag_service

logger = logging.getLogger("rag_search_tool")


def create_tool(agent):
    """Return a LangChain tool for RAG search bound to the given agent instance."""

    @tool
    def rag_search(question: str, language: str = "English") -> str:
        """Search the knowledge base for answers to user questions.

        Args:
            question: The user's question to search for.
            language: The language for the response (default: English).

        Returns:
            Answer from the knowledge base or an error message.
        """
        try:
            # Get the RAG chain (fresh instance per invocation)
            rag_chain = rag_service.get_rag_chain(agent.supabase_client)

            if not rag_chain:
                return (
                    "I'm sorry, I can't access our knowledge base at the moment. "
                    "Please try again later."
                )

            # Invoke the chain
            response = rag_chain.invoke(
                {
                    "question": question,
                    "session_id": agent.session_id,
                    "language": language,
                }
            )

            return (
                response
                if response
                else "I couldn't find specific information about that. Could you please rephrase your question?"
            )

        except Exception as exc:
            logger.error("Error in rag_search: %s", exc)
            return (
                "I'm having trouble accessing our information system. "
                "Please try again or contact our support team."
            )

    return rag_search
