"""
Database Service Module

This module provides functionality for database operations related to conversations,
messages, and leads. It abstracts the database operations from the main application code.
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

# Configure logging
logger = logging.getLogger("database_service")


def create_or_update_conversation(
    supabase_client, session_id: str, host_name: str = None, language: str = "English"
) -> bool:
    """
    Create a new conversation or update an existing one.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        host_name: The host name (optional)
        language: The language preference (default: English)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Check if conversation exists
        conversation = (
            supabase_client.table("conversations")
            .select("*")
            .eq("session_id", session_id)
            .execute()
        )

        # If conversation doesn't exist, create it
        if len(conversation.data) == 0:
            # Create new conversation with host information and default language
            supabase_client.table("conversations").insert(
                {
                    "session_id": session_id,
                    "host_name": host_name,
                    "language": language,
                }
            ).execute()

            logger.info(f"Created new conversation for session {session_id}")
            return True
        else:
            # Update last_accessed timestamp
            supabase_client.table("conversations").update(
                {
                    "last_accessed": datetime.now().isoformat(),
                    "host_name": host_name,  # Update host name if it changes
                }
            ).eq("session_id", session_id).execute()

            logger.info(f"Updated existing conversation for session {session_id}")
            return True
    except Exception as e:
        logger.error(f"Error creating or updating conversation: {e}")
        return False


def add_system_message(supabase_client, session_id: str, content: str) -> bool:
    """
    Add a system message to the conversation.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        content: The message content

    Returns:
        True if successful, False otherwise
    """
    try:
        supabase_client.table("messages").insert(
            {
                "session_id": session_id,
                "role": "system",
                "content": content,
            }
        ).execute()

        logger.info(f"Added system message for session {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error adding system message: {e}")
        return False


def add_user_message(supabase_client, session_id: str, content: str) -> bool:
    """
    Add a user message to the conversation.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        content: The message content

    Returns:
        True if successful, False otherwise
    """
    try:
        supabase_client.table("messages").insert(
            {
                "session_id": session_id,
                "role": "user",
                "content": content,
            }
        ).execute()

        logger.info(f"Added user message for session {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error adding user message: {e}")
        return False


def add_assistant_message(supabase_client, session_id: str, content: str) -> bool:
    """
    Add an assistant message to the conversation.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        content: The message content

    Returns:
        True if successful, False otherwise
    """
    try:
        supabase_client.table("messages").insert(
            {
                "session_id": session_id,
                "role": "assistant",
                "content": content,
            }
        ).execute()

        logger.info(f"Added assistant message for session {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error adding assistant message: {e}")
        return False


def get_conversation_history(
    supabase_client, session_id: str, limit: int = None
) -> List[Dict[str, Any]]:
    """
    Get the conversation history for a session.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        limit: Maximum number of messages to retrieve (optional)

    Returns:
        List of messages in the conversation
    """
    try:
        query = (
            supabase_client.table("messages")
            .select("role, content, created_at")
            .eq("session_id", session_id)
            .order("created_at", desc=False)
        )

        if limit:
            query = query.limit(limit)

        result = query.execute()

        if result.data:
            return result.data
        else:
            return []
    except Exception as e:
        logger.error(f"Error retrieving conversation history: {e}")
        return []


def get_conversation_messages_as_tuples(
    supabase_client, session_id: str, limit: int = None
) -> List[Tuple[str, str]]:
    """
    Get the conversation history for a session as a list of (role, content) tuples.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        limit: Maximum number of messages to retrieve (optional)

    Returns:
        List of (role, content) tuples
    """
    messages = get_conversation_history(supabase_client, session_id, limit)
    return [
        (msg["role"], msg["content"]) for msg in messages if msg["role"] != "system"
    ]


def get_all_cv_assessments(supabase_client) -> List[Dict[str, Any]]:
    """
    Get all CV assessment records.

    Args:
        supabase_client: Initialized Supabase client

    Returns:
        List of CV assessment records
    """
    try:
        result = (
            supabase_client.table("cv_assessments")
            .select("*")
            .order("created_at", desc=True)
            .execute()
        )

        if result.data:
            return result.data
        else:
            return []
    except Exception as e:
        logger.error(f"Error retrieving all CV assessments: {e}")
        return []


def get_cv_assessment_by_id(
    supabase_client, assessment_id: int
) -> Optional[Dict[str, Any]]:
    """
    Get a single CV assessment by its ID.

    Args:
        supabase_client: Initialized Supabase client
        assessment_id: The ID of the CV assessment

    Returns:
        A dictionary representing the CV assessment, or None if not found
    """
    try:
        result = (
            supabase_client.table("cv_assessments")
            .select("*")
            .eq("id", assessment_id)
            .single()
            .execute()
        )

        if result.data:
            return result.data
        else:
            return None
    except Exception as e:
        logger.error(f"Error retrieving CV assessment by ID {assessment_id}: {e}")
        return None


# =====================================================
# PHASE 2 CRUD OPERATIONS - HIERARCHICAL TAG SYSTEM
# =====================================================


def get_all_tag_categories(
    supabase_client, active_only: bool = True
) -> List[Dict[str, Any]]:
    """
    Get all tag categories.

    Args:
        supabase_client: Initialized Supabase client
        active_only: Whether to return only active categories

    Returns:
        List of tag categories
    """
    try:
        query = supabase_client.table("tag_categories").select("*")

        if active_only:
            query = query.eq("is_active", True)

        result = query.order("priority", desc=False).execute()

        return result.data if result.data else []
    except Exception as e:
        logger.error(f"Error retrieving tag categories: {e}")
        return []


def get_tag_definitions_by_category(
    supabase_client, category_id: str
) -> List[Dict[str, Any]]:
    """
    Get all tag definitions for a specific category.

    Args:
        supabase_client: Initialized Supabase client
        category_id: The category UUID

    Returns:
        List of tag definitions
    """
    try:
        result = (
            supabase_client.table("tag_definitions")
            .select("*")
            .eq("category_id", category_id)
            .order("tag_key", desc=False)
            .execute()
        )

        return result.data if result.data else []
    except Exception as e:
        logger.error(
            f"Error retrieving tag definitions for category {category_id}: {e}"
        )
        return []


def create_candidate_tag(
    supabase_client,
    cv_assessment_id: str,
    tag_definition_id: str,
    value: str,
    source: str,
    confidence_score: float = 1.0,
) -> Optional[Dict[str, Any]]:
    """
    Create a new candidate tag or update existing one.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID
        tag_definition_id: The tag definition UUID
        value: The tag value
        source: The extraction source (cv, question, manual, inference)
        confidence_score: The confidence score (0-1)

    Returns:
        The created/updated tag record or None if failed
    """
    try:
        # First check if tag already exists
        existing = (
            supabase_client.table("candidate_tags")
            .select("*, tag_definitions(*)")
            .eq("cv_assessment_id", cv_assessment_id)
            .eq("tag_definition_id", tag_definition_id)
            .execute()
        )

        if existing.data:
            # Update existing tag with new value if different or higher confidence
            existing_tag = existing.data[0]
            existing_value = existing_tag["value"]
            existing_confidence = existing_tag["confidence_score"]
            existing_source = existing_tag["source"]

            # Get tag definition to understand data type
            tag_definition = existing_tag.get("tag_definitions", {})
            data_type = (
                tag_definition.get("data_type", "text") if tag_definition else "text"
            )

            # Determine if we should update based on improved logic
            should_update = False
            new_value = value
            new_source = source
            new_confidence = confidence_score

            # For list data types, append new values instead of overriding
            if data_type == "list" and value and value != "null":
                if existing_value:
                    # Split existing values and new values
                    existing_values = [
                        v.strip() for v in existing_value.split(",") if v.strip()
                    ]
                    new_values = [v.strip() for v in value.split(",") if v.strip()]

                    # Combine unique values
                    combined_values = list(set(existing_values + new_values))
                    new_value = ", ".join(combined_values)

                    # Only update if we actually added new values
                    should_update = len(combined_values) > len(existing_values)

                    # For list updates, create combined source when data comes from different sources
                    if should_update:
                        # Create combined source when data comes from different sources
                        if existing_source != source:
                            if existing_source == "cv" and source == "question":
                                new_source = "cv + questions"
                            elif existing_source == "question" and source == "cv":
                                new_source = "cv + questions"
                            elif existing_source == "cv + questions" and source in [
                                "cv",
                                "question",
                            ]:
                                new_source = "cv + questions"  # Already combined
                            elif source == "cv + questions" and existing_source in [
                                "cv",
                                "question",
                            ]:
                                new_source = "cv + questions"
                            else:
                                # For other source combinations, use the more reliable source
                                source_priority = {
                                    "cv": 4,
                                    "question": 3,
                                    "manual": 2,
                                    "inference": 1,
                                }
                                existing_priority = source_priority.get(
                                    existing_source, 0
                                )
                                new_priority = source_priority.get(source, 0)

                                if new_priority > existing_priority:
                                    new_source = source
                                else:
                                    new_source = existing_source
                        else:
                            # Same source, keep it
                            new_source = existing_source
                else:
                    # No existing value, so update with new value
                    should_update = True
            else:
                # For non-list types, implement smart TEXT combination logic
                if value and value != "null":
                    # Always combine TEXT values, never override
                    existing_has_commas = (
                        "," in existing_value if existing_value else False
                    )
                    new_has_commas = "," in value if value else False

                    # If both contain comma-separated values, combine them
                    if existing_has_commas and new_has_commas:
                        # Split existing and new values
                        existing_items = [
                            item.strip()
                            for item in existing_value.split(",")
                            if item.strip()
                        ]
                        new_items = [
                            item.strip() for item in value.split(",") if item.strip()
                        ]

                        # Combine unique items
                        combined_items = list(set(existing_items + new_items))
                        new_value = ", ".join(combined_items)

                        # Always update when combining
                        should_update = True
                        logger.info(
                            f"Combining comma-separated TEXT values: '{existing_value}' + '{value}' = '{new_value}'"
                        )

                    elif existing_has_commas:
                        # Existing has commas, new is single value - append to existing
                        existing_items = [
                            item.strip()
                            for item in existing_value.split(",")
                            if item.strip()
                        ]
                        if value.strip() not in existing_items:
                            existing_items.append(value.strip())
                            new_value = ", ".join(existing_items)
                            should_update = True
                            logger.info(
                                f"Appending single value to comma-separated TEXT: '{existing_value}' + '{value}' = '{new_value}'"
                            )
                        else:
                            # Value already exists, no update needed
                            should_update = False
                            new_value = existing_value

                    elif new_has_commas:
                        # New has commas, existing is single value - combine both
                        new_items = [
                            item.strip() for item in value.split(",") if item.strip()
                        ]
                        if existing_value.strip() not in new_items:
                            new_items.append(existing_value.strip())
                            new_value = ", ".join(new_items)
                            should_update = True
                            logger.info(
                                f"Combining single existing value with comma-separated new value: '{existing_value}' + '{value}' = '{new_value}'"
                            )
                        else:
                            # Existing value already in new list, use new value
                            new_value = value
                            should_update = True

                    else:
                        # Both are single values - combine them
                        if existing_value.strip() != value.strip():
                            new_value = f"{existing_value}, {value}"
                            should_update = True
                            logger.info(
                                f"Combining single TEXT values: '{existing_value}' + '{value}' = '{new_value}'"
                            )
                        else:
                            # Values are identical, no update needed
                            should_update = False
                            new_value = existing_value

                    # Handle source tracking with combined sources
                    if existing_source != source:
                        # Create combined source when data comes from different sources
                        if existing_source == "cv" and source == "question":
                            new_source = "cv + questions"
                        elif existing_source == "question" and source == "cv":
                            new_source = "cv + questions"
                        elif existing_source == "cv + questions" and source in [
                            "cv",
                            "question",
                        ]:
                            new_source = "cv + questions"  # Already combined
                        elif source == "cv + questions" and existing_source in [
                            "cv",
                            "question",
                        ]:
                            new_source = "cv + questions"
                        else:
                            # For other source combinations, use the more reliable source
                            source_priority = {
                                "cv": 4,
                                "question": 3,
                                "manual": 2,
                                "inference": 1,
                            }
                            existing_priority = source_priority.get(existing_source, 0)
                            new_priority = source_priority.get(source, 0)

                            # Only change source if new source is more reliable
                            if new_priority > existing_priority:
                                new_source = source
                            else:
                                new_source = existing_source
                    else:
                        # Same source, keep it
                        new_source = existing_source
                else:
                    # Don't update if new value is null/empty
                    should_update = False

            if should_update:
                result = (
                    supabase_client.table("candidate_tags")
                    .update(
                        {
                            "value": new_value,
                            "source": new_source,
                            "confidence_score": new_confidence,
                        }
                    )
                    .eq("id", existing_tag["id"])
                    .execute()
                )
                return result.data[0] if result.data else existing_tag
            else:
                # Return existing tag without updating
                return existing_tag
        else:
            # Only create new tag if value is not null/empty
            if value and value != "null":
                result = (
                    supabase_client.table("candidate_tags")
                    .insert(
                        {
                            "cv_assessment_id": cv_assessment_id,
                            "tag_definition_id": tag_definition_id,
                            "value": value,
                            "source": source,
                            "confidence_score": confidence_score,
                        }
                    )
                    .execute()
                )
                return result.data[0] if result.data else None
            else:
                # Don't create tag with null/empty value
                return None

    except Exception as e:
        logger.error(f"Error creating/updating candidate tag: {e}")
        return None


def batch_create_candidate_tags(
    supabase_client, tags_data: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    OPTIMIZED: Create multiple candidate tags in a single batch operation.
    This dramatically reduces database overhead from individual INSERTs.

    Args:
        supabase_client: Initialized Supabase client
        tags_data: List of tag dictionaries with keys:
                  - cv_assessment_id: str
                  - tag_definition_id: str
                  - value: str
                  - source: str
                  - confidence_score: float

    Returns:
        List of created tag records
    """
    try:
        if not tags_data:
            return []

        logger.info(f"Batch creating {len(tags_data)} candidate tags")

        # Use upsert to handle conflicts (update existing or insert new)
        result = (
            supabase_client.table("candidate_tags")
            .upsert(
                tags_data,
                on_conflict="cv_assessment_id,tag_definition_id",
                returning="*",
            )
            .execute()
        )

        created_count = len(result.data) if result.data else 0
        logger.info(
            f"Successfully batch created/updated {created_count} candidate tags"
        )

        return result.data if result.data else []

    except Exception as e:
        logger.error(f"Error in batch creating candidate tags: {e}")
        # Return empty list to allow fallback to individual inserts
        return []


def batch_update_tag_coverage(
    supabase_client, coverage_updates: List[Dict[str, Any]]
) -> bool:
    """
    OPTIMIZED: Update multiple tag coverage records in a single batch operation.
    This dramatically reduces database overhead from individual UPDATEs.

    Args:
        supabase_client: Initialized Supabase client
        coverage_updates: List of coverage dictionaries with keys:
                         - cv_assessment_id: str
                         - category_id: str
                         - total_tags: int
                         - filled_tags: int
                         - threshold_percentage: float
                         - coverage_percentage: float
                         - is_complete: bool

    Returns:
        True if successful, False otherwise
    """
    try:
        if not coverage_updates:
            return True

        logger.info(f"Batch updating coverage for {len(coverage_updates)} categories")

        # Use upsert to handle both inserts and updates
        result = (
            supabase_client.table("candidate_tag_coverage")
            .upsert(
                coverage_updates,
                on_conflict="cv_assessment_id,category_id",
                returning="*",
            )
            .execute()
        )

        updated_count = len(result.data) if result.data else 0
        logger.info(f"Successfully batch updated {updated_count} coverage records")

        return True

    except Exception as e:
        logger.error(f"Error in batch updating tag coverage: {e}")
        return False


def update_candidate_tag(
    supabase_client, tag_id: str, value: str = None, confidence_score: float = None
) -> bool:
    """
    Update an existing candidate tag.

    Args:
        supabase_client: Initialized Supabase client
        tag_id: The tag UUID
        value: New value (optional)
        confidence_score: New confidence score (optional)

    Returns:
        True if successful, False otherwise
    """
    try:
        update_data = {}
        if value is not None:
            update_data["value"] = value
        if confidence_score is not None:
            update_data["confidence_score"] = confidence_score

        if update_data:
            supabase_client.table("candidate_tags").update(update_data).eq(
                "id", tag_id
            ).execute()

        return True
    except Exception as e:
        logger.error(f"Error updating candidate tag {tag_id}: {e}")
        return False


def get_candidate_tags(supabase_client, cv_assessment_id: str) -> List[Dict[str, Any]]:
    """
    Get all tags for a specific CV assessment.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID

    Returns:
        List of candidate tags with tag definitions
    """
    try:
        result = (
            supabase_client.table("candidate_tags")
            .select("*, tag_definitions(*, category:tag_categories(*))")
            .eq("cv_assessment_id", cv_assessment_id)
            .execute()
        )

        return result.data if result.data else []
    except Exception as e:
        logger.error(
            f"Error retrieving candidate tags for assessment {cv_assessment_id}: {e}"
        )
        return []


def update_tag_coverage(
    supabase_client,
    cv_assessment_id: str,
    category_id: str,
    total_tags: int,
    filled_tags: int,
    threshold_percentage: float = 70.0,
) -> bool:
    """
    Update or create tag coverage for a category in an assessment.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID
        category_id: The category UUID
        total_tags: Total number of tags in category
        filled_tags: Number of filled tags
        threshold_percentage: Required coverage percentage

    Returns:
        True if successful, False otherwise
    """
    try:
        coverage_percentage = (filled_tags / total_tags * 100) if total_tags > 0 else 0
        is_complete = coverage_percentage >= threshold_percentage

        # Try to update existing record
        existing = (
            supabase_client.table("candidate_tag_coverage")
            .select("id")
            .eq("cv_assessment_id", cv_assessment_id)
            .eq("category_id", category_id)
            .execute()
        )

        if existing.data:
            # Update existing
            supabase_client.table("candidate_tag_coverage").update(
                {
                    "total_tags": total_tags,
                    "filled_tags": filled_tags,
                    "coverage_percentage": coverage_percentage,
                    "threshold_percentage": threshold_percentage,
                    "is_complete": is_complete,
                    "last_calculated": datetime.now().isoformat(),
                }
            ).eq("id", existing.data[0]["id"]).execute()
        else:
            # Create new
            supabase_client.table("candidate_tag_coverage").insert(
                {
                    "cv_assessment_id": cv_assessment_id,
                    "category_id": category_id,
                    "total_tags": total_tags,
                    "filled_tags": filled_tags,
                    "coverage_percentage": coverage_percentage,
                    "threshold_percentage": threshold_percentage,
                    "is_complete": is_complete,
                }
            ).execute()

        return True
    except Exception as e:
        logger.error(f"Error updating tag coverage: {e}")
        return False


def get_tag_coverage(supabase_client, cv_assessment_id: str) -> List[Dict[str, Any]]:
    """
    Get tag coverage for all categories in an assessment.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID

    Returns:
        List of coverage records with category information
    """
    try:
        result = (
            supabase_client.table("candidate_tag_coverage")
            .select("*, tag_categories(*)")
            .eq("cv_assessment_id", cv_assessment_id)
            .execute()
        )

        return result.data if result.data else []
    except Exception as e:
        logger.error(
            f"Error retrieving tag coverage for assessment {cv_assessment_id}: {e}"
        )
        return []


# =====================================================
# PHASE 2 CRUD OPERATIONS - SCORING TEMPLATES
# =====================================================


def create_scoring_template(
    supabase_client, template_data: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """
    Create a new scoring template.

    Args:
        supabase_client: Initialized Supabase client
        template_data: Dictionary containing template data

    Returns:
        The created template or None if failed
    """
    try:
        result = (
            supabase_client.table("scoring_templates").insert(template_data).execute()
        )
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error creating scoring template: {e}")
        return None


def get_scoring_templates(
    supabase_client,
    active_only: bool = True,
    industry: str = None,
    job_level: str = None,
) -> List[Dict[str, Any]]:
    """
    Get scoring templates with optional filters.

    Args:
        supabase_client: Initialized Supabase client
        active_only: Whether to return only active templates
        industry: Filter by industry
        job_level: Filter by job level

    Returns:
        List of scoring templates
    """
    try:
        query = supabase_client.table("scoring_templates").select("*")

        if active_only:
            query = query.eq("is_active", True)
        if industry:
            query = query.eq("industry", industry)
        if job_level:
            query = query.eq("job_level", job_level)

        result = query.order("created_at", desc=True).execute()

        return result.data if result.data else []
    except Exception as e:
        logger.error(f"Error retrieving scoring templates: {e}")
        return []


def get_default_scoring_template(supabase_client) -> Optional[Dict[str, Any]]:
    """
    Get the default scoring template.

    Args:
        supabase_client: Initialized Supabase client

    Returns:
        The default template or None if not found
    """
    try:
        result = (
            supabase_client.table("scoring_templates")
            .select("*")
            .eq("is_default", True)
            .eq("is_active", True)
            .single()
            .execute()
        )

        return result.data if result.data else None
    except Exception as e:
        logger.error(f"Error retrieving default scoring template: {e}")
        return None


def update_scoring_template(
    supabase_client, template_id: str, update_data: Dict[str, Any]
) -> bool:
    """
    Update a scoring template.

    Args:
        supabase_client: Initialized Supabase client
        template_id: The template UUID
        update_data: Dictionary of fields to update

    Returns:
        True if successful, False otherwise
    """
    try:
        supabase_client.table("scoring_templates").update(update_data).eq(
            "id", template_id
        ).execute()
        return True
    except Exception as e:
        logger.error(f"Error updating scoring template {template_id}: {e}")
        return False


# =====================================================
# PHASE 2 CRUD OPERATIONS - JOBS
# =====================================================


def create_job(supabase_client, job_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Create a new job posting.

    Args:
        supabase_client: Initialized Supabase client
        job_data: Dictionary containing job data

    Returns:
        The created job or None if failed
    """
    try:
        result = supabase_client.table("jobs").insert(job_data).execute()
        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error creating job: {e}")
        return None


def get_jobs(
    supabase_client,
    active_only: bool = True,
    recruiter_id: str = None,
    limit: int = None,
) -> List[Dict[str, Any]]:
    """
    Get job postings with optional filters.

    Args:
        supabase_client: Initialized Supabase client
        active_only: Whether to return only active jobs
        recruiter_id: Filter by recruiter
        limit: Maximum number of jobs to return

    Returns:
        List of job postings
    """
    try:
        query = supabase_client.table("jobs").select(
            "*, scoring_templates(*), recruiters(*)"
        )

        if active_only:
            query = query.eq("is_active", True)
        if recruiter_id:
            query = query.eq("recruiter_id", recruiter_id)
        if limit:
            query = query.limit(limit)

        result = query.order("created_at", desc=True).execute()

        return result.data if result.data else []
    except Exception as e:
        logger.error(f"Error retrieving jobs: {e}")
        return []


def update_job(supabase_client, job_id: str, update_data: Dict[str, Any]) -> bool:
    """
    Update a job posting.

    Args:
        supabase_client: Initialized Supabase client
        job_id: The job UUID
        update_data: Dictionary of fields to update

    Returns:
        True if successful, False otherwise
    """
    try:
        supabase_client.table("jobs").update(update_data).eq("id", job_id).execute()
        return True
    except Exception as e:
        logger.error(f"Error updating job {job_id}: {e}")
        return False


def increment_job_applications(supabase_client, job_id: str) -> bool:
    """
    Increment the applications count for a job.

    Args:
        supabase_client: Initialized Supabase client
        job_id: The job UUID

    Returns:
        True if successful, False otherwise
    """
    try:
        # Get current count
        result = (
            supabase_client.table("jobs")
            .select("applications_count")
            .eq("id", job_id)
            .single()
            .execute()
        )

        if result.data:
            current_count = result.data.get("applications_count", 0)
            supabase_client.table("jobs").update(
                {"applications_count": current_count + 1}
            ).eq("id", job_id).execute()

        return True
    except Exception as e:
        logger.error(f"Error incrementing job applications for {job_id}: {e}")
        return False


# =====================================================
# PHASE 2 CRUD OPERATIONS - RECRUITERS
# =====================================================


def create_recruiter(
    supabase_client, recruiter_data: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """
    Create a new recruiter account.

    Args:
        supabase_client: Initialized Supabase client
        recruiter_data: Dictionary containing recruiter data

    Returns:
        The created recruiter (without password_hash) or None if failed
    """
    try:
        result = supabase_client.table("recruiters").insert(recruiter_data).execute()

        if result.data:
            # Remove password_hash from response
            recruiter = result.data[0]
            recruiter.pop("password_hash", None)
            return recruiter
        return None
    except Exception as e:
        logger.error(f"Error creating recruiter: {e}")
        return None


def get_recruiter_by_email(supabase_client, email: str) -> Optional[Dict[str, Any]]:
    """
    Get a recruiter by email address.

    Args:
        supabase_client: Initialized Supabase client
        email: The recruiter's email

    Returns:
        The recruiter record or None if not found
    """
    try:
        result = (
            supabase_client.table("recruiters")
            .select("*")
            .eq("email", email)
            .single()
            .execute()
        )

        return result.data if result.data else None
    except Exception as e:
        logger.error(f"Error retrieving recruiter by email {email}: {e}")
        return None


def update_recruiter_last_login(supabase_client, recruiter_id: str) -> bool:
    """
    Update the last login timestamp for a recruiter.

    Args:
        supabase_client: Initialized Supabase client
        recruiter_id: The recruiter UUID

    Returns:
        True if successful, False otherwise
    """
    try:
        supabase_client.table("recruiters").update(
            {"last_login": datetime.now().isoformat()}
        ).eq("id", recruiter_id).execute()

        return True
    except Exception as e:
        logger.error(f"Error updating recruiter last login: {e}")
        return False


# =====================================================
# PHASE 2 CRUD OPERATIONS - CANDIDATE SCORES
# =====================================================


def create_or_update_candidate_score(
    supabase_client, cv_assessment_id: str, score_data: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """
    Create or update a candidate score record.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID
        score_data: Dictionary containing score data

    Returns:
        The created/updated score record or None if failed
    """
    try:
        # Check if score exists
        existing = (
            supabase_client.table("candidate_scores")
            .select("id")
            .eq("cv_assessment_id", cv_assessment_id)
            .execute()
        )

        score_data["cv_assessment_id"] = cv_assessment_id

        if existing.data:
            # Update existing
            result = (
                supabase_client.table("candidate_scores")
                .update(score_data)
                .eq("id", existing.data[0]["id"])
                .execute()
            )
        else:
            # Create new
            result = (
                supabase_client.table("candidate_scores").insert(score_data).execute()
            )

        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error creating/updating candidate score: {e}")
        return None


def get_candidate_score(
    supabase_client, cv_assessment_id: str
) -> Optional[Dict[str, Any]]:
    """
    Get the candidate score for an assessment.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID

    Returns:
        The score record or None if not found
    """
    try:
        result = (
            supabase_client.table("candidate_scores")
            .select("*")
            .eq("cv_assessment_id", cv_assessment_id)
            .execute()
        )

        # Return first result if exists, None otherwise
        return result.data[0] if result.data and len(result.data) > 0 else None
    except Exception as e:
        logger.error(f"Error retrieving candidate score: {e}")
        return None


# =====================================================
# PHASE 2 CRUD OPERATIONS - HUMAN ASSESSMENTS
# =====================================================


def create_or_update_human_assessment(
    supabase_client,
    cv_assessment_id: str,
    recruiter_id: str,
    assessment_data: Dict[str, Any],
) -> Optional[Dict[str, Any]]:
    """
    Create or update a human assessment.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID
        recruiter_id: The recruiter UUID
        assessment_data: Dictionary containing assessment data

    Returns:
        The created/updated assessment or None if failed
    """
    try:
        # Check if assessment exists
        existing = (
            supabase_client.table("human_assessments")
            .select("id")
            .eq("cv_assessment_id", cv_assessment_id)
            .execute()
        )

        assessment_data["cv_assessment_id"] = cv_assessment_id
        assessment_data["recruiter_id"] = recruiter_id

        if existing.data:
            # Update existing
            result = (
                supabase_client.table("human_assessments")
                .update(assessment_data)
                .eq("id", existing.data[0]["id"])
                .execute()
            )
        else:
            # Create new
            result = (
                supabase_client.table("human_assessments")
                .insert(assessment_data)
                .execute()
            )

        return result.data[0] if result.data else None
    except Exception as e:
        logger.error(f"Error creating/updating human assessment: {e}")
        return None


def get_human_assessment(
    supabase_client, cv_assessment_id: str
) -> Optional[Dict[str, Any]]:
    """
    Get the human assessment for a CV assessment.

    Args:
        supabase_client: Initialized Supabase client
        cv_assessment_id: The CV assessment UUID

    Returns:
        The assessment record or None if not found
    """
    try:
        result = (
            supabase_client.table("human_assessments")
            .select("*, recruiters(*)")
            .eq("cv_assessment_id", cv_assessment_id)
            .single()
            .execute()
        )

        return result.data if result.data else None
    except Exception as e:
        logger.error(f"Error retrieving human assessment: {e}")
        return None
