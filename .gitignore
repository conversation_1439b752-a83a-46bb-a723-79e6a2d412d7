# Environment variables
.env
.env.local
.env.development
.env.production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/

# Virtual environments
venv/
ENV/
env/
.venv/
.env/
.virtualenv/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/
*.sublime-workspace
*.sublime-project

# OS specific
.DS_Store
Thumbs.db
.directory
desktop.ini

# Logs
logs/
*.log
npm-debug.log*

# Temporary files
*.tmp
*.bak
*.backup
*~
*.pdf

# Local development
.python-version
.node-version
.nvmrc

# Database
*.sqlite3
*.db
