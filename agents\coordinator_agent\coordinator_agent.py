"""Coordinator Agent Module

Routes incoming requests to appropriate specialized agents based on the request type.
This agent acts as the entry point for all requests and determines which specialized
agent should handle each request.
"""

from __future__ import annotations

import logging
from typing import Dict, Any, Optional
import os

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>plate, MessagesPlaceholder
from langchain_openai import Chat<PERSON>penA<PERSON>

from services.memory_service import SupabaseMemory

logger = logging.getLogger("coordinator_agent")


class CoordinatorAgent:
    """Coordinator agent that routes requests to appropriate specialized agents."""

    def __init__(
        self,
        model_name: str = "gpt-4.1",
        host_domain: str | None = None,
        session_id: str | None = None,
        supabase_client=None,
        db_service=None,
    ) -> None:
        # Language model
        self.model = ChatOpenAI(model=model_name, temperature=0.3)

        # Context
        self.host_domain = host_domain or os.environ.get("HOST_DOMAIN")
        self.session_id = session_id
        self.supabase_client = supabase_client
        self.db_service = db_service

        # Initialize specialized agents lazily
        self._conversation_agent = None
        self._cv_assessment_agent = None

        # Memory for context
        self.memory = (
            SupabaseMemory(
                conversation_id=session_id,
                supabase_client=supabase_client,
                max_message_limit=5,  # Less history needed for routing
            )
            if session_id and supabase_client
            else None
        )

        # Tools
        self.tools = self._create_tools()

        # Agent executor
        self.agent = self._create_agent()

    def _create_tools(self):
        """Create tools for the coordinator agent."""
        from agents.coordinator_agent.tools.agent_selection_tool import create_tool

        return [create_tool(self)]

    def _create_agent(self):
        """Create the coordinator agent with routing logic."""

        system_prompt = """
        You are a request router that determines which specialized agent should handle each request.
        
        Your job is to analyze the user's message and route it to the appropriate agent:
        
        1. **CV Assessment Agent**: Route here if:
           - User mentions uploading or has uploaded a CV/resume
           - User asks about CV assessment or evaluation
           - User wants their CV analyzed or scored
           - User is responding to CV assessment questions
           - There's an ongoing CV assessment session
           
        2. **Conversation Agent**: Route here for ALL other requests including:
           - General questions about services or company
           - Lead capture (collecting name, email, phone)
           - Information requests
           - Small talk or greetings
           - Any non-CV related queries
           
        IMPORTANT: 
        - You must use the agent_selection tool to route the request to the appropriate agent.
        - The tool will execute the request and return the response from the selected agent.
        - Do not provide your own response - let the tool handle the routing and execution.
        
        When using the tool, provide:
        - agent_type: Either "cv_assessment" or "conversation"
        - reason: Brief explanation of why you chose this agent
        """

        if self.memory:
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    MessagesPlaceholder(variable_name="chat_history"),
                    ("human", "{input}"),
                    MessagesPlaceholder(variable_name="agent_scratchpad"),
                ]
            )
        else:
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    ("human", "{input}"),
                    MessagesPlaceholder(variable_name="agent_scratchpad"),
                ]
            )

        agent = create_tool_calling_agent(self.model, self.tools, prompt)

        if self.memory:
            return AgentExecutor(
                agent=agent,
                tools=self.tools,
                memory=self.memory,
                verbose=False,  # Disable verbose mode
                return_intermediate_steps=False,
            )
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=False,  # Disable verbose mode
            return_intermediate_steps=False,
        )

    @property
    def conversation_agent(self):
        """Lazy initialization of conversation agent."""
        if self._conversation_agent is None:
            from agents.conversation_agent.conversation_agent import ConversationAgent

            self._conversation_agent = ConversationAgent(
                session_id=self.session_id,
                supabase_client=self.supabase_client,
                host_domain=self.host_domain,
            )
        return self._conversation_agent

    @property
    def cv_assessment_agent(self):
        """Lazy initialization of CV assessment agent."""
        if self._cv_assessment_agent is None:
            from agents.cv_assessment_agent.cv_assessment_agent import CVAssessmentAgent

            self._cv_assessment_agent = CVAssessmentAgent(
                session_id=self.session_id,
                supabase_client=self.supabase_client,
                host_domain=self.host_domain,
                db_service=self.db_service,
            )
        return self._cv_assessment_agent

    def process_message(self, message: str, cv_text: str = None) -> str:
        """Process a message by routing to the appropriate agent."""
        try:
            # Store the current message for the tool to access
            self._current_message = message

            # Add CV context if provided
            if cv_text:
                routing_message = f"[User has uploaded a CV]\n{message}"
            else:
                routing_message = message

            # Let the coordinator decide which agent to use
            result = self.agent.invoke({"input": routing_message})

            # The agent_selection tool will have been called and returned the response
            return result.get(
                "output", "I apologize, but I couldn't process your request."
            )

        except Exception as exc:
            logger.error("CoordinatorAgent error: %s", exc, exc_info=True)
            # Fallback to conversation agent
            return self.conversation_agent.process_message(message)

    def run_cv_assessment(
        self,
        cv_text: str,
        qa_history: list | None = None,
        job_description: Optional[str] = None,
        industry: Optional[str] = None,
        template_id: Optional[str] = None,
        cv_assessment_id: Optional[str] = None,
        job_id: Optional[str] = None,
    ):
        """
        Direct CV assessment request - routes to CV assessment agent.

        The CV Assessment Agent automatically:
        - Analyzes the CV content
        - Selects the best matching template
        - Uses template weights for scoring
        - Generates appropriate questions
        """
        return self.cv_assessment_agent.run_cv_assessment(
            cv_text=cv_text,
            qa_history=qa_history or [],
            job_description=job_description,
            industry=industry,
            template_id=template_id,
            cv_assessment_id=cv_assessment_id,
            job_id=job_id,
        )
