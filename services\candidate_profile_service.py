"""
Candidate Profile Service Module

Handles generation of candidate profiles from CV and Q&A data, creates embeddings,
and manages candidate profile storage for semantic search functionality.
"""

import logging
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from datetime import datetime
from uuid import UUID, uuid4

from langchain_openai import Chat<PERSON>penAI, OpenAIEmbeddings
from database.models import (
    CVAssessment,
    CandidateTag,
    CandidateTagCoverage,
    TagCategory,
    TagDefinition,
    JobLevel,
    CandidateScore,
)
from database import database_service
from database.database_service_wrapper import DatabaseService
from utils.assessment_config import AssessmentConfig
from services.candidate_vector_service import get_candidate_vector_service

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("candidate_profile_service")


class CandidateProfileService:
    """Service for generating and managing candidate profiles for semantic search"""

    def __init__(self, db_service, model_name: str = "gpt-4.1"):
        """Initialize the candidate profile service"""
        self.db_service = db_service
        self.model = ChatOpenAI(model=model_name, temperature=0.3, max_tokens=2000)
        self.embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
        self.vector_service = get_candidate_vector_service()

    async def generate_candidate_profile(
        self, cv_assessment_id: int, force_regenerate: bool = False
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive candidate profile from CV and Q&A data

        Args:
            cv_assessment_id: ID of the CV assessment
            force_regenerate: Whether to regenerate even if profile exists

        Returns:
            Dictionary containing profile data and generation status
        """
        try:
            logger.info(
                f"Generating candidate profile for assessment {cv_assessment_id}"
            )

            # Check if profile already exists (unless force_regenerate is True)
            if not force_regenerate:
                existing_profile = await self.get_candidate_by_assessment_id(
                    str(cv_assessment_id)
                )
                if existing_profile:
                    logger.info(
                        f"Profile already exists for assessment {cv_assessment_id}, skipping generation"
                    )
                    return {
                        "status": "skipped",
                        "message": "Profile already exists",
                        "cv_assessment_id": cv_assessment_id,
                        "existing_profile": existing_profile,
                    }

            # Get CV assessment data
            cv_assessment_data = self.db_service.get_cv_assessment_by_id(
                cv_assessment_id
            )
            if not cv_assessment_data:
                raise ValueError(f"CV assessment {cv_assessment_id} not found")

            # Convert to CVAssessment model
            cv_assessment = CVAssessment(**cv_assessment_data)

            # Get candidate tags (work with raw data to avoid model conversion issues)
            candidate_tags_data = self.db_service.get_candidate_tags(
                str(cv_assessment_id)
            )

            # Convert to models for other methods that need them
            candidate_tags = [
                CandidateTag(**tag_data) for tag_data in candidate_tags_data
            ]

            # Get Q&A responses from assessment state
            qa_responses = self._extract_qa_responses(cv_assessment.assessment_state)

            # Use AI to extract metadata from raw tags data
            extracted_info = await self._ai_extract_metadata_from_raw_data(
                candidate_tags_data, cv_assessment
            )

            # Generate profile text
            profile_text = await self._generate_profile_text(
                cv_assessment, candidate_tags, qa_responses
            )

            # Generate professional summary
            professional_summary = await self._generate_professional_summary(
                cv_assessment, candidate_tags, qa_responses
            )

            # Generate embedding
            embedding = await self._generate_embedding(profile_text)

            # Create profile metadata with AI extracted info
            metadata = self._create_profile_metadata(
                cv_assessment, candidate_tags, extracted_info
            )

            # Add summary to metadata
            metadata["summary"] = professional_summary

            # Store in vector database
            candidate_id = metadata["candidate_id"]
            vector_stored = await self.vector_service.store_candidate_profile(
                candidate_id, profile_text, metadata
            )

            result = {
                "success": True,
                "cv_assessment_id": cv_assessment_id,
                "candidate_id": candidate_id,
                "profile_text": profile_text,
                "embedding": embedding,
                "metadata": metadata,
                "profile_length": len(profile_text),
                "completeness_score": self._calculate_completeness_score(
                    candidate_tags
                ),
                "vector_stored": vector_stored,
                "generation_time": datetime.utcnow().isoformat(),
            }

            logger.info(
                f"Successfully generated and stored profile for assessment {cv_assessment_id}"
            )
            return result

        except Exception as e:
            logger.error(f"Error generating candidate profile: {str(e)}")
            return {
                "success": False,
                "error_message": str(e),
                "cv_assessment_id": cv_assessment_id,
            }

    async def _generate_profile_text(
        self,
        cv_assessment: CVAssessment,
        candidate_tags: List[CandidateTag],
        qa_responses: List[Dict[str, str]],
    ) -> str:
        """Generate comprehensive profile text following the design document template"""

        # Organize tags by category
        tags_by_category = self._organize_tags_by_category(candidate_tags)

        # Create profile template following the design document structure
        profile_template = f"""
CANDIDATE PROFILE - {cv_assessment.name or 'Unknown'}

SUMMARY:
{await self._generate_professional_summary(cv_assessment, candidate_tags, qa_responses)}

PERSONAL INFORMATION:
- Name: {cv_assessment.name or 'Not provided'}
- Email: {cv_assessment.email or 'Not provided'}
- Location: {self._extract_location_info(tags_by_category, qa_responses)}
- Experience Level: {cv_assessment.experience_level.value if cv_assessment.experience_level else 'Not determined'}
- Availability: {self._extract_availability_info(tags_by_category, qa_responses)}

PROFESSIONAL EXPERIENCE:
{self._format_experience_section(tags_by_category.get('Experience', []), qa_responses)}

EDUCATION:
{self._format_education_section(tags_by_category.get('Education', []), qa_responses)}

TECHNICAL SKILLS:
{self._format_skills_section(tags_by_category.get('Skills', []), qa_responses)}

SOFT SKILLS & COMPETENCIES:
{self._format_soft_skills_section(tags_by_category, qa_responses)}

ACHIEVEMENTS & RECOGNITION:
{self._format_achievements_section(tags_by_category.get('Achievements', []), qa_responses)}

CAREER GOALS & PREFERENCES:
{self._format_career_goals_section(tags_by_category.get('Personal', []), qa_responses)}

COMPENSATION & LOGISTICS:
{self._format_compensation_section(tags_by_category, qa_responses)}

CULTURAL FIT INDICATORS:
{self._format_cultural_section(tags_by_category.get('Cultural', []), qa_responses)}

ASSESSMENT INSIGHTS:
{await self._generate_assessment_insights(cv_assessment, candidate_tags, qa_responses)}
        """.strip()

        return profile_template

    async def _generate_professional_summary(
        self,
        cv_assessment: CVAssessment,
        candidate_tags: List[CandidateTag],
        qa_responses: List[Dict[str, str]],
    ) -> str:
        """Generate AI-powered professional summary for any profession"""

        # Prepare context for AI
        context = {
            "cv_text": cv_assessment.pdf_text[:2000] if cv_assessment.pdf_text else "",
            "experience_level": (
                cv_assessment.experience_level.value
                if cv_assessment.experience_level
                else "unknown"
            ),
            "key_tags": [
                tag.value for tag in candidate_tags[:10] if tag.value
            ],  # Top 10 non-empty tags
            "recent_responses": qa_responses[-3:] if qa_responses else [],  # Last 3 Q&A
        }

        prompt = f"""
        Generate a professional summary for this candidate based on their CV and assessment responses.
        This system handles ALL professions (finance, marketing, sales, engineering, management, healthcare, etc.), not just software.
        
        Context:
        - Experience Level: {context['experience_level']}
        - Key Skills/Attributes: {', '.join(context['key_tags'])}
        - CV Excerpt: {context['cv_text']}
        - Recent Q&A: {json.dumps(context['recent_responses'], indent=2)}
        
        Create a 2-3 sentence professional summary that:
        1. Highlights their key strengths and experience relevant to their field
        2. Mentions their experience level and main expertise areas
        3. Focuses on what makes them unique and valuable to potential employers
        4. Is generic enough to work for any profession, not just technical roles
        
        Keep it professional, concise, and industry-appropriate.
        """

        try:
            response = await self.model.ainvoke(prompt)
            return response.content.strip()
        except Exception as e:
            logger.warning(f"Failed to generate AI summary: {e}")
            # Create a fallback summary
            key_skills = (
                ", ".join(context["key_tags"][:3])
                if context["key_tags"]
                else "various skills"
            )
            return f"Professional with {context['experience_level']} level experience and expertise in {key_skills}. Demonstrated track record of success and continuous professional development."

    def _organize_tags_by_category(
        self, candidate_tags: List[CandidateTag]
    ) -> Dict[str, List[CandidateTag]]:
        """Organize candidate tags by their categories"""
        tags_by_category = {}

        for tag in candidate_tags:
            if tag.tag_definition and tag.tag_definition.category:
                category_name = tag.tag_definition.category.name
                if category_name not in tags_by_category:
                    tags_by_category[category_name] = []
                tags_by_category[category_name].append(tag)

        return tags_by_category

    def _format_experience_section(
        self, experience_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the experience section following design document structure"""
        lines = []

        # Extract experience-related information
        for tag in experience_tags:
            if tag.tag_definition and tag.value:
                tag_name = tag.tag_definition.name.lower()
                if "years" in tag_name and "experience" in tag_name:
                    lines.append(f"- Total Years: {tag.value} years")
                elif "current" in tag_name and (
                    "role" in tag_name or "position" in tag_name
                ):
                    lines.append(f"- Current Role: {tag.value}")
                elif "company" in tag_name:
                    lines.append(f"- Current Company: {tag.value}")
                elif "industry" in tag_name:
                    lines.append(f"- Industry Experience: {tag.value}")
                elif "management" in tag_name or "team" in tag_name:
                    lines.append(f"- Management Experience: {tag.value}")
                elif "achievement" in tag_name or "accomplishment" in tag_name:
                    lines.append(f"- Key Achievements: {tag.value}")

        # Add relevant Q&A responses
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "experience",
                    "work",
                    "role",
                    "responsibility",
                    "achievement",
                    "accomplishment",
                ]
            ):
                lines.append(f"- {qa.get('answer', '')[:150]}...")

        return "\n".join(lines) if lines else "- Experience details not available"

    def _format_education_section(
        self, education_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the education section following design document structure"""
        lines = []

        for tag in education_tags:
            if tag.tag_definition and tag.value:
                tag_name = tag.tag_definition.name.lower()
                if "degree" in tag_name:
                    lines.append(f"- Highest Degree: {tag.value}")
                elif "major" in tag_name or "field" in tag_name:
                    lines.append(f"- Field of Study: {tag.value}")
                elif (
                    "university" in tag_name
                    or "school" in tag_name
                    or "institution" in tag_name
                ):
                    lines.append(f"- University: {tag.value}")
                elif "gpa" in tag_name or "grade" in tag_name:
                    lines.append(f"- GPA: {tag.value}")
                elif "graduation" in tag_name or "year" in tag_name:
                    lines.append(f"- Graduation Year: {tag.value}")
                elif "honor" in tag_name or "distinction" in tag_name:
                    lines.append(f"- Academic Honors: {tag.value}")
                elif "coursework" in tag_name or "course" in tag_name:
                    lines.append(f"- Relevant Coursework: {tag.value}")

        # Add education info from Q&A
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "education",
                    "degree",
                    "university",
                    "study",
                    "academic",
                ]
            ):
                lines.append(f"- {qa.get('answer', '')[:100]}...")

        return "\n".join(lines) if lines else "- Education details not available"

    def _format_skills_section(
        self, skills_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the skills section for all professions (not just technical)"""
        lines = []

        # Organize skills by type (generic for all professions)
        key_skills = []
        soft_skills = []
        technical_skills = []
        certifications = []
        languages = []
        other_skills = []

        for tag in skills_tags:
            if tag.tag_definition and tag.value:
                tag_name = tag.tag_definition.name.lower()

                # Generic skill categorization
                if any(
                    keyword in tag_name
                    for keyword in [
                        "leadership",
                        "management",
                        "analysis",
                        "strategy",
                        "sales",
                        "marketing",
                        "finance",
                        "accounting",
                        "project",
                    ]
                ):
                    key_skills.extend([s.strip() for s in tag.value.split(",")])
                elif any(
                    keyword in tag_name
                    for keyword in [
                        "communication",
                        "teamwork",
                        "problem",
                        "adaptability",
                        "creativity",
                        "critical thinking",
                    ]
                ):
                    soft_skills.extend([s.strip() for s in tag.value.split(",")])
                elif (
                    any(
                        keyword in tag_name
                        for keyword in ["language", "speak", "fluent"]
                    )
                    and not "programming" in tag_name
                ):
                    languages.extend([s.strip() for s in tag.value.split(",")])
                elif "certification" in tag_name or "certified" in tag_name:
                    certifications.extend([s.strip() for s in tag.value.split(",")])
                else:
                    # All technical skills (including programming, frameworks, tools, software)
                    technical_skills.extend([s.strip() for s in tag.value.split(",")])

        # Format organized skills (all generic)
        if key_skills:
            lines.append(f"- Key Professional Skills: {', '.join(set(key_skills))}")
        if soft_skills:
            lines.append(f"- Soft Skills: {', '.join(set(soft_skills))}")
        if languages:
            lines.append(f"- Languages: {', '.join(set(languages))}")
        if technical_skills:
            lines.append(f"- Technical Skills: {', '.join(set(technical_skills))}")
        if certifications:
            lines.append(f"- Certifications: {', '.join(set(certifications))}")

        # Add other skills
        lines.extend(other_skills)

        # Add skills from Q&A
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "skill",
                    "technology",
                    "software",
                    "tool",
                    "certification",
                    "language",
                    "ability",
                ]
            ):
                lines.append(f"- {qa.get('answer', '')[:100]}...")

        return "\n".join(lines) if lines else "- Skills information not available"

    def _format_achievements_section(
        self, achievement_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the achievements section of the profile"""
        lines = []

        for tag in achievement_tags:
            if tag.tag_definition:
                lines.append(f"- {tag.tag_definition.name}: {tag.value}")

        # Add achievements from Q&A
        for qa in qa_responses:
            if any(
                keyword in qa.get("question", "").lower()
                for keyword in ["achievement", "award", "recognition", "accomplish"]
            ):
                lines.append(f"- {qa.get('answer', '')[:100]}...")

        return "\n".join(lines) if lines else "- Achievements not specified"

    def _format_personal_section(
        self, personal_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the personal attributes section"""
        lines = []

        for tag in personal_tags:
            if tag.tag_definition:
                lines.append(f"- {tag.tag_definition.name}: {tag.value}")

        # Add personal info from Q&A
        for qa in qa_responses:
            if any(
                keyword in qa.get("question", "").lower()
                for keyword in ["salary", "location", "availability", "preference"]
            ):
                lines.append(
                    f"- {qa.get('question', '')}: {qa.get('answer', '')[:50]}..."
                )

        return "\n".join(lines) if lines else "- Personal preferences not specified"

    def _extract_location_info(
        self, tags_by_category: Dict, qa_responses: List[Dict]
    ) -> str:
        """Extract location information from tags and Q&A"""
        location_parts = []

        # Check tags for location info
        for category_tags in tags_by_category.values():
            for tag in category_tags:
                if tag.tag_definition and tag.value:
                    tag_name = tag.tag_definition.name.lower()
                    if any(
                        keyword in tag_name
                        for keyword in ["city", "location", "country"]
                    ):
                        location_parts.append(tag.value)

        # Check Q&A responses
        for qa in qa_responses:
            if any(
                keyword in qa.get("question", "").lower()
                for keyword in ["location", "city", "country", "where"]
            ):
                location_parts.append(qa.get("answer", "")[:50])

        return ", ".join(location_parts) if location_parts else "Not specified"

    def _extract_availability_info(
        self, tags_by_category: Dict, qa_responses: List[Dict]
    ) -> str:
        """Extract availability information from tags and Q&A"""
        # Check tags for availability info
        for category_tags in tags_by_category.values():
            for tag in category_tags:
                if tag.tag_definition and tag.value:
                    tag_name = tag.tag_definition.name.lower()
                    if any(
                        keyword in tag_name
                        for keyword in ["availability", "notice", "start"]
                    ):
                        return tag.value

        # Check Q&A responses
        for qa in qa_responses:
            if any(
                keyword in qa.get("question", "").lower()
                for keyword in ["availability", "start", "notice"]
            ):
                return qa.get("answer", "")[:100]

        return "Not specified"

    def _format_soft_skills_section(
        self, tags_by_category: Dict, qa_responses: List[Dict]
    ) -> str:
        """Format the soft skills and competencies section"""
        lines = []

        # Look for soft skills in various categories
        for category_name, category_tags in tags_by_category.items():
            for tag in category_tags:
                if tag.tag_definition and tag.value:
                    tag_name = tag.tag_definition.name.lower()
                    if any(
                        keyword in tag_name
                        for keyword in [
                            "leadership",
                            "communication",
                            "teamwork",
                            "problem",
                            "learning",
                        ]
                    ):
                        lines.append(f"- {tag.tag_definition.name}: {tag.value}")

        # Add soft skills from Q&A
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "leadership",
                    "team",
                    "communication",
                    "challenge",
                    "learn",
                ]
            ):
                lines.append(
                    f"- {qa.get('question', '')}: {qa.get('answer', '')[:100]}..."
                )

        return "\n".join(lines) if lines else "- Soft skills information not available"

    def _format_career_goals_section(
        self, personal_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the career goals and preferences section"""
        lines = []

        # Extract career goals from personal tags
        for tag in personal_tags:
            if tag.tag_definition and tag.value:
                tag_name = tag.tag_definition.name.lower()
                if any(
                    keyword in tag_name
                    for keyword in [
                        "goal",
                        "objective",
                        "career",
                        "aspiration",
                        "growth",
                    ]
                ):
                    lines.append(f"- {tag.tag_definition.name}: {tag.value}")

        # Add career goals from Q&A
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "goal",
                    "career",
                    "future",
                    "aspiration",
                    "growth",
                    "objective",
                ]
            ):
                lines.append(
                    f"- {qa.get('question', '')}: {qa.get('answer', '')[:100]}..."
                )

        return "\n".join(lines) if lines else "- Career goals not specified"

    def _format_compensation_section(
        self, tags_by_category: Dict, qa_responses: List[Dict]
    ) -> str:
        """Format the compensation and logistics section"""
        lines = []

        # Look for compensation info in all categories
        for category_tags in tags_by_category.values():
            for tag in category_tags:
                if tag.tag_definition and tag.value:
                    tag_name = tag.tag_definition.name.lower()
                    if any(
                        keyword in tag_name
                        for keyword in [
                            "salary",
                            "compensation",
                            "remote",
                            "travel",
                            "relocation",
                            "visa",
                        ]
                    ):
                        lines.append(f"- {tag.tag_definition.name}: {tag.value}")

        # Add compensation info from Q&A
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "salary",
                    "compensation",
                    "remote",
                    "travel",
                    "relocate",
                    "visa",
                ]
            ):
                lines.append(
                    f"- {qa.get('question', '')}: {qa.get('answer', '')[:100]}..."
                )

        return "\n".join(lines) if lines else "- Compensation preferences not specified"

    def _format_cultural_section(
        self, cultural_tags: List[CandidateTag], qa_responses: List[Dict]
    ) -> str:
        """Format the cultural fit section"""
        lines = []

        for tag in cultural_tags:
            if tag.tag_definition:
                lines.append(f"- {tag.tag_definition.name}: {tag.value}")

        # Add cultural fit info from Q&A
        for qa in qa_responses:
            question = qa.get("question", "").lower()
            if any(
                keyword in question
                for keyword in [
                    "culture",
                    "values",
                    "work style",
                    "team",
                    "environment",
                ]
            ):
                lines.append(
                    f"- {qa.get('question', '')}: {qa.get('answer', '')[:100]}..."
                )

        return "\n".join(lines) if lines else "- Cultural fit indicators not available"

    async def _generate_assessment_insights(
        self,
        cv_assessment: CVAssessment,
        candidate_tags: List[CandidateTag],
        qa_responses: List[Dict],
    ) -> str:
        """Generate AI-powered assessment insights"""

        prompt = f"""
        Analyze this candidate's assessment performance and provide insights:
        
        Assessment Completion: {cv_assessment.is_complete}
        Number of Tags Extracted: {len(candidate_tags)}
        Number of Q&A Responses: {len(qa_responses)}
        
        Recent Q&A Responses:
        {json.dumps(qa_responses[-3:], indent=2) if qa_responses else "No responses available"}
        
        Provide 2-3 bullet points highlighting:
        1. Key strengths based on responses
        2. Any potential concerns or gaps
        3. Overall assessment quality and completeness
        """

        try:
            response = await self.model.ainvoke(prompt)
            return response.content.strip()
        except Exception as e:
            logger.warning(f"Failed to generate assessment insights: {e}")
            return f"Assessment completed with {len(candidate_tags)} data points extracted and {len(qa_responses)} responses provided."

    async def _generate_embedding(self, profile_text: str) -> List[float]:
        """Generate vector embedding for the profile text"""
        try:
            embedding = await self.embeddings.aembed_query(profile_text)
            logger.info(f"Generated embedding with {len(embedding)} dimensions")
            return embedding
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    def _create_profile_metadata(
        self,
        cv_assessment: CVAssessment,
        candidate_tags: List[CandidateTag],
        extracted_info: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Create metadata for the candidate profile following the design document schema"""

        metadata = {
            # Core Identification
            "candidate_id": str(uuid4()),
            "cv_assessment_id": str(cv_assessment.id),
            "profile_version": "v1.0",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            # Personal Information
            "candidate_name": cv_assessment.name or "Unknown",
            "email": cv_assessment.email or "",
            "location_city": extracted_info.get("location_city", ""),
            "location_country": extracted_info.get("location_country", ""),
            "nationality": extracted_info.get("nationality", ""),
            # Experience & Level
            "experience_level": extracted_info.get("experience_level", "entry"),
            "total_years_experience": extracted_info.get("total_years_experience", 0),
            "current_role": extracted_info.get("current_role", ""),
            "current_company": extracted_info.get("current_company", ""),
            "industry_primary": extracted_info.get("industry_primary", ""),
            "industry_secondary": extracted_info.get("industry_secondary", ""),
            # Skills & Qualifications
            "degree_level": extracted_info.get("degree_level", ""),
            "major_field": extracted_info.get("major_field", ""),
            "university_tier": extracted_info.get("university_tier", ""),
            "gpa": extracted_info.get("gpa", 0.0),
            "certifications": json.dumps(extracted_info.get("certifications", [])),
            # Skills & Competencies (generic for all professions)
            "key_skills": json.dumps(extracted_info.get("key_skills", [])),
            "soft_skills": json.dumps(extracted_info.get("soft_skills", [])),
            "languages_spoken": json.dumps(extracted_info.get("languages_spoken", [])),
            "technical_skills": json.dumps(extracted_info.get("technical_skills", [])),
            # Compensation & Logistics
            "current_salary_range": extracted_info.get("current_salary_range", ""),
            "expected_salary_range": extracted_info.get("expected_salary_range", ""),
            "currency": extracted_info.get("currency", "SAR"),
            "remote_work_preference": extracted_info.get("remote_work_preference", ""),
            "relocation_willingness": extracted_info.get(
                "relocation_willingness", False
            ),
            "visa_sponsorship_needed": extracted_info.get(
                "visa_sponsorship_needed", False
            ),
            # Assessment Metrics
            "assessment_completion_percentage": (
                100.0 if cv_assessment.is_complete else 50.0
            ),
            "tag_coverage_percentage": self._calculate_completeness_score(
                candidate_tags
            ),
            "ai_total_score": (
                float(cv_assessment.score) if cv_assessment.score else 0.0
            ),
            "final_weighted_score": extracted_info.get("final_weighted_score", 0.0),
            "response_quality_score": extracted_info.get("response_quality_score", 0.0),
            # Tag Coverage Flags (for quick filtering)
            "has_education_tags": extracted_info.get("has_education_tags", False),
            "has_experience_tags": extracted_info.get("has_experience_tags", False),
            "has_skills_tags": extracted_info.get("has_skills_tags", False),
            "has_achievements_tags": extracted_info.get("has_achievements_tags", False),
            "has_personal_tags": extracted_info.get("has_personal_tags", False),
            "has_cultural_tags": extracted_info.get("has_cultural_tags", False),
            # Availability & Status
            "availability": extracted_info.get("availability", ""),
            "job_search_status": extracted_info.get("job_search_status", ""),
            "last_active": datetime.utcnow().isoformat(),
            # Profile Completeness
            "profile_completeness_score": self._calculate_completeness_score(
                candidate_tags
            ),
            "has_cv": bool(cv_assessment.pdf_text),
            "has_qa_responses": bool(cv_assessment.assessment_state),
            "has_photo": extracted_info.get("has_photo", False),
            "has_portfolio": extracted_info.get("has_portfolio", False),
            "linkedin_url": extracted_info.get("linkedin_url", ""),
            # Search Optimization
            "profile_text_length": 0,  # Will be updated when storing
            "keyword_density_score": extracted_info.get("keyword_density_score", 0.0),
            "semantic_richness_score": extracted_info.get(
                "semantic_richness_score", 0.0
            ),
            # Storage metadata
            "namespace": "active",
            "stored_at": datetime.utcnow().isoformat(),
        }

        return metadata

    async def _ai_extract_metadata(
        self, candidate_tags: List[CandidateTag], cv_assessment: CVAssessment
    ) -> Dict[str, Any]:
        """Use AI to intelligently extract metadata from candidate tags"""

        # Prepare tags data for AI analysis - keep ALL non-empty tags
        tags_data = []

        for tag in candidate_tags:
            if (
                tag.tag_definition and tag.value and tag.value.strip()
            ):  # Only include non-empty tags
                tag_name = getattr(tag.tag_definition, "display_name", None) or getattr(
                    tag.tag_definition, "tag_key", "Unknown"
                )
                category_name = (
                    tag.tag_definition.category.name
                    if tag.tag_definition.category
                    else "Unknown"
                )

                tags_data.append(
                    {
                        "category": category_name,
                        "tag_name": tag_name,
                        "value": tag.value.strip(),
                        "confidence": float(tag.confidence) if tag.confidence else 0.0,
                    }
                )

        # Get CV text and assessment description instead of Q&A responses
        cv_text = (
            cv_assessment.pdf_text[:1500]
            if cv_assessment.pdf_text
            else "No CV text available"
        )
        assessment_description = (
            cv_assessment.assessment_description
            if cv_assessment.assessment_description
            else ""
        )

        # Get Q&A responses for additional context
        qa_responses = self._extract_qa_responses(cv_assessment.assessment_state)

        # Create comprehensive AI prompt following the design document schema
        prompt = f"""
        You are an expert at extracting structured candidate information for any job type (not just software). Analyze the following candidate data and extract comprehensive metadata following the exact schema from the design document.

        CANDIDATE TAGS BY CATEGORY:
        {json.dumps(tags_data, indent=2)}

        CV BASIC INFO:
        - Name: {cv_assessment.name or 'Not provided'}
        - Email: {cv_assessment.email or 'Not provided'}
        - Experience Level: {cv_assessment.experience_level.value if cv_assessment.experience_level else 'Unknown'}
        
        CV TEXT EXCERPT:
        {cv_text}
        
        Q&A RESPONSES:
        {json.dumps(qa_responses[-5:], indent=2) if qa_responses else "No Q&A responses available"}

        EXTRACTION INSTRUCTIONS:
        - This system handles ALL job types (finance, marketing, sales, engineering, management, healthcare, education, etc.)
        - Look at the "category" and "value" fields to understand what each tag represents
        - For education category: extract degree level (bachelors/masters/phd), major field, university tier (top/mid/regional), GPA
        - For skills category: organize skills into these GENERIC categories:
          * key_skills: Main professional skills for ANY job (e.g., "Project Management", "Financial Analysis", "Digital Marketing", "Sales", "Leadership", "Python Programming", "Data Analysis")
          * soft_skills: Interpersonal skills (e.g., "Communication", "Teamwork", "Problem Solving", "Adaptability", "Critical Thinking")
          * languages_spoken: Spoken languages (e.g., "English", "Arabic", "Spanish", "French")
          * technical_skills: Software/tools/technologies for any profession (e.g., "Excel", "PowerPoint", "SAP", "Photoshop", "AutoCAD", "Salesforce", "Python", "Java", "AWS", "React", "Django")
        - For experience category: extract years of experience, current role, company, industry
        - For achievements category: extract accomplishments and recognition
        - For personal category: extract preferences, goals, logistics
        - For cultural category: extract work style and values
        - Determine if tags exist for each category (has_*_tags flags)
        - Extract location, salary, availability, and work preferences
        
        IMPORTANT: Put ALL technical skills (including programming languages, frameworks, cloud platforms) into the technical_skills array. This makes the system truly generic.

        Return a JSON object with these EXACT fields (generic for all professions):

        {{
            "nationality": "",
            "experience_level": "entry",
            "total_years_experience": 0,
            "current_role": "",
            "current_company": "",
            "industry_primary": "",
            "industry_secondary": "",
            "degree_level": "",
            "major_field": "",
            "university_tier": "",
            "gpa": 0.0,
            "certifications": [],
            "key_skills": [],
            "soft_skills": [],
            "languages_spoken": [],
            "technical_skills": [],
            "current_salary_range": "",
            "expected_salary_range": "",
            "currency": "SAR",
            "location_city": "",
            "location_country": "",
            "remote_work_preference": "",
            "relocation_willingness": false,
            "visa_sponsorship_needed": false,
            "final_weighted_score": 0.0,
            "response_quality_score": 0.0,
            "has_education_tags": false,
            "has_experience_tags": false,
            "has_skills_tags": false,
            "has_achievements_tags": false,
            "has_personal_tags": false,
            "has_cultural_tags": false,
            "availability": "",
            "job_search_status": "",
            "has_photo": false,
            "has_portfolio": false,
            "linkedin_url": "",
            "keyword_density_score": 0.0,
            "semantic_richness_score": 0.0
        }}

        FIELD MAPPING EXAMPLES (GENERIC FOR ALL PROFESSIONS):
        - Education category with "Bachelor" → degree_level: "bachelors"
        - Education category with "Computer Engineering" → major_field: "computer_engineering"  
        - Education category with "3.2" → gpa: 3.2
        
        SKILLS CATEGORIZATION (for ALL professions):
        - Skills category with "Project Management, Leadership, Sales, Data Analysis" → key_skills: ["Project Management", "Leadership", "Sales", "Data Analysis"]
        - Skills category with "Communication, Teamwork, Problem Solving, Adaptability" → soft_skills: ["Communication", "Teamwork", "Problem Solving", "Adaptability"]
        - Skills category with "English, Arabic, French, Spanish" → languages_spoken: ["English", "Arabic", "French", "Spanish"]
        - Skills category with "Excel, SAP, Python, AWS, Photoshop, AutoCAD" → technical_skills: ["Excel", "SAP", "Python", "AWS", "Photoshop", "AutoCAD"]
        
        NOTE: All technical skills (including programming languages, frameworks, cloud platforms) go into technical_skills array
        
        OTHER FIELDS:
        - Experience category with "5" → total_years_experience: 5
        - Experience category with "Senior Manager" → current_role: "Senior Manager"
        - Personal category with "Remote work preferred" → remote_work_preference: "remote"
        - Set has_*_tags to true only if that category actually has meaningful tags

        CRITICAL: Return ONLY the JSON object with no markdown formatting, no ```json blocks, no additional text or explanation. Just the raw JSON object starting with {{ and ending with }}.
        """

        try:
            response = await self.model.ainvoke(prompt)
            logger.info(f"AI response received: {response.content[:200]}...")

            extracted_data = json.loads(response.content.strip())
            logger.info(f"Parsed AI response: {len(extracted_data)} fields")

            # Validate and clean the extracted data
            extracted_data = self._validate_extracted_metadata(extracted_data)

            logger.info(
                f"Successfully extracted comprehensive metadata using AI: {len(extracted_data)} fields"
            )
            return extracted_data
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            return self._fallback_metadata_extraction(candidate_tags, cv_assessment)
        except Exception as e:
            logger.error(f"AI metadata extraction failed: {e}")
            return self._fallback_metadata_extraction(candidate_tags, cv_assessment)

    async def _ai_extract_metadata_from_raw_data(
        self, candidate_tags_data: List[Dict[str, Any]], cv_assessment: CVAssessment
    ) -> Dict[str, Any]:
        """Use AI to intelligently extract metadata from raw candidate tags data"""

        # Prepare tags data for AI analysis from raw database data
        tags_data = []

        for tag_data in candidate_tags_data:
            tag_def = tag_data.get("tag_definitions")
            if tag_def:
                category_name = "Unknown"
                if tag_def.get("category"):
                    category_name = tag_def["category"].get("name", "Unknown")

                tag_name = tag_def.get("display_name") or tag_def.get(
                    "tag_key", "Unknown"
                )

                tags_data.append(
                    {
                        "category": category_name,
                        "tag_name": tag_name,
                        "value": tag_data.get("value", ""),
                        "confidence": float(tag_data.get("confidence_score", 0.0)),
                    }
                )

        # Filter out empty tags
        filtered_tags_data = []
        for tag in tags_data:
            # Skip tags with empty values
            if (
                not tag.get("value")
                or tag.get("value") == "Unknown"
                or tag.get("value") == "None"
            ):
                continue
            filtered_tags_data.append(tag)

        # Get CV text and assessment description
        cv_text = cv_assessment.pdf_text[:1000] if cv_assessment.pdf_text else ""
        assessment_description = (
            cv_assessment.assessment_description[:500]
            if cv_assessment.assessment_description
            else ""
        )

        # Create comprehensive AI prompt following the design document schema
        prompt = f"""
        You are an expert at extracting structured candidate information. Analyze the following candidate data and extract comprehensive metadata.

        CANDIDATE TAGS BY CATEGORY:
        {json.dumps(filtered_tags_data, indent=2)}

        CV BASIC INFO:
        - Name: {cv_assessment.name or 'Not provided'}
        - Email: {cv_assessment.email or 'Not provided'}
        - Experience Level: {cv_assessment.experience_level.value if cv_assessment.experience_level else 'Unknown'}
        
        CV TEXT EXCERPT:
        {cv_text}
        
        ASSESSMENT DESCRIPTION:
        {assessment_description}

        EXTRACTION INSTRUCTIONS:
        - Look at the "category" and "value" fields to understand what each tag represents
        - For education category: extract degree level, major, university, GPA, graduation date
        - For skills category: extract key skills, soft skills, languages, and technical skills
        - For experience category: extract years of experience, roles, companies
        - For achievements category: extract accomplishments and metrics
        - For personal category: extract career goals and preferences
        - For cultural category: extract work style and team preferences

        Return a JSON object with these fields (use appropriate defaults for missing data):

        {{
            "nationality": "",
            "experience_level": "junior",
            "total_years_experience": 0,
            "current_role": "",
            "current_company": "",
            "industry_primary": "",
            "industry_secondary": "",
            "degree_level": "",
            "major_field": "",
            "university_tier": "",
            "gpa": 0.0,
            "certifications": [],
            "key_skills": [],
            "soft_skills": [],
            "languages_spoken": [],
            "technical_skills": [],
            "current_salary_range": "",
            "expected_salary_range": "",
            "currency": "SAR",
            "location_city": "",
            "location_country": "",
            "remote_work_preference": "",
            "relocation_willingness": false,
            "visa_sponsorship_needed": false,
            "final_weighted_score": 0.0,
            "response_quality_score": 0.0,
            "has_education_tags": true,
            "has_experience_tags": true,
            "has_skills_tags": true,
            "has_achievements_tags": true,
            "has_personal_tags": true,
            "has_cultural_tags": true,
            "availability": "",
            "job_search_status": "",
            "has_photo": false,
            "has_portfolio": false,
            "linkedin_url": "",
            "keyword_density_score": 0.0,
            "semantic_richness_score": 0.0
        }}

        EXAMPLE EXTRACTIONS:
        - If you see education category with value "Bachelor" → degree_level: "bachelors"
        - If you see education category with value "Computer Engineering" → major_field: "computer_engineering"
        - If you see education category with value "3.2" → gpa: 3.2
        - If you see skills category with value "C, Java, Python" → technical_skills: ["C", "Java", "Python"]
        - If you see experience category with value "5" → total_years_experience: 5

        CRITICAL: Return ONLY the JSON object with no markdown formatting, no ```json blocks, no additional text or explanation. Just the raw JSON object starting with {{ and ending with }}.
        """

        try:
            response = await self.model.ainvoke(prompt)
            logger.info(f"AI response received: {response.content[:200]}...")

            extracted_data = json.loads(response.content.strip())
            logger.info(f"Parsed AI response: {len(extracted_data)} fields")

            # Validate and clean the extracted data
            extracted_data = self._validate_extracted_metadata(extracted_data)

            logger.info(
                f"Successfully extracted comprehensive metadata using AI: {len(extracted_data)} fields"
            )
            return extracted_data
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            # Create fallback from raw data
            return self._fallback_metadata_extraction_from_raw_data(
                candidate_tags_data, cv_assessment
            )
        except Exception as e:
            logger.error(f"AI metadata extraction failed: {e}")
            return self._fallback_metadata_extraction_from_raw_data(
                candidate_tags_data, cv_assessment
            )

    def _fallback_metadata_extraction_from_raw_data(
        self, candidate_tags_data: List[Dict[str, Any]], cv_assessment: CVAssessment
    ) -> Dict[str, Any]:
        """Fallback metadata extraction from raw data if AI fails"""

        # Basic fallback extraction
        categories_present = set()
        for tag_data in candidate_tags_data:
            tag_def = tag_data.get("tag_definitions")
            if tag_def and tag_def.get("category"):
                categories_present.add(tag_def["category"].get("name", "Unknown"))

        return {
            # Personal Information
            "nationality": "",
            # Experience & Level
            "experience_level": (
                cv_assessment.experience_level.value
                if cv_assessment.experience_level
                else "entry"
            ),
            "total_years_experience": 0,
            "current_role": "",
            "current_company": "",
            "industry_primary": "",
            "industry_secondary": "",
            # Skills & Qualifications
            "degree_level": "",
            "major_field": "",
            "university_tier": "",
            "gpa": 0.0,
            "certifications": [],
            # Skills & Competencies (generic for all professions)
            "key_skills": [],
            "soft_skills": [],
            "languages_spoken": [],
            "technical_skills": [],
            # Compensation & Logistics
            "current_salary_range": "",
            "expected_salary_range": "",
            "currency": "SAR",
            "location_city": "",
            "location_country": "",
            "remote_work_preference": "",
            "relocation_willingness": False,
            "visa_sponsorship_needed": False,
            # Assessment Metrics
            "final_weighted_score": 0.0,
            "response_quality_score": 0.0,
            # Tag Coverage Flags
            "has_education_tags": "education" in categories_present,
            "has_experience_tags": "experience" in categories_present,
            "has_skills_tags": "skills" in categories_present,
            "has_achievements_tags": "achievements" in categories_present,
            "has_personal_tags": "personal" in categories_present,
            "has_cultural_tags": "cultural" in categories_present,
            # Availability & Status
            "availability": "",
            "job_search_status": "",
            # Profile Completeness
            "has_photo": False,
            "has_portfolio": False,
            "linkedin_url": "",
            # Search Optimization
            "keyword_density_score": 0.0,
            "semantic_richness_score": 0.0,
        }

    def _validate_extracted_metadata(
        self, extracted_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate and clean extracted metadata to ensure proper types and values"""

        # Ensure arrays are properly formatted
        array_fields = [
            "certifications",
            "key_skills",
            "soft_skills",
            "languages_spoken",
            "technical_skills",
        ]
        for field in array_fields:
            if field in extracted_data:
                value = extracted_data[field]
                if isinstance(value, str):
                    # Convert comma-separated string to array
                    extracted_data[field] = [
                        item.strip() for item in value.split(",") if item.strip()
                    ]
                elif not isinstance(value, list):
                    extracted_data[field] = []

        # Ensure numeric fields are properly typed
        numeric_fields = [
            "total_years_experience",
            "gpa",
            "final_weighted_score",
            "response_quality_score",
            "keyword_density_score",
            "semantic_richness_score",
        ]
        for field in numeric_fields:
            if field in extracted_data:
                try:
                    extracted_data[field] = (
                        float(extracted_data[field]) if extracted_data[field] else 0.0
                    )
                except (ValueError, TypeError):
                    extracted_data[field] = 0.0

        # Ensure boolean fields are properly typed
        boolean_fields = [
            "relocation_willingness",
            "visa_sponsorship_needed",
            "has_education_tags",
            "has_experience_tags",
            "has_skills_tags",
            "has_achievements_tags",
            "has_personal_tags",
            "has_cultural_tags",
            "has_photo",
            "has_portfolio",
        ]
        for field in boolean_fields:
            if field in extracted_data:
                extracted_data[field] = bool(extracted_data[field])

        # Validate experience level
        valid_levels = ["entry", "junior", "mid", "senior", "executive"]
        if extracted_data.get("experience_level") not in valid_levels:
            extracted_data["experience_level"] = "entry"

        return extracted_data

    def _fallback_metadata_extraction(
        self, candidate_tags: List[CandidateTag], cv_assessment: CVAssessment
    ) -> Dict[str, Any]:
        """Fallback metadata extraction if AI fails - generic for all professions"""

        # Basic fallback extraction
        categories_present = set()
        for tag in candidate_tags:
            if tag.tag_definition and tag.tag_definition.category:
                categories_present.add(tag.tag_definition.category.name)

        return {
            # Personal Information
            "nationality": "",
            # Experience & Level
            "experience_level": (
                cv_assessment.experience_level.value
                if cv_assessment.experience_level
                else "entry"
            ),
            "total_years_experience": 0,
            "current_role": "",
            "current_company": "",
            "industry_primary": "",
            "industry_secondary": "",
            # Skills & Qualifications
            "degree_level": "",
            "major_field": "",
            "university_tier": "",
            "gpa": 0.0,
            "certifications": [],
            # Skills & Competencies (generic for all professions)
            "key_skills": [],
            "soft_skills": [],
            "languages_spoken": [],
            "technical_skills": [],
            # Compensation & Logistics
            "current_salary_range": "",
            "expected_salary_range": "",
            "currency": "SAR",
            "location_city": "",
            "location_country": "",
            "remote_work_preference": "",
            "relocation_willingness": False,
            "visa_sponsorship_needed": False,
            # Assessment Metrics
            "final_weighted_score": 0.0,
            "response_quality_score": 0.0,
            # Tag Coverage Flags
            "has_education_tags": "Education" in categories_present,
            "has_experience_tags": "Experience" in categories_present,
            "has_skills_tags": "Skills" in categories_present,
            "has_achievements_tags": "Achievements" in categories_present,
            "has_personal_tags": "Personal" in categories_present,
            "has_cultural_tags": "Cultural" in categories_present,
            # Availability & Status
            "availability": "",
            "job_search_status": "",
            # Profile Completeness
            "has_photo": False,
            "has_portfolio": False,
            "linkedin_url": "",
            # Search Optimization
            "keyword_density_score": 0.0,
            "semantic_richness_score": 0.0,
        }

    def _extract_qa_responses(self, assessment_state: str) -> List[Dict[str, str]]:
        """Extract Q&A responses from assessment state JSON"""
        if not assessment_state:
            return []

        try:
            state_data = json.loads(assessment_state)
            qa_responses = []

            # Extract Q&A pairs from the state
            if isinstance(state_data, list):
                for item in state_data:
                    if (
                        isinstance(item, dict)
                        and "question" in item
                        and "answer" in item
                    ):
                        qa_responses.append(
                            {"question": item["question"], "answer": item["answer"]}
                        )
            elif isinstance(state_data, dict):
                # Handle different state formats
                if "qa_history" in state_data:
                    qa_responses = state_data["qa_history"]
                elif "questions" in state_data and "answers" in state_data:
                    questions = state_data["questions"]
                    answers = state_data["answers"]
                    for i, q in enumerate(questions):
                        if i < len(answers):
                            qa_responses.append({"question": q, "answer": answers[i]})

            return qa_responses

        except json.JSONDecodeError:
            logger.warning("Failed to parse assessment state JSON")
            return []

    def _calculate_completeness_score(
        self, candidate_tags: List[CandidateTag]
    ) -> float:
        """Calculate profile completeness score based on tag coverage"""
        if not candidate_tags:
            return 0.0

        # Count tags by category
        categories = {}
        for tag in candidate_tags:
            if tag.tag_definition and tag.tag_definition.category:
                category = tag.tag_definition.category.name
                categories[category] = categories.get(category, 0) + 1

        # Expected minimum tags per category
        expected_categories = {
            "Education": 3,
            "Experience": 4,
            "Skills": 5,
            "Achievements": 2,
            "Personal": 3,
            "Cultural": 2,
        }

        total_score = 0
        total_weight = 0

        for category, expected_count in expected_categories.items():
            actual_count = categories.get(category, 0)
            category_score = min(actual_count / expected_count, 1.0) * 100
            total_score += category_score
            total_weight += 100

        return total_score / total_weight if total_weight > 0 else 0.0

    async def get_candidate_profile_by_assessment(
        self, cv_assessment_id: int
    ) -> Optional[Dict[str, Any]]:
        """Retrieve existing candidate profile by assessment ID"""
        # This would typically query a candidate_profiles table
        # For now, we'll generate on-demand
        return await self.generate_candidate_profile(cv_assessment_id)

    async def update_candidate_profile(
        self, cv_assessment_id: int, updated_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update an existing candidate profile"""
        # Regenerate profile with latest data
        return await self.generate_candidate_profile(
            cv_assessment_id, force_regenerate=True
        )

    async def search_candidates(
        self,
        query_text: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 10,
        similarity_threshold: float = 0.7,
    ) -> List[Dict[str, Any]]:
        """
        Search for candidates using semantic similarity

        Args:
            query_text: Job description or search query
            filters: Metadata filters for candidate selection
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score

        Returns:
            List of matching candidates with enhanced information
        """
        try:
            # Use vector service to search
            vector_results = await self.vector_service.search_candidates(
                query_text, filters, top_k, "active", similarity_threshold
            )

            # Enhance results with additional information
            enhanced_results = []
            for result in vector_results:
                enhanced_result = {
                    "candidate_id": result["candidate_id"],
                    "similarity_score": result["similarity_score"],
                    "candidate_name": result["metadata"].get(
                        "candidate_name", "Unknown"
                    ),
                    "experience_level": result["metadata"].get(
                        "experience_level", "unknown"
                    ),
                    "current_role": result["metadata"].get("current_role"),
                    "programming_languages": result["metadata"].get(
                        "programming_languages", []
                    ),
                    "total_years_experience": result["metadata"].get(
                        "total_years_experience"
                    ),
                    "profile_completeness_score": result["metadata"].get(
                        "profile_completeness_score", 0
                    ),
                    "cv_assessment_id": result["metadata"].get("cv_assessment_id"),
                    "last_active": result["metadata"].get("last_active"),
                    "metadata": result["metadata"],
                }
                enhanced_results.append(enhanced_result)

            logger.info(
                f"Found {len(enhanced_results)} candidates for query: {query_text[:50]}..."
            )
            return enhanced_results

        except Exception as e:
            logger.error(f"Failed to search candidates: {str(e)}")
            return []

    async def find_similar_candidates(
        self,
        reference_candidate_id: str,
        top_k: int = 10,
        exclude_reference: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Find candidates similar to a reference candidate

        Args:
            reference_candidate_id: ID of the reference candidate
            top_k: Number of similar candidates to return
            exclude_reference: Whether to exclude the reference candidate from results

        Returns:
            List of similar candidates
        """
        try:
            similar_results = await self.vector_service.find_similar_candidates(
                reference_candidate_id, top_k, "active", exclude_reference
            )

            # Enhance results similar to search_candidates
            enhanced_results = []
            for result in similar_results:
                enhanced_result = {
                    "candidate_id": result["candidate_id"],
                    "similarity_score": result["similarity_score"],
                    "candidate_name": result["metadata"].get(
                        "candidate_name", "Unknown"
                    ),
                    "experience_level": result["metadata"].get(
                        "experience_level", "unknown"
                    ),
                    "current_role": result["metadata"].get("current_role"),
                    "key_skills": result["metadata"].get("key_skills", []),
                    "technical_skills": result["metadata"].get("technical_skills", []),
                    "total_years_experience": result["metadata"].get(
                        "total_years_experience"
                    ),
                    "profile_completeness_score": result["metadata"].get(
                        "profile_completeness_score", 0
                    ),
                    "metadata": result["metadata"],
                }
                enhanced_results.append(enhanced_result)

            logger.info(
                f"Found {len(enhanced_results)} similar candidates to {reference_candidate_id}"
            )
            return enhanced_results

        except Exception as e:
            logger.error(f"Failed to find similar candidates: {str(e)}")
            return []

    async def get_candidate_by_id(self, candidate_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific candidate by their ID

        Args:
            candidate_id: The candidate's unique identifier

        Returns:
            Candidate information if found, None otherwise
        """
        try:
            # Search for the specific candidate
            results = await self.vector_service.search_candidates(
                "", {"candidate_id": candidate_id}, 1, "active", 0.0
            )

            if results:
                result = results[0]
                return {
                    "candidate_id": result["candidate_id"],
                    "candidate_name": result["metadata"].get(
                        "candidate_name", "Unknown"
                    ),
                    "email": result["metadata"].get("email"),
                    "experience_level": result["metadata"].get(
                        "experience_level", "unknown"
                    ),
                    "current_role": result["metadata"].get("current_role"),
                    "current_company": result["metadata"].get("current_company"),
                    "key_skills": result["metadata"].get("key_skills", []),
                    "technical_skills": result["metadata"].get("technical_skills", []),
                    "soft_skills": result["metadata"].get("soft_skills", []),
                    "languages_spoken": result["metadata"].get("languages_spoken", []),
                    "certifications": result["metadata"].get("certifications", []),
                    "total_years_experience": result["metadata"].get(
                        "total_years_experience"
                    ),
                    "location_city": result["metadata"].get("location_city"),
                    "location_country": result["metadata"].get("location_country"),
                    "nationality": result["metadata"].get("nationality"),
                    "degree_level": result["metadata"].get("degree_level"),
                    "major_field": result["metadata"].get("major_field"),
                    "gpa": result["metadata"].get("gpa"),
                    "university_tier": result["metadata"].get("university_tier"),
                    "industry_primary": result["metadata"].get("industry_primary"),
                    "industry_secondary": result["metadata"].get("industry_secondary"),
                    "current_salary_range": result["metadata"].get(
                        "current_salary_range"
                    ),
                    "expected_salary_range": result["metadata"].get(
                        "expected_salary_range"
                    ),
                    "currency": result["metadata"].get("currency"),
                    "remote_work_preference": result["metadata"].get(
                        "remote_work_preference"
                    ),
                    "relocation_willingness": result["metadata"].get(
                        "relocation_willingness"
                    ),
                    "visa_sponsorship_needed": result["metadata"].get(
                        "visa_sponsorship_needed"
                    ),
                    "job_search_status": result["metadata"].get("job_search_status"),
                    "availability": result["metadata"].get("availability"),
                    "linkedin_url": result["metadata"].get("linkedin_url"),
                    "profile_completeness_score": result["metadata"].get(
                        "profile_completeness_score", 0
                    ),
                    "ai_total_score": result["metadata"].get("ai_total_score"),
                    "assessment_completion_percentage": result["metadata"].get(
                        "assessment_completion_percentage"
                    ),
                    "tag_coverage_percentage": result["metadata"].get(
                        "tag_coverage_percentage"
                    ),
                    "cv_assessment_id": result["metadata"].get("cv_assessment_id"),
                    "created_at": result["metadata"].get("created_at"),
                    "updated_at": result["metadata"].get("updated_at"),
                    "last_active": result["metadata"].get("last_active"),
                    "summary": result["metadata"].get("summary"),
                    "has_cv": result["metadata"].get("has_cv"),
                    "has_photo": result["metadata"].get("has_photo"),
                    "has_portfolio": result["metadata"].get("has_portfolio"),
                    "has_qa_responses": result["metadata"].get("has_qa_responses"),
                    "has_education_tags": result["metadata"].get("has_education_tags"),
                    "has_experience_tags": result["metadata"].get(
                        "has_experience_tags"
                    ),
                    "has_skills_tags": result["metadata"].get("has_skills_tags"),
                    "has_achievements_tags": result["metadata"].get(
                        "has_achievements_tags"
                    ),
                    "has_personal_tags": result["metadata"].get("has_personal_tags"),
                    "has_cultural_tags": result["metadata"].get("has_cultural_tags"),
                    "profile_text_length": result["metadata"].get(
                        "profile_text_length"
                    ),
                    "profile_version": result["metadata"].get("profile_version"),
                    "metadata": result["metadata"],
                }

            return None

        except Exception as e:
            logger.error(f"Failed to get candidate {candidate_id}: {str(e)}")
            return None

    async def get_candidate_by_assessment_id(
        self, cv_assessment_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific candidate by their CV assessment ID

        Args:
            cv_assessment_id: The CV assessment ID

        Returns:
            Candidate information if found, None otherwise
        """
        try:
            # Search for candidate by cv_assessment_id
            results = await self.vector_service.search_candidates(
                "", {"cv_assessment_id": cv_assessment_id}, 1, "active", 0.0
            )

            if results:
                result = results[0]
                return {
                    "candidate_id": result["candidate_id"],
                    "cv_assessment_id": result["metadata"].get("cv_assessment_id"),
                    "candidate_name": result["metadata"].get(
                        "candidate_name", "Unknown"
                    ),
                    "email": result["metadata"].get("email"),
                    "experience_level": result["metadata"].get(
                        "experience_level", "unknown"
                    ),
                    "current_role": result["metadata"].get("current_role"),
                    "current_company": result["metadata"].get("current_company"),
                    "key_skills": result["metadata"].get("key_skills", []),
                    "technical_skills": result["metadata"].get("technical_skills", []),
                    "soft_skills": result["metadata"].get("soft_skills", []),
                    "languages_spoken": result["metadata"].get("languages_spoken", []),
                    "certifications": result["metadata"].get("certifications", []),
                    "total_years_experience": result["metadata"].get(
                        "total_years_experience"
                    ),
                    "location_city": result["metadata"].get("location_city"),
                    "location_country": result["metadata"].get("location_country"),
                    "nationality": result["metadata"].get("nationality"),
                    "degree_level": result["metadata"].get("degree_level"),
                    "major_field": result["metadata"].get("major_field"),
                    "gpa": result["metadata"].get("gpa"),
                    "university_tier": result["metadata"].get("university_tier"),
                    "industry_primary": result["metadata"].get("industry_primary"),
                    "industry_secondary": result["metadata"].get("industry_secondary"),
                    "current_salary_range": result["metadata"].get(
                        "current_salary_range"
                    ),
                    "expected_salary_range": result["metadata"].get(
                        "expected_salary_range"
                    ),
                    "currency": result["metadata"].get("currency"),
                    "remote_work_preference": result["metadata"].get(
                        "remote_work_preference"
                    ),
                    "relocation_willingness": result["metadata"].get(
                        "relocation_willingness"
                    ),
                    "visa_sponsorship_needed": result["metadata"].get(
                        "visa_sponsorship_needed"
                    ),
                    "job_search_status": result["metadata"].get("job_search_status"),
                    "availability": result["metadata"].get("availability"),
                    "linkedin_url": result["metadata"].get("linkedin_url"),
                    "profile_completeness_score": result["metadata"].get(
                        "profile_completeness_score", 0
                    ),
                    "ai_total_score": result["metadata"].get("ai_total_score"),
                    "assessment_completion_percentage": result["metadata"].get(
                        "assessment_completion_percentage"
                    ),
                    "tag_coverage_percentage": result["metadata"].get(
                        "tag_coverage_percentage"
                    ),
                    "cv_assessment_id": result["metadata"].get("cv_assessment_id"),
                    "created_at": result["metadata"].get("created_at"),
                    "updated_at": result["metadata"].get("updated_at"),
                    "last_active": result["metadata"].get("last_active"),
                    "summary": result["metadata"].get("summary"),
                    "has_cv": result["metadata"].get("has_cv"),
                    "has_photo": result["metadata"].get("has_photo"),
                    "has_portfolio": result["metadata"].get("has_portfolio"),
                    "has_qa_responses": result["metadata"].get("has_qa_responses"),
                    "has_education_tags": result["metadata"].get("has_education_tags"),
                    "has_experience_tags": result["metadata"].get(
                        "has_experience_tags"
                    ),
                    "has_skills_tags": result["metadata"].get("has_skills_tags"),
                    "has_achievements_tags": result["metadata"].get(
                        "has_achievements_tags"
                    ),
                    "has_personal_tags": result["metadata"].get("has_personal_tags"),
                    "has_cultural_tags": result["metadata"].get("has_cultural_tags"),
                    "profile_text_length": result["metadata"].get(
                        "profile_text_length"
                    ),
                    "profile_version": result["metadata"].get("profile_version"),
                    "similarity_score": result.get("score", 0),
                    "metadata": result["metadata"],
                }

            return None
        except Exception as e:
            logger.error(
                f"Error getting candidate by assessment ID {cv_assessment_id}: {str(e)}"
            )
            return None

    def _parse_assessment_state(self, assessment_state: str) -> List[Dict[str, str]]:
        """Parse assessment state to extract Q&A pairs"""
        if not assessment_state:
            return []

        try:
            import json

            parsed_state = json.loads(assessment_state)
            if isinstance(parsed_state, dict):
                return parsed_state.get("qa_history", [])
            elif isinstance(parsed_state, list):
                return parsed_state
            else:
                return []
        except Exception as e:
            logger.warning(f"Error parsing assessment state: {str(e)}")
            return []

    async def batch_generate_profiles(
        self, cv_assessment_ids: List[int], batch_size: int = 10
    ) -> Dict[str, Any]:
        """
        Generate candidate profiles for multiple assessments in batches

        Args:
            cv_assessment_ids: List of CV assessment IDs
            batch_size: Number of profiles to process per batch

        Returns:
            Dictionary with success/failure counts and details
        """
        try:
            logger.info(
                f"Batch generating profiles for {len(cv_assessment_ids)} assessments"
            )

            successful = 0
            failed = 0
            results = []

            # Process in batches
            for i in range(0, len(cv_assessment_ids), batch_size):
                batch_ids = cv_assessment_ids[i : i + batch_size]

                for assessment_id in batch_ids:
                    try:
                        result = await self.generate_candidate_profile(assessment_id)
                        if result["success"]:
                            successful += 1
                            results.append(
                                {
                                    "cv_assessment_id": assessment_id,
                                    "candidate_id": result.get("candidate_id"),
                                    "status": "success",
                                }
                            )
                        else:
                            failed += 1
                            results.append(
                                {
                                    "cv_assessment_id": assessment_id,
                                    "status": "failed",
                                    "error": result.get("error_message"),
                                }
                            )
                    except Exception as e:
                        failed += 1
                        results.append(
                            {
                                "cv_assessment_id": assessment_id,
                                "status": "failed",
                                "error": str(e),
                            }
                        )

                logger.info(
                    f"Processed batch {i//batch_size + 1}, successful: {successful}, failed: {failed}"
                )

            summary = {
                "total_assessments": len(cv_assessment_ids),
                "successful": successful,
                "failed": failed,
                "success_rate": (
                    successful / len(cv_assessment_ids) if cv_assessment_ids else 0
                ),
                "results": results,
            }

            logger.info(f"Batch profile generation completed: {summary}")
            return summary

        except Exception as e:
            logger.error(f"Failed batch profile generation: {str(e)}")
            return {
                "total_assessments": len(cv_assessment_ids),
                "successful": 0,
                "failed": len(cv_assessment_ids),
                "success_rate": 0.0,
                "error": str(e),
            }

    def get_vector_index_stats(self) -> Dict[str, Any]:
        """Get statistics about the candidate vector index"""
        try:
            return self.vector_service.get_index_stats()
        except Exception as e:
            logger.error(f"Failed to get index stats: {str(e)}")
            return {}


# Initialize service instance
def get_candidate_profile_service(supabase_client=None):
    """Get initialized candidate profile service"""
    if supabase_client:
        db_service = DatabaseService(supabase_client)
        return CandidateProfileService(db_service)
    else:
        # For testing purposes, we'll create a mock service
        # In production, supabase_client should always be provided
        return CandidateProfileService(None)
