"""
Background Task Processing Module

This module provides functionality for running tasks in the background
without interrupting the main application flow.
"""

import threading
import logging
import queue
import atexit
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Callable, Any, Set
from functools import wraps

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("background_tasks")

# Create a global executor with limited workers and a bounded queue to avoid resource exhaustion
# Using a custom queue with a maximum size to prevent unbounded memory growth
task_queue = queue.Queue(maxsize=100)
executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="bg_task")

# Track active futures for proper shutdown
active_futures: Set[Future] = set()
futures_lock = threading.Lock()


# Create a cleanup thread to periodically remove completed futures
def cleanup_completed_futures():
    """Remove completed futures from the active_futures set to prevent memory leaks"""
    with futures_lock:
        completed_futures = [f for f in active_futures if f.done()]
        for f in completed_futures:
            active_futures.remove(f)
        logger.debug(
            f"Cleaned up {len(completed_futures)} completed futures. Active futures: {len(active_futures)}"
        )


# Schedule periodic cleanup
cleanup_interval = 60  # seconds
cleanup_timer = None


def start_cleanup_timer():
    """Start the periodic cleanup timer"""
    global cleanup_timer
    cleanup_completed_futures()
    cleanup_timer = threading.Timer(cleanup_interval, start_cleanup_timer)
    cleanup_timer.daemon = True
    cleanup_timer.start()


# Start the initial cleanup timer
start_cleanup_timer()


def submit_background_task(task_func: Callable, *args: Any, **kwargs: Any) -> Future:
    """
    Submit a task to run in the background using a thread pool with a bounded queue.

    Args:
        task_func: The function to run in the background
        *args: Positional arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function

    Returns:
        A Future object representing the task
    """

    # Wrap the function to catch and log exceptions
    @wraps(task_func)
    def wrapped_task(*args, **kwargs):
        try:
            result = task_func(*args, **kwargs)
            return result
        except Exception as e:
            logger.error(f"Error in background task {task_func.__name__}: {str(e)}")
            # Re-raise the exception so it's captured in the Future
            raise
        finally:
            # Remove the future from the active set when done
            with futures_lock:
                future_to_remove = None
                for f in active_futures:
                    if f.done():
                        future_to_remove = f
                        break
                if future_to_remove:
                    active_futures.remove(future_to_remove)

    try:
        # Try to submit the task, but don't block if the queue is full
        future = executor.submit(wrapped_task, *args, **kwargs)

        # Track the future for proper shutdown
        with futures_lock:
            active_futures.add(future)

            # Clean up any completed futures
            completed_futures = [f for f in active_futures if f.done()]
            for f in completed_futures:
                active_futures.remove(f)

        return future
    except queue.Full:
        logger.warning(
            f"Task queue is full. Task {task_func.__name__} will not be executed."
        )
        # Return a future that's already done with an exception
        future = Future()
        future.set_exception(RuntimeError("Task queue is full"))
        return future


def run_in_background(func: Callable) -> Callable:
    """
    Decorator to run a function in the background.

    Example:
        @run_in_background
        def send_email(recipient, subject, body):
            # Email sending logic here
            pass

    Args:
        func: The function to run in the background

    Returns:
        A wrapper function that submits the decorated function to run in the background
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        return submit_background_task(func, *args, **kwargs)

    return wrapper


def shutdown_executor():
    """
    Shutdown the executor gracefully, waiting for running tasks to complete.
    This function is registered with atexit to ensure proper cleanup on application exit.
    """
    logger.info("Shutting down background task executor...")

    # Stop the cleanup timer
    global cleanup_timer
    if cleanup_timer:
        cleanup_timer.cancel()

    # Cancel all pending tasks
    with futures_lock:
        for future in active_futures:
            if not future.done():
                future.cancel()

    # Shutdown the executor
    executor.shutdown(wait=True)
    logger.info("Background task executor shutdown complete.")


# Register the shutdown function to be called on application exit
atexit.register(shutdown_executor)
