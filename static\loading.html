<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refreshing Pinecone Index</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            text-align: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        p {
            color: #666;
        }
        .loader {
            border: 16px solid #f3f3f3; /* Light grey */
            border-top: 16px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Refreshing Pinecone Index</h1>
        <div class="loader"></div>
        <p>Please wait while the Pinecone index is being refreshed. This may take a few minutes.</p>
        <p id="statusMessage">You will be redirected automatically when the process is complete.</p>
    </div>
    <script>
        const statusMessageElement = document.getElementById('statusMessage');
        const loaderElement = document.querySelector('.loader');
        const headingElement = document.querySelector('.container h1');

        function checkStatus() {
            fetch('/pinecone-refresh-status/')
                .then(response => response.json())
                .then(data => {
                    const status = data.status;
                    console.log("Current status:", status);
                    if (status === 'completed') {
                        headingElement.textContent = 'Pinecone Index Refreshed!';
                        statusMessageElement.textContent = 'The Pinecone index has been successfully refreshed. You can now close this page.';
                        loaderElement.style.display = 'none';
                        // No automatic redirect, user can close the page.
                    } else if (status === 'failed') {
                        headingElement.textContent = 'Refresh Failed';
                        statusMessageElement.textContent = 'There was an error refreshing the Pinecone index. Please check the server logs for more details.';
                        loaderElement.style.display = 'none';
                        loaderElement.style.borderTop = '16px solid #db3434'; // Change loader to red for error
                    } else if (status === 'processing') {
                        // Optional: update message if you want to show dynamic sub-statuses
                        // statusMessageElement.textContent = 'Currently processing...'; 
                        setTimeout(checkStatus, 3000); // Poll every 3 seconds
                    } else if (status === 'idle') {
                        // This might happen if the page is loaded before refresh is triggered or after a reset
                        headingElement.textContent = 'Pinecone Refresh Ready';
                        statusMessageElement.textContent = 'The Pinecone index refresh process is ready to start or has been reset.';
                        loaderElement.style.display = 'none'; 
                    } else {
                        // Unknown status or page loaded before any status set
                        // Keep polling or show a generic message
                        statusMessageElement.textContent = 'Waiting for refresh process to start...';
                        setTimeout(checkStatus, 3000); // Poll every 3 seconds
                    }
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                    headingElement.textContent = 'Error Checking Status';
                    statusMessageElement.textContent = 'Could not retrieve the refresh status. Please check the server logs and your connection.';
                    loaderElement.style.display = 'none';
                    loaderElement.style.borderTop = '16px solid #db3434';
                });
        }

        // Start checking the status when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Initial call to check status, then it will poll based on response
            checkStatus(); 
        });
    </script>
</body>
</html> 