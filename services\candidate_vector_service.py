"""
Candidate Vector Service Module

Handles vector storage and retrieval for candidate profiles using Pinecone.
Manages the 'leadersscout-candidates' index for semantic candidate search.
"""

import logging
import os
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from uuid import UUID, uuid4

import pinecone
from langchain_openai import OpenAIEmbeddings
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("candidate_vector_service")

# Load environment variables
load_dotenv()

# Pinecone configuration
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
CANDIDATES_INDEX_NAME = "leadersscout-candidates"
EMBEDDING_DIMENSION = 1536  # OpenAI text-embedding-ada-002 dimension
SIMILARITY_METRIC = "cosine"


class CandidateVectorService:
    """Service for managing candidate profile vectors in Pinecone"""

    def __init__(self):
        """Initialize the candidate vector service"""
        self.pc = None
        self.index = None
        self.embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
        self._initialize_pinecone()

    def _initialize_pinecone(self):
        """Initialize Pinecone connection and ensure candidates index exists"""
        try:
            if not PINECONE_API_KEY:
                raise ValueError("PINECONE_API_KEY environment variable not set")

            # Initialize Pinecone
            self.pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)

            # Ensure candidates index exists
            self._ensure_candidates_index_exists()

            # Connect to the index
            self.index = self.pc.Index(CANDIDATES_INDEX_NAME)

            logger.info(
                f"Successfully connected to Pinecone index: {CANDIDATES_INDEX_NAME}"
            )

        except Exception as e:
            logger.error(f"Failed to initialize Pinecone: {str(e)}")
            raise

    def _ensure_candidates_index_exists(self):
        """Create the candidates index if it doesn't exist"""
        try:
            # Check if index exists
            existing_indexes = [index.name for index in self.pc.list_indexes()]

            if CANDIDATES_INDEX_NAME not in existing_indexes:
                logger.info(f"Creating new Pinecone index: {CANDIDATES_INDEX_NAME}")

                # Create the candidates index
                self.pc.create_index(
                    name=CANDIDATES_INDEX_NAME,
                    dimension=EMBEDDING_DIMENSION,
                    metric=SIMILARITY_METRIC,
                    spec=pinecone.ServerlessSpec(
                        cloud="aws", region="us-east-1"  # Adjust region as needed
                    ),
                )

                logger.info(f"Successfully created index: {CANDIDATES_INDEX_NAME}")
            else:
                logger.info(f"Using existing index: {CANDIDATES_INDEX_NAME}")

        except Exception as e:
            logger.error(f"Failed to ensure candidates index exists: {str(e)}")
            raise

    async def store_candidate_profile(
        self,
        candidate_id: str,
        profile_text: str,
        metadata: Dict[str, Any],
        namespace: str = "active",
    ) -> bool:
        """
        Store a candidate profile vector in Pinecone

        Args:
            candidate_id: Unique identifier for the candidate
            profile_text: Full profile text to embed
            metadata: Candidate metadata for filtering
            namespace: Pinecone namespace (active, passive, archived, etc.)

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Storing candidate profile: {candidate_id}")

            # Generate embedding
            embedding = await self.embeddings.aembed_query(profile_text)

            # Prepare metadata (ensure all values are JSON serializable)
            processed_metadata = self._process_metadata(metadata)
            processed_metadata.update(
                {
                    "profile_text_length": len(profile_text),
                    "stored_at": datetime.utcnow().isoformat(),
                    "namespace": namespace,
                }
            )

            # Store in Pinecone
            self.index.upsert(
                vectors=[
                    {
                        "id": candidate_id,
                        "values": embedding,
                        "metadata": processed_metadata,
                    }
                ],
                namespace=namespace,
            )

            logger.info(f"Successfully stored candidate profile: {candidate_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to store candidate profile {candidate_id}: {str(e)}")
            return False

    async def search_candidates(
        self,
        query_text: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 10,
        namespace: str = "active",
        similarity_threshold: float = 0.7,
    ) -> List[Dict[str, Any]]:
        """
        Search for candidates using semantic similarity

        Args:
            query_text: Job description or search query
            filters: Metadata filters for candidate selection
            top_k: Number of results to return
            namespace: Pinecone namespace to search
            similarity_threshold: Minimum similarity score

        Returns:
            List of matching candidates with scores and metadata
        """
        try:
            logger.info(f"Searching candidates with query: {query_text[:100]}...")

            # Generate query embedding
            query_embedding = await self.embeddings.aembed_query(query_text)

            # Prepare filter
            pinecone_filter = self._build_pinecone_filter(filters) if filters else None

            # Search in Pinecone
            search_results = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True,
                namespace=namespace,
                filter=pinecone_filter,
            )

            # Process results
            candidates = []
            for match in search_results.matches:
                if match.score >= similarity_threshold:
                    candidate = {
                        "candidate_id": match.id,
                        "similarity_score": float(match.score),
                        "metadata": match.metadata,
                    }
                    candidates.append(candidate)

            logger.info(
                f"Found {len(candidates)} candidates above threshold {similarity_threshold}"
            )
            return candidates

        except Exception as e:
            logger.error(f"Failed to search candidates: {str(e)}")
            return []

    async def find_similar_candidates(
        self,
        reference_candidate_id: str,
        top_k: int = 10,
        namespace: str = "active",
        exclude_reference: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Find candidates similar to a reference candidate

        Args:
            reference_candidate_id: ID of the reference candidate
            top_k: Number of similar candidates to return
            namespace: Pinecone namespace to search
            exclude_reference: Whether to exclude the reference candidate from results

        Returns:
            List of similar candidates
        """
        try:
            logger.info(f"Finding candidates similar to: {reference_candidate_id}")

            # Get the reference candidate's vector
            fetch_result = self.index.fetch(
                ids=[reference_candidate_id], namespace=namespace
            )

            if reference_candidate_id not in fetch_result.vectors:
                logger.warning(
                    f"Reference candidate {reference_candidate_id} not found"
                )
                return []

            reference_vector = fetch_result.vectors[reference_candidate_id].values

            # Search for similar candidates
            search_results = self.index.query(
                vector=reference_vector,
                top_k=top_k + (1 if exclude_reference else 0),
                include_metadata=True,
                namespace=namespace,
            )

            # Process results
            similar_candidates = []
            for match in search_results.matches:
                if exclude_reference and match.id == reference_candidate_id:
                    continue

                candidate = {
                    "candidate_id": match.id,
                    "similarity_score": float(match.score),
                    "metadata": match.metadata,
                }
                similar_candidates.append(candidate)

            # Limit to requested number
            similar_candidates = similar_candidates[:top_k]

            logger.info(f"Found {len(similar_candidates)} similar candidates")
            return similar_candidates

        except Exception as e:
            logger.error(f"Failed to find similar candidates: {str(e)}")
            return []

    async def update_candidate_profile(
        self,
        candidate_id: str,
        profile_text: str,
        metadata: Dict[str, Any],
        namespace: str = "active",
    ) -> bool:
        """Update an existing candidate profile"""
        # For Pinecone, update is the same as upsert
        return await self.store_candidate_profile(
            candidate_id, profile_text, metadata, namespace
        )

    async def delete_candidate_profile(
        self, candidate_id: str, namespace: str = "active"
    ) -> bool:
        """
        Delete a candidate profile from Pinecone

        Args:
            candidate_id: ID of the candidate to delete
            namespace: Pinecone namespace

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Deleting candidate profile: {candidate_id}")

            self.index.delete(ids=[candidate_id], namespace=namespace)

            logger.info(f"Successfully deleted candidate profile: {candidate_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete candidate profile {candidate_id}: {str(e)}")
            return False

    def get_index_stats(self, namespace: str = "active") -> Dict[str, Any]:
        """
        Get statistics about the candidates index

        Args:
            namespace: Pinecone namespace

        Returns:
            Dictionary with index statistics
        """
        try:
            stats = self.index.describe_index_stats()

            namespace_stats = stats.namespaces.get(namespace, {})

            return {
                "total_vectors": stats.total_vector_count,
                "namespace_vectors": namespace_stats.get("vector_count", 0),
                "dimension": stats.dimension,
                "index_fullness": stats.index_fullness,
                "namespace": namespace,
            }

        except Exception as e:
            logger.error(f"Failed to get index stats: {str(e)}")
            return {}

    def _process_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Process metadata to ensure Pinecone compatibility"""
        processed = {}

        for key, value in metadata.items():
            # Convert non-serializable types
            if isinstance(value, (datetime, UUID)):
                processed[key] = str(value)
            elif isinstance(value, list):
                # Convert lists to strings for Pinecone
                processed[key] = json.dumps(value) if value else ""
            elif isinstance(value, dict):
                # Convert dicts to JSON strings
                processed[key] = json.dumps(value)
            elif value is None:
                processed[key] = ""
            else:
                processed[key] = value

        return processed

    def _build_pinecone_filter(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Build Pinecone-compatible filter from search filters"""
        pinecone_filter = {}

        for key, value in filters.items():
            if isinstance(value, list):
                # Handle list filters (e.g., skills in ['python', 'java'])
                pinecone_filter[key] = {"$in": value}
            elif isinstance(value, dict):
                # Handle range filters (e.g., experience >= 5)
                if "min" in value and "max" in value:
                    pinecone_filter[key] = {"$gte": value["min"], "$lte": value["max"]}
                elif "min" in value:
                    pinecone_filter[key] = {"$gte": value["min"]}
                elif "max" in value:
                    pinecone_filter[key] = {"$lte": value["max"]}
            else:
                # Direct equality filter
                pinecone_filter[key] = {"$eq": value}

        return pinecone_filter

    async def batch_store_candidates(
        self,
        candidates: List[Dict[str, Any]],
        namespace: str = "active",
        batch_size: int = 100,
    ) -> Dict[str, Any]:
        """
        Store multiple candidate profiles in batches

        Args:
            candidates: List of candidate data with id, profile_text, and metadata
            namespace: Pinecone namespace
            batch_size: Number of candidates to process per batch

        Returns:
            Dictionary with success/failure counts
        """
        try:
            logger.info(f"Batch storing {len(candidates)} candidates")

            successful = 0
            failed = 0

            # Process in batches
            for i in range(0, len(candidates), batch_size):
                batch = candidates[i : i + batch_size]
                batch_vectors = []

                for candidate in batch:
                    try:
                        # Generate embedding
                        embedding = await self.embeddings.aembed_query(
                            candidate["profile_text"]
                        )

                        # Prepare metadata
                        metadata = self._process_metadata(candidate["metadata"])
                        metadata.update(
                            {
                                "profile_text_length": len(candidate["profile_text"]),
                                "stored_at": datetime.utcnow().isoformat(),
                                "namespace": namespace,
                            }
                        )

                        batch_vectors.append(
                            {
                                "id": candidate["id"],
                                "values": embedding,
                                "metadata": metadata,
                            }
                        )

                    except Exception as e:
                        logger.error(
                            f"Failed to prepare candidate {candidate.get('id', 'unknown')}: {str(e)}"
                        )
                        failed += 1

                # Store batch in Pinecone
                if batch_vectors:
                    try:
                        self.index.upsert(vectors=batch_vectors, namespace=namespace)
                        successful += len(batch_vectors)
                        logger.info(f"Stored batch of {len(batch_vectors)} candidates")
                    except Exception as e:
                        logger.error(f"Failed to store batch: {str(e)}")
                        failed += len(batch_vectors)

            result = {
                "total_candidates": len(candidates),
                "successful": successful,
                "failed": failed,
                "success_rate": successful / len(candidates) if candidates else 0,
            }

            logger.info(f"Batch storage completed: {result}")
            return result

        except Exception as e:
            logger.error(f"Failed batch storage: {str(e)}")
            return {
                "total_candidates": len(candidates),
                "successful": 0,
                "failed": len(candidates),
                "success_rate": 0.0,
                "error": str(e),
            }


# Initialize service instance
def get_candidate_vector_service():
    """Get initialized candidate vector service"""
    return CandidateVectorService()


# Test function for development
async def test_candidate_vector_service():
    """Test function for the candidate vector service"""
    service = get_candidate_vector_service()

    # Test storing a candidate
    test_candidate = {
        "id": "test-candidate-1",
        "profile_text": "Senior Software Engineer with 5 years of Python experience...",
        "metadata": {
            "candidate_name": "John Doe",
            "experience_level": "senior",
            "programming_languages": ["python", "javascript"],
            "total_years_experience": 5,
        },
    }

    success = await service.store_candidate_profile(
        test_candidate["id"], test_candidate["profile_text"], test_candidate["metadata"]
    )

    if success:
        print("✅ Test candidate stored successfully")

        # Test searching
        results = await service.search_candidates(
            "Python developer with 5 years experience"
        )
        print(f"✅ Search returned {len(results)} results")

        # Test similar candidates
        similar = await service.find_similar_candidates(test_candidate["id"])
        print(f"✅ Similar candidates search returned {len(similar)} results")

        # Get stats
        stats = service.get_index_stats()
        print(f"✅ Index stats: {stats}")

    else:
        print("❌ Failed to store test candidate")


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_candidate_vector_service())
