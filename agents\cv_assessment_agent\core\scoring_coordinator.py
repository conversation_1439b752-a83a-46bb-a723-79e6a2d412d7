"""
Scoring Coordinator <PERSON><PERSON><PERSON>

Coordinates with scoring services and generates final assessments.
Extracted from the original CVAssessmentAgent to provide clean separation of concerns.
"""

import json
import re
import logging
from typing import Optional, Dict, Any
from uuid import UUID
from langchain_openai import ChatOpenAI

from ..utils.prompt_templates import PromptTemplates
from ..utils.language_handler import LanguageHandler
from ..utils.async_helper import AsyncHelper

logger = logging.getLogger("scoring_coordinator")


class ScoringCoordinator:
    """Coordinates scoring services and generates final assessments."""

    def __init__(self, model_name: str = "gpt-4.1"):
        """Initialize the scoring coordinator."""
        self.model = ChatOpenAI(model=model_name, temperature=0.4, max_tokens=4000)

    def generate_final_assessment(
        self,
        cv_text: str,
        qa_history: list,
        template_info: Optional[Dict[str, Any]] = None,
        cv_assessment_id: Optional[UUID] = None,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
        scoring_engine_service=None,
        cv_analyzer=None,
    ) -> dict:
        """
        Generate the final comprehensive assessment.

        Args:
            cv_text: The CV text
            qa_history: Q&A history
            template_info: Template configuration
            cv_assessment_id: CV assessment ID for scoring engine
            template_id: Template ID for scoring engine
            job_id: Job ID for scoring engine
            scoring_engine_service: Optional scoring engine service
            cv_analyzer: Optional CV analyzer for contact extraction

        Returns:
            Final assessment dictionary
        """
        try:
            # Try comprehensive scoring engine first if available
            if scoring_engine_service and cv_assessment_id:
                comprehensive_result = self._generate_comprehensive_assessment(
                    cv_text,
                    qa_history,
                    cv_assessment_id,
                    template_id,
                    job_id,
                    scoring_engine_service,
                    cv_analyzer,
                )

                if comprehensive_result:
                    return comprehensive_result

            # Fall back to AI-based assessment
            logger.info("Using AI-based assessment as fallback")
            return self._generate_ai_assessment(
                cv_text, qa_history, template_info, cv_analyzer
            )

        except Exception as e:
            logger.error(f"Error generating final assessment: {str(e)}")
            return self._generate_error_assessment(cv_analyzer, cv_text)

    def _generate_comprehensive_assessment(
        self,
        cv_text: str,
        qa_history: list,
        cv_assessment_id: UUID,
        template_id: Optional[UUID],
        job_id: Optional[UUID],
        scoring_engine_service,
        cv_analyzer=None,
    ) -> Optional[dict]:
        """Generate assessment using the comprehensive scoring engine."""
        try:
            logger.info(
                f"Using comprehensive scoring engine for assessment {cv_assessment_id}"
            )

            # Calculate comprehensive score using the scoring engine
            scoring_result = AsyncHelper.safe_async_call(
                scoring_engine_service.calculate_comprehensive_score,
                {},  # default_value
                cv_assessment_id=cv_assessment_id,
                template_id=template_id,
                job_id=job_id,
            )

            if scoring_result.get("success"):
                # Extract contact info for the response
                name, email = self._extract_contact_info(cv_text, cv_analyzer)

                # Use the comprehensive score and breakdown
                final_score = scoring_result.get("final_weighted_score", 0)
                score_breakdown = scoring_result.get("score_breakdown", {})
                ai_scores = scoring_result.get("ai_component_scores", {})

                # Generate user-friendly message
                user_message = self._generate_user_friendly_message(
                    final_score, score_breakdown, cv_text, qa_history
                )

                # Generate detailed description
                description = self._generate_comprehensive_description(
                    cv_text, qa_history, scoring_result
                )

                return {
                    "name": name or "Unknown",
                    "email": email or "Not provided",
                    "score": int(final_score),
                    "description": description,
                    "user_message": user_message,
                    "scoring_breakdown": score_breakdown,
                    "ai_component_scores": ai_scores,
                    "template_used": scoring_result.get("template_used", "default"),
                }
            else:
                logger.warning(
                    f"Scoring engine failed: {scoring_result.get('error', 'Unknown error')}"
                )
                return None

        except Exception as e:
            logger.error(f"Error using comprehensive scoring engine: {str(e)}")
            return None

    def _generate_ai_assessment(
        self,
        cv_text: str,
        qa_history: list,
        template_info: Optional[Dict[str, Any]],
        cv_analyzer=None,
    ) -> dict:
        """Generate assessment using AI analysis as fallback."""
        try:
            # Get template info or use defaults
            if not template_info:
                template_info = self._get_default_template_info()

            # Determine current language from Q&A history
            current_language = LanguageHandler.extract_language_from_qa_history(
                qa_history
            )

            # Get language-specific prompt additions
            language_additions = LanguageHandler.get_language_specific_prompt_additions(
                current_language
            )
            language_instruction = language_additions.get("language_instruction", "")

            # Format Q&A history
            qa_str = self._format_qa_history(qa_history)

            # Generate assessment using AI
            prompt = PromptTemplates.get_final_assessment_prompt(
                cv_text, qa_str, template_info, language_instruction
            )

            response = self.model.invoke(prompt)
            response_text = (
                response.content if hasattr(response, "content") else str(response)
            )

            # Parse JSON response
            return self._parse_assessment_response(response_text)

        except Exception as e:
            logger.error(f"Error in AI assessment generation: {str(e)}")
            return self._generate_error_assessment(cv_analyzer, cv_text)

    def _extract_contact_info(self, cv_text: str, cv_analyzer=None) -> tuple:
        """Extract contact information using CV analyzer if available."""
        if cv_analyzer:
            return cv_analyzer.extract_contact_info(cv_text)

        # Fallback: simple regex extraction
        try:
            # Extract email
            email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
            email_match = re.search(email_pattern, cv_text)
            email = email_match.group() if email_match else None

            # Extract name (simple heuristic - first few words that look like names)
            lines = cv_text.split("\n")
            name = None
            for line in lines[:5]:  # Check first 5 lines
                line = line.strip()
                if len(line.split()) <= 4 and len(line) > 5:  # Likely a name
                    # Check if it's not an email or phone number
                    if "@" not in line and not re.search(r"\d{10}", line):
                        name = line
                        break

            return name, email
        except Exception:
            return None, None

    def _generate_user_friendly_message(
        self,
        final_score: float,
        score_breakdown: Dict[str, Any],
        cv_text: str,
        qa_history: list,
    ) -> str:
        """Generate a user-friendly message about the assessment results."""
        try:
            # Extract key strengths from breakdown
            explanations = score_breakdown.get("explanation", [])

            if final_score >= 85:
                message_start = "Excellent performance! Your assessment shows strong qualifications across multiple areas."
            elif final_score >= 70:
                message_start = "Good performance overall with solid qualifications."
            elif final_score >= 55:
                message_start = (
                    "Your assessment shows potential with some areas for development."
                )
            else:
                message_start = "Thank you for completing the assessment. There are opportunities for growth."

            # Add specific insights if available
            if explanations:
                insight = explanations[0] if explanations else ""
                return f"{message_start} {insight}"

            return f"{message_start} We appreciate the time you took to complete this comprehensive assessment."

        except Exception as e:
            logger.error(f"Error generating user message: {str(e)}")
            return "Thank you for completing the assessment. We'll review your responses and get back to you soon."

    def _generate_comprehensive_description(
        self, cv_text: str, qa_history: list, scoring_result: Dict[str, Any]
    ) -> str:
        """Generate a comprehensive description using scoring engine results."""
        try:
            score_breakdown = scoring_result.get("score_breakdown", {})
            ai_scores = scoring_result.get("ai_component_scores", {})
            final_score = scoring_result.get("final_weighted_score", 0)

            description_parts = []

            # Overall assessment
            description_parts.append(
                f"Comprehensive Assessment Score: {final_score:.1f}/100"
            )

            # AI component breakdown
            if ai_scores:
                description_parts.append("\nComponent Analysis:")
                for component, score in ai_scores.items():
                    component_name = component.replace("_", " ").title()
                    description_parts.append(f"- {component_name}: {score:.1f}/100")

            # Detailed explanations
            explanations = score_breakdown.get("explanation", [])
            if explanations:
                description_parts.append("\nDetailed Analysis:")
                for explanation in explanations:
                    description_parts.append(f"• {explanation}")

            # Summary from breakdown
            summary = score_breakdown.get("summary", {})
            if summary:
                ai_total = summary.get("ai_total_score", 0)
                human_total = summary.get("human_total_score", 0)

                description_parts.append(f"\nScoring Summary:")
                description_parts.append(f"- AI Assessment: {ai_total:.1f}/100")
                if human_total > 0:
                    description_parts.append(
                        f"- Human Assessment: {human_total:.1f}/100"
                    )
                else:
                    description_parts.append("- Human Assessment: Pending")

            return "\n".join(description_parts)

        except Exception as e:
            logger.error(f"Error generating comprehensive description: {str(e)}")
            # Fall back to simpler description
            return f"Assessment completed with a score of {scoring_result.get('final_weighted_score', 0):.1f}/100. Detailed analysis includes CV evaluation and questionnaire responses."

    def _format_qa_history(self, qa_history: list) -> str:
        """Format Q&A history for prompt inclusion."""
        if not qa_history:
            return "None"

        formatted_pairs = []
        for qa in qa_history:
            question = qa.get("question", "")
            answer = qa.get("answer", "")
            if question:
                formatted_pairs.append(f"Q: {question}\nA: {answer}")

        return "\n".join(formatted_pairs) if formatted_pairs else "None"

    def _parse_assessment_response(self, response_text: str) -> dict:
        """Parse the AI assessment response."""
        try:
            # Clean response text by removing control characters
            cleaned_text = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", response_text)

            # Extract JSON from response
            json_match = re.search(r"\{[\s\S]*\}", cleaned_text)
            if json_match:
                result = json.loads(json_match.group(0))

                # Validate required keys
                required_keys = {
                    "name",
                    "email",
                    "score",
                    "description",
                    "user_message",
                }
                if not required_keys.issubset(result.keys()):
                    missing = ", ".join(required_keys - result.keys())
                    raise ValueError(f"Missing required fields: {missing}")

                # Validate score range
                score_val = result["score"]
                if not isinstance(score_val, (int, float)) or not 0 <= score_val <= 100:
                    raise ValueError("Score must be a number between 0 and 100")

                # Ensure user_message not empty
                if not result["user_message"].strip():
                    result["user_message"] = (
                        "Based on your CV and responses, here's your assessment..."
                    )

                return result
            else:
                raise ValueError("No JSON found in response")

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Error parsing assessment response: {str(e)}")
            return self._generate_error_assessment()

    def _generate_error_assessment(self, cv_analyzer=None, cv_text: str = "") -> dict:
        """Generate a basic error assessment when all else fails."""
        name, email = (
            self._extract_contact_info(cv_text, cv_analyzer)
            if cv_text
            else (None, None)
        )

        return {
            "name": name or "Unknown",
            "email": email or "Not provided",
            "score": 0,
            "description": "Assessment could not be completed due to technical issues. Please try again.",
            "user_message": "We apologize, but there was an issue processing your assessment. Please contact support for assistance.",
        }

    def _get_default_template_info(self) -> Dict[str, Any]:
        """Get default template information when none is provided."""
        return {
            "template_id": "default",
            "template_name": "Default Template",
            "scoring_weights": {
                "cv_analysis": 0.30,
                "questionnaire": 0.30,
                "case_study": 0.20,
                "psychometric": 0.10,
                "background_check": 0.10,
            },
            "ai_percentage": 0.70,
            "human_percentage": 0.30,
            "category_weights": {},
            "reason": "Using default template configuration",
        }
