"""
CV Analyzer Module

Handles CV text analysis, contact information extraction, and experience level detection.
Extracted from the original CVAssessmentAgent to provide clean separation of concerns.
"""

import json
import re
import logging
from typing import Optional, Tuple, Dict, Any
from langchain_openai import ChatOpenAI
from ..utils.prompt_templates import PromptTemplates

logger = logging.getLogger("cv_analyzer")


class CVAnalyzer:
    """Handles CV analysis and information extraction."""

    def __init__(self, model_name: str = "gpt-4.1"):
        """Initialize the CV analyzer."""
        self.model = ChatOpenAI(model=model_name, temperature=0.4, max_tokens=4000)

    def extract_contact_info(self, cv_text: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract candidate name and email from CV text.

        Args:
            cv_text: The CV text to analyze

        Returns:
            Tuple of (name, email) or (None, None) if extraction fails
        """
        try:
            prompt = PromptTemplates.get_cv_analysis_prompt(cv_text)
            response = self.model.invoke(prompt)
            response_text = (
                response.content if hasattr(response, "content") else str(response)
            )

            # Parse JSON response
            json_match = re.search(r"\{[\s\S]*\}", response_text)
            if json_match:
                contact_data = json.loads(json_match.group(0))
                name = contact_data.get("name") or None
                email = contact_data.get("email") or None
                return name, email

        except Exception as e:
            logger.error(f"Error extracting contact info: {str(e)}")

        return None, None

    def extract_experience_level(self, cv_text: str) -> Optional[str]:
        """
        Extract experience level from CV text using AI analysis.

        Args:
            cv_text: The CV text to analyze

        Returns:
            Experience level: 'entry', 'mid', 'senior', or 'executive'
        """
        try:
            # First try pattern-based detection for quick results
            pattern_result = self._extract_experience_level_patterns(cv_text)
            if pattern_result:
                return pattern_result

            # Fall back to AI analysis for more complex cases
            return self._extract_experience_level_ai(cv_text)

        except Exception as e:
            logger.error(f"Error extracting experience level: {str(e)}")
            return "mid"  # Default fallback

    def _extract_experience_level_patterns(self, cv_text: str) -> Optional[str]:
        """Extract experience level using pattern matching (fast)."""
        cv_lower = cv_text.lower()

        # Define patterns for each experience level
        patterns = {
            "entry": [
                r"\b(0-2|zero to two|one|two)\s+years?\s+(?:of\s+)?experience\b",
                r"\b(fresh|recent)\s+graduate\b",
                r"\bintern(?:ship)?\b",
                r"\bjunior\b",
                r"\bentry\s+level\b",
            ],
            "mid": [
                r"\b(3-5|three to five|four|five)\s+years?\s+(?:of\s+)?experience\b",
                r"\bmid-?(?:level|senior)\b",
                r"\bintermediate\b",
            ],
            "senior": [
                r"\b(6-10|six to ten|seven|eight|nine|ten)\s+years?\s+(?:of\s+)?experience\b",
                r"\bsenior\b(?!\s+vice|\s+director)",
                r"\blead\b",
                r"\bprincipal\b",
            ],
            "executive": [
                r"\b(?:10\+|10\s*\+|over\s+10|more\s+than\s+10)\s+years?\s+(?:of\s+)?experience\b",
                r"\b(?:vp|vice\s+president)\b",
                r"\bdirector\b",
                r"\bexecutive\b",
                r"\bc-?level\b",
                r"\bchief\b",
            ],
        }

        # Check patterns
        for level, patterns_list in patterns.items():
            for pattern in patterns_list:
                if re.search(pattern, cv_lower):
                    logger.info(f"Pattern-detected experience level: {level}")
                    return level

        # Try to count years mentioned
        years_pattern = r"\b(\d+)\s+years?\s+(?:of\s+)?(?:experience|working)\b"
        years_matches = re.findall(years_pattern, cv_lower)
        if years_matches:
            max_years = max(int(y) for y in years_matches)
            if max_years <= 2:
                return "entry"
            elif max_years <= 5:
                return "mid"
            elif max_years <= 10:
                return "senior"
            else:
                return "executive"

        return None  # No pattern matched

    def _extract_experience_level_ai(self, cv_text: str) -> str:
        """Extract experience level using AI analysis (more accurate but slower)."""
        try:
            prompt = PromptTemplates.get_experience_level_prompt(cv_text)
            response = self.model.invoke(prompt)
            response_text = (
                (response.content if hasattr(response, "content") else str(response))
                .strip()
                .lower()
            )

            # Validate response
            valid_levels = ["entry", "mid", "senior", "executive"]
            if response_text in valid_levels:
                logger.info(f"AI-detected experience level: {response_text}")
                return response_text

            # Try to extract from response if it contains extra text
            for level in valid_levels:
                if level in response_text:
                    return level

        except Exception as e:
            logger.error(f"Error in AI experience level extraction: {str(e)}")

        return "mid"  # Default fallback

    def analyze_cv_structure(self, cv_text: str) -> Dict[str, Any]:
        """
        Analyze CV structure and quality for formatting scoring.

        Args:
            cv_text: The CV text to analyze

        Returns:
            Dictionary with structure analysis results
        """
        analysis = {
            "length": len(cv_text),
            "word_count": len(cv_text.split()),
            "has_sections": False,
            "has_contact_info": False,
            "has_education": False,
            "has_experience": False,
            "formatting_score": 0.0,
        }

        cv_lower = cv_text.lower()

        # Check for common CV sections
        sections = [
            "education",
            "experience",
            "skills",
            "objective",
            "summary",
            "achievements",
            "projects",
            "certifications",
            "languages",
        ]

        found_sections = []
        for section in sections:
            if section in cv_lower:
                found_sections.append(section)

        analysis["has_sections"] = len(found_sections) >= 3
        analysis["found_sections"] = found_sections

        # Check for contact information
        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        phone_pattern = (
            r"\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b"
        )

        analysis["has_contact_info"] = bool(
            re.search(email_pattern, cv_text) or re.search(phone_pattern, cv_text)
        )

        # Check for education keywords
        education_keywords = [
            "degree",
            "university",
            "college",
            "bachelor",
            "master",
            "phd",
            "graduate",
        ]
        analysis["has_education"] = any(
            keyword in cv_lower for keyword in education_keywords
        )

        # Check for experience keywords
        experience_keywords = [
            "work",
            "job",
            "position",
            "role",
            "company",
            "employer",
            "career",
        ]
        analysis["has_experience"] = any(
            keyword in cv_lower for keyword in experience_keywords
        )

        # Calculate basic formatting score
        score = 0.0
        if analysis["has_contact_info"]:
            score += 0.25
        if analysis["has_education"]:
            score += 0.25
        if analysis["has_experience"]:
            score += 0.25
        if analysis["has_sections"]:
            score += 0.25

        # Bonus for good length
        if 500 <= analysis["word_count"] <= 2000:
            score += 0.1

        analysis["formatting_score"] = min(1.0, score)

        return analysis

    def extract_key_information(self, cv_text: str) -> Dict[str, Any]:
        """
        Extract key information from CV for assessment purposes.

        Args:
            cv_text: The CV text to analyze

        Returns:
            Dictionary with extracted key information
        """
        # Get contact info
        name, email = self.extract_contact_info(cv_text)

        # Get experience level
        experience_level = self.extract_experience_level(cv_text)

        # Get structure analysis
        structure_analysis = self.analyze_cv_structure(cv_text)

        return {
            "name": name,
            "email": email,
            "experience_level": experience_level,
            "structure_analysis": structure_analysis,
            "cv_length": len(cv_text),
            "word_count": len(cv_text.split()),
        }
