<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Candidate Search - Testing Interface</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #555;
      }
      input,
      textarea,
      select {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        box-sizing: border-box;
      }
      textarea {
        height: 120px;
        resize: vertical;
      }
      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }
      .form-row-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px;
      }
      button {
        background: #007bff;
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
      }
      button:hover {
        background: #0056b3;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .loading {
        display: none;
        text-align: center;
        padding: 20px;
        color: #666;
      }
      .results {
        display: none;
      }
      .candidate-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        background: #fafafa;
      }
      .candidate-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      .candidate-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      .candidate-score {
        background: #28a745;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 600;
      }
      .candidate-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
      }
      .detail-item {
        font-size: 14px;
      }
      .detail-label {
        font-weight: 600;
        color: #666;
      }
      .candidate-summary {
        background: white;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #007bff;
        margin-top: 15px;
      }
      .score-breakdown {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        margin-top: 15px;
      }
      .score-item {
        background: white;
        padding: 10px;
        border-radius: 6px;
        text-align: center;
        border: 1px solid #eee;
      }
      .score-value {
        font-size: 18px;
        font-weight: 600;
        color: #007bff;
      }
      .score-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 6px;
        margin-top: 20px;
      }
      .nav-links {
        text-align: center;
        margin-bottom: 20px;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 500;
      }
      .nav-links a:hover {
        text-decoration: underline;
      }
      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
      }
      .tag {
        background: #007bff;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .tag-skill {
        background: #ffc107;
        color: #333;
      }
      .tag-certification {
        background: #dc3545;
      }
    </style>
  </head>
  <body>
    <div class="nav-links">
      <a href="/candidate-search">Candidate Search</a>
      <a href="/job-creation">Job Creation</a>
      <a href="/cv-assessments/">CV Assessments</a>
    </div>

    <div class="container">
      <h1>🔍 Candidate Search</h1>

      <form id="searchForm">
        <div class="form-group">
          <label for="jobDescription">Job Description / Requirements:</label>
          <textarea
            id="jobDescription"
            placeholder="Enter job description, requirements, or search query..."
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="minExperience">Min Experience (years):</label>
            <input type="number" id="minExperience" min="0" max="50" />
          </div>
          <div class="form-group">
            <label for="maxExperience">Max Experience (years):</label>
            <input type="number" id="maxExperience" min="0" max="50" />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="minSalary">Min Salary:</label>
            <input type="number" id="minSalary" min="0" step="1000" />
          </div>
          <div class="form-group">
            <label for="maxSalary">Max Salary:</label>
            <input type="number" id="maxSalary" min="0" step="1000" />
          </div>
        </div>

        <div class="form-row-3">
          <div class="form-group">
            <label for="location">Location:</label>
            <input
              type="text"
              id="location"
              placeholder="e.g., New York, Remote"
            />
          </div>
          <div class="form-group">
            <label for="limit">Results Limit:</label>
            <select id="limit">
              <option value="10">10 results</option>
              <option value="20">20 results</option>
              <option value="50">50 results</option>
            </select>
          </div>
          <div class="form-group">
            <label for="remoteWork">Remote Work:</label>
            <select id="remoteWork">
              <option value="">Any Preference</option>
              <option value="open">Open to Remote</option>
              <option value="hybrid">Hybrid</option>
              <option value="onsite">On-site Only</option>
            </select>
          </div>
        </div>

        <div class="form-row-3">
          <div class="form-group">
            <label for="experienceLevel">Experience Level:</label>
            <select id="experienceLevel">
              <option value="">Any Level</option>
              <option value="fresh">Fresh</option>
              <option value="junior">Junior</option>
              <option value="mid">Mid</option>
              <option value="senior">Senior</option>
            </select>
          </div>
          <div class="form-group">
            <label for="degreeLevel">Degree Level:</label>
            <select id="degreeLevel">
              <option value="">Any Degree</option>
              <option value="high_school">High School</option>
              <option value="bachelors">Bachelor's</option>
              <option value="masters">Master's</option>
              <option value="phd">PhD</option>
            </select>
          </div>
        </div>

        <button type="submit">Search Candidates</button>
      </form>

      <div class="loading" id="loading">
        <p>🔍 Searching candidates...</p>
      </div>

      <div class="error" id="error" style="display: none"></div>
    </div>

    <div class="container results" id="results">
      <h2>Search Results</h2>
      <div id="resultsContainer"></div>
    </div>

    <script>
      document
        .getElementById("searchForm")
        .addEventListener("submit", async function (e) {
          e.preventDefault();

          const loading = document.getElementById("loading");
          const results = document.getElementById("results");
          const error = document.getElementById("error");
          const resultsContainer = document.getElementById("resultsContainer");

          // Show loading, hide results and errors
          loading.style.display = "block";
          results.style.display = "none";
          error.style.display = "none";

          // Collect form data
          const searchData = {
            job_description: document.getElementById("jobDescription").value,
            filters: {},
          };

          // Add filters if provided
          const minExp = document.getElementById("minExperience").value;
          const maxExp = document.getElementById("maxExperience").value;
          const minSal = document.getElementById("minSalary").value;
          const maxSal = document.getElementById("maxSalary").value;
          const location = document.getElementById("location").value;
          const limit = parseInt(document.getElementById("limit").value);
          const searchType = document.getElementById("searchType").value;
          const experienceLevel = document.getElementById("experienceLevel").value;
          const degreeLevel = document.getElementById("degreeLevel").value;
          const remoteWork = document.getElementById("remoteWork").value;

          if (minExp)
            searchData.filters.min_experience_years = parseInt(minExp);
          if (maxExp)
            searchData.filters.max_experience_years = parseInt(maxExp);
          if (minSal) searchData.filters.min_salary = parseInt(minSal);
          if (maxSal) searchData.filters.max_salary = parseInt(maxSal);
          if (location) searchData.filters.location = location;
          if (limit) searchData.limit = limit;
          if (experienceLevel) searchData.filters.experience_level = experienceLevel;
          if (degreeLevel) searchData.filters.degree_level = degreeLevel;
          if (remoteWork) searchData.filters.remote_work_preference = remoteWork;

          try {
            const endpoint =
              searchType === "semantic"
                ? "/candidates/search"
                : "/candidates/advanced-search";
            const response = await fetch(endpoint, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(searchData),
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            displayResults(data);
          } catch (err) {
            console.error("Search error:", err);
            error.textContent = `Search failed: ${err.message}`;
            error.style.display = "block";
          } finally {
            loading.style.display = "none";
          }
        });

      function displayResults(data) {
        const resultsContainer = document.getElementById("resultsContainer");
        const results = document.getElementById("results");

        if (!data.candidates || data.candidates.length === 0) {
          resultsContainer.innerHTML =
            "<p>No candidates found matching your criteria.</p>";
          results.style.display = "block";
          return;
        }

        // Remove duplicates by candidate_id if present
        const seen = new Set();
        const uniqueCandidates = data.candidates.filter(c => {
          if (!c.candidate_id) return true;
          if (seen.has(c.candidate_id)) return false;
          seen.add(c.candidate_id);
          return true;
        });

        let html = `<p><strong>${uniqueCandidates.length}</strong> candidates found</p>`;

        uniqueCandidates.forEach((candidate, idx) => {
          // Parse JSON fields if needed
          let technicalSkills = [];
          let softSkills = [];
          let certifications = [];
          try { if (typeof candidate.technical_skills === 'string') technicalSkills = JSON.parse(candidate.technical_skills); else technicalSkills = candidate.technical_skills || []; } catch {}
          try { if (typeof candidate.soft_skills === 'string') softSkills = JSON.parse(candidate.soft_skills); else softSkills = candidate.soft_skills || []; } catch {}
          try { if (typeof candidate.certifications === 'string') certifications = JSON.parse(candidate.certifications); else certifications = candidate.certifications || []; } catch {}

          // Robust field extraction
          let experience = "N/A";
          if (candidate.total_years_experience !== undefined && candidate.total_years_experience !== null) {
            experience = candidate.total_years_experience;
          }
          const location = candidate.location_city && candidate.location_country ? `${candidate.location_city}, ${candidate.location_country}` : (candidate.location_city || candidate.location_country || "N/A");
          const degree = candidate.degree_level || "N/A";
          const industry = candidate.industry_primary || "N/A";

          html += `
            <div class="candidate-card" id="candidate-card-${idx}">
                <div class="candidate-header">
                    <div class="candidate-name">${
                      candidate.candidate_name ||
                      candidate.name ||
                      "Unknown"
                    }</div>
                </div>
                <div class="candidate-details">
                    <div class="detail-item">
                        <div class="detail-label">Experience:</div>
                        <div class="candidate-experience">${experience} years</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Location:</div>
                        <div class="candidate-location">${location}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Current Role:</div>
                        <div class="candidate-role">${candidate.current_role || "N/A"}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Experience Level:</div>
                        <div class="candidate-exp-level">${candidate.experience_level || "N/A"}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Degree Level:</div>
                        <div class="candidate-degree">${degree}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Industry:</div>
                        <div class="candidate-industry">${industry}</div>
                    </div>
                </div>
                <div class="candidate-profile-loading" style="color:#888; font-size:13px;">Loading full profile...</div>
                <div style="margin-top: 15px;">
                    <a href="/candidate-profile/${
                      candidate.candidate_id ||
                      candidate.cv_assessment_id ||
                      candidate.id ||
                      "unknown"
                    }" target="_blank" style="color: #007bff; text-decoration: none;">View Full Profile →</a>
                </div>
            </div>
          `;
        });

        resultsContainer.innerHTML = html;
        results.style.display = "block";

        // Fetch full profile for each candidate and update the card
        uniqueCandidates.forEach((candidate, idx) => {
          const candidateId = candidate.candidate_id || candidate.cv_assessment_id || candidate.id;
          if (!candidateId) return;
          fetch(`/candidates/${candidateId}`)
            .then(resp => resp.ok ? resp.json() : null)
            .then(data => {
              if (!data || !data.candidate) return;
              const full = data.candidate;
              const card = document.getElementById(`candidate-card-${idx}`);
              if (!card) return;
              // Update fields
              card.querySelector('.candidate-experience').textContent = (full.total_years_experience !== undefined && full.total_years_experience !== null) ? `${full.total_years_experience} years` : 'N/A';
              card.querySelector('.candidate-location').textContent = full.location_city && full.location_country ? `${full.location_city}, ${full.location_country}` : (full.location_city || full.location_country || 'N/A');
              card.querySelector('.candidate-role').textContent = full.current_role || 'N/A';
              card.querySelector('.candidate-exp-level').textContent = full.experience_level || 'N/A';
              card.querySelector('.candidate-degree').textContent = full.degree_level || 'N/A';
              card.querySelector('.candidate-industry').textContent = full.industry_primary || 'N/A';
              // Remove loading
              const loading = card.querySelector('.candidate-profile-loading');
              if (loading) loading.remove();
            })
            .catch(() => {});
        });
      }

      function parseSkills(skillsString, tagClass) {
        try {
          const skills = JSON.parse(skillsString);
          if (skills && Array.isArray(skills)) {
            return skills.slice(0, 8).map(skill => 
              `<span class="tag ${tagClass}">${skill}</span>`
            ).join('');
          }
        } catch (e) {
          console.error("Error parsing skills:", e);
        }
        return '';
      }
    </script>
  </body>
</html>
