"""
Shared Data Models Module

This module contains shared data models used across the application.
It helps avoid circular imports by providing a central location for data structures.
"""

import re
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from decimal import Decimal
from enum import Enum
import json
from utils.assessment_config import AssessmentConfig


class LeadInfo(BaseModel):
    """Model for storing lead information"""

    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    lead_type: Optional[str] = None  # 'company' or 'recruitment_partner'
    company: Optional[str] = None
    industry: Optional[str] = None
    num_locations: Optional[int] = None
    num_employees: Optional[int] = None
    additional_message: Optional[str] = None
    consent_given: bool = False


def format_phone_number(phone: str) -> str:
    """
    Format a phone number to ensure it has the correct format for WhatsApp.
    Ensures the number has a + prefix for international format.

    Args:
        phone: The phone number to format

    Returns:
        Formatted phone number with + prefix
    """
    if not phone:
        return phone

    # Remove any non-digit characters except the + sign
    phone = re.sub(r"[^\d+]", "", phone)

    # If the number already has a + prefix, return it as is
    if phone.startswith("+"):
        return phone

    # If the number starts with 0, assume it's a Saudi number and replace the 0 with +966
    if phone.startswith("0"):
        return "+966" + phone[1:]

    # If the number has no prefix, assume it's a Saudi number
    return "+966" + phone


class CustomAnswer(BaseModel):
    """Model for storing custom answer information"""

    question_pattern: str
    answer: str
    embedding: Optional[List[float]] = (
        None  # Not visible to admin, only used internally
    )


# =====================================================
# PHASE 2 MODELS - HIERARCHICAL TAG SYSTEM
# =====================================================


class TagDataType(str, Enum):
    """Enum for tag data types"""

    TEXT = "text"
    NUMBER = "number"
    BOOLEAN = "boolean"
    DATE = "date"
    LIST = "list"


class TagSource(str, Enum):
    """Enum for tag extraction sources"""

    CV = "cv"
    QUESTION = "question"
    MANUAL = "manual"
    INFERENCE = "inference"
    CV_QUESTION = "cv + questions"
    CV_MANUAL = "cv + manual"
    QUESTION_MANUAL = "question + manual"
    CV_QUESTION_MANUAL = "cv + questions + manual"


class TagCategory(BaseModel):
    """Model for tag categories (main categories like Education, Experience, etc.)"""

    id: Optional[UUID] = None
    name: str = Field(..., description="Unique name identifier (e.g., 'education')")
    display_name: str = Field(
        ..., description="Human-readable name (e.g., 'Education')"
    )
    description: Optional[str] = None
    default_threshold: Decimal = Field(default=Decimal("0.70"), ge=0, le=1)
    priority: int = Field(default=0, description="Display/processing priority")
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


class TagDefinition(BaseModel):
    """Model for tag definitions (subtags within each category)"""

    id: Optional[UUID] = None
    category_id: UUID
    tag_key: str = Field(
        ..., description="Unique key within category (e.g., 'degree_level')"
    )
    display_name: str = Field(..., description="Human-readable name")
    description: Optional[str] = None
    data_type: TagDataType = TagDataType.TEXT
    validation_rules: Optional[Dict[str, Any]] = Field(
        default=None, description="JSON validation rules"
    )
    extraction_hints: List[str] = Field(
        default_factory=list, description="Keywords to help AI extraction"
    )
    question_templates: List[str] = Field(
        default_factory=list, description="Questions to fill this tag"
    )
    is_required: bool = False
    weight: Decimal = Field(
        default=Decimal("1.0"), ge=0, description="Relative weight within category"
    )
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


class CandidateTag(BaseModel):
    """Model for candidate tags extracted from CVs and assessments"""

    id: Optional[UUID] = None
    cv_assessment_id: int  # References cv_assessments(id) which is BIGSERIAL
    tag_definition_id: UUID
    value: str = Field(..., description="The extracted/provided value")
    confidence_score: Decimal = Field(default=Decimal("1.0"), ge=0, le=1)
    source: TagSource
    extracted_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # Relationships (populated when needed)
    tag_definition: Optional[TagDefinition] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


class CandidateTagCoverage(BaseModel):
    """Model for tracking tag coverage per category for each assessment"""

    id: Optional[UUID] = None
    cv_assessment_id: int  # References cv_assessments(id) which is BIGSERIAL
    category_id: UUID
    total_tags: int = Field(default=0, ge=0)
    filled_tags: int = Field(default=0, ge=0)
    coverage_percentage: Decimal = Field(default=Decimal("0.00"), ge=0, le=100)
    threshold_percentage: Decimal = Field(default=Decimal("70.00"), ge=0, le=100)
    is_complete: bool = False
    last_calculated: Optional[datetime] = None

    # Relationships (populated when needed)
    category: Optional[TagCategory] = None

    @validator("coverage_percentage", always=True)
    def calculate_coverage(cls, v, values):
        """Auto-calculate coverage percentage if not provided"""
        if (
            "total_tags" in values
            and "filled_tags" in values
            and values["total_tags"] > 0
        ):
            return Decimal(
                str(round((values["filled_tags"] / values["total_tags"]) * 100, 2))
            )
        return v

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


class CoverageThreshold(BaseModel):
    """Model for configurable coverage thresholds per job/template"""

    id: Optional[UUID] = None
    job_id: Optional[UUID] = None
    template_id: Optional[UUID] = None
    category_id: UUID
    required_percentage: Decimal = Field(..., ge=0, le=100)
    is_mandatory: bool = True
    created_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


# =====================================================
# PHASE 2 MODELS - SCORING TEMPLATES & ASSESSMENT
# =====================================================


class JobLevel(str, Enum):
    """Enum for job experience levels"""

    ENTRY = "entry"
    JUNIOR = "junior"
    MID = "mid"
    SENIOR = "senior"
    EXECUTIVE = "executive"


class ScoringTemplate(BaseModel):
    """Model for scoring templates with weighted components"""

    id: Optional[UUID] = None
    name: str
    description: Optional[str] = None
    industry: Optional[str] = None
    job_level: Optional[JobLevel] = None
    is_default: bool = False
    is_active: bool = True

    # Component weights (must sum to 1.0)
    cv_analysis_weight: Decimal = Field(default=Decimal("0.30"), ge=0, le=1)
    questionnaire_weight: Decimal = Field(default=Decimal("0.30"), ge=0, le=1)
    case_study_weight: Decimal = Field(default=Decimal("0.20"), ge=0, le=1)
    psychometric_weight: Decimal = Field(default=Decimal("0.10"), ge=0, le=1)
    background_check_weight: Decimal = Field(default=Decimal("0.10"), ge=0, le=1)

    # AI/Human ratio
    ai_percentage: Decimal = Field(default=Decimal("0.70"), ge=0, le=1)
    human_percentage: Decimal = Field(default=Decimal("0.30"), ge=0, le=1)

    # Tag category weights (category_id as key, weight as value)
    category_weights: Dict[str, float] = Field(default_factory=dict)

    created_by: Optional[UUID] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @validator("background_check_weight")
    def validate_component_weights(cls, v, values):
        """Ensure component weights sum to 1.0 - only validate when all weights are set"""
        # Only validate when we have all weight values (last field in the model)
        weights = [
            values.get("cv_analysis_weight", Decimal("0.30")),
            values.get("questionnaire_weight", Decimal("0.30")),
            values.get("case_study_weight", Decimal("0.20")),
            values.get("psychometric_weight", Decimal("0.10")),
            v,  # background_check_weight (current field)
        ]
        total = sum(weights)
        if total != Decimal("1.0"):
            raise ValueError(f"Component weights must sum to 1.0, got {total}")
        return v

    @validator("ai_percentage")
    def validate_ai_human_ratio(cls, v, values):
        """Ensure AI and human percentages sum to 1.0"""
        if "human_percentage" in values:
            if v + values["human_percentage"] != Decimal("1.0"):
                raise ValueError("AI and human percentages must sum to 1.0")
        return v

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


class TemplateEmbedding(BaseModel):
    """Model for template vector embeddings"""

    id: Optional[UUID] = None
    template_id: UUID
    embedding_text: str = Field(..., description="Combined text used for embedding")
    embedding_vector: Optional[List[float]] = None
    created_at: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat() if v else None}


# =====================================================
# PHASE 2 MODELS - USER & JOB MANAGEMENT
# =====================================================


class Recruiter(BaseModel):
    """Model for recruiters/HR users"""

    id: Optional[UUID] = None
    email: str
    password_hash: Optional[str] = Field(
        None, exclude=True
    )  # Exclude from API responses
    full_name: str
    company: Optional[str] = None
    role: Optional[str] = None
    is_active: bool = True
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat() if v else None}


class JobType(str, Enum):
    """Enum for job types"""

    FULL_TIME = "full-time"
    PART_TIME = "part-time"
    CONTRACT = "contract"
    INTERNSHIP = "internship"


class Job(BaseModel):
    """Model for job postings"""

    id: Optional[UUID] = None
    title: str
    description: str
    requirements: Optional[str] = None
    company: Optional[str] = None
    location: Optional[str] = None
    job_type: Optional[JobType] = None
    experience_level: Optional[JobLevel] = None
    salary_range: Optional[str] = None

    template_id: Optional[UUID] = None
    custom_weights: Optional[Dict[str, Any]] = Field(
        None, description="One-off custom weights (overrides template)"
    )
    recruiter_id: Optional[UUID] = None

    # Assessment configuration
    assessment_length: Optional[int] = Field(
        None, ge=1, le=50, description="Override template's assessment length"
    )

    is_active: bool = True
    applications_count: int = 0

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None

    # Relationships (populated when needed)
    template: Optional[ScoringTemplate] = None
    recruiter: Optional[Recruiter] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat() if v else None}


# =====================================================
# PHASE 2 MODELS - ASSESSMENT & SCORING
# =====================================================


class CandidateScore(BaseModel):
    """Model for combined AI and human scores"""

    id: Optional[UUID] = None
    cv_assessment_id: int  # References cv_assessments(id) which is BIGSERIAL
    job_id: Optional[UUID] = None
    template_id: Optional[UUID] = None

    # Component scores (0-100)
    cv_analysis_score: Optional[Decimal] = None
    questionnaire_score: Optional[Decimal] = None
    case_study_score: Optional[Decimal] = None
    psychometric_score: Optional[Decimal] = None
    background_check_score: Optional[Decimal] = None

    # Tag-based scores per category
    tag_scores: Dict[str, float] = Field(default_factory=dict)

    # Combined scores
    ai_total_score: Optional[Decimal] = None
    human_total_score: Optional[Decimal] = None
    final_weighted_score: Optional[Decimal] = None

    # Score breakdown and explanations
    score_breakdown: Optional[Dict[str, Any]] = None
    ai_feedback: Optional[str] = None

    calculated_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


class BackgroundCheckStatus(str, Enum):
    """Enum for background check status"""

    PENDING = "pending"
    PASSED = "passed"
    FAILED = "failed"
    FLAGGED = "flagged"


class RecommendationType(str, Enum):
    """Enum for recommendation types"""

    STRONGLY_RECOMMEND = "strongly_recommend"
    RECOMMEND = "recommend"
    NEUTRAL = "neutral"
    NOT_RECOMMEND = "not_recommend"


class HumanAssessment(BaseModel):
    """Model for human assessments and overrides"""

    id: Optional[UUID] = None
    cv_assessment_id: int  # References cv_assessments(id) which is BIGSERIAL
    recruiter_id: Optional[UUID] = None

    # Manual scores
    technical_score: Optional[Decimal] = Field(None, ge=0, le=100)
    soft_skills_score: Optional[Decimal] = Field(None, ge=0, le=100)
    cultural_fit_score: Optional[Decimal] = Field(None, ge=0, le=100)
    overall_score: Optional[Decimal] = Field(None, ge=0, le=100)

    # Case study evaluation
    case_study_score: Optional[Decimal] = Field(None, ge=0, le=100)
    case_study_feedback: Optional[str] = None

    # Background check
    background_check_status: Optional[BackgroundCheckStatus] = None
    background_check_notes: Optional[str] = None

    # General feedback
    strengths: List[str] = Field(default_factory=list)
    weaknesses: List[str] = Field(default_factory=list)
    recommendation: Optional[RecommendationType] = None
    notes: Optional[str] = None

    assessed_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
        }


# =====================================================
# PHASE 2 MODELS - EXTENDED CV ASSESSMENT
# =====================================================


class CVAssessmentUpdate(BaseModel):
    """Model for updating cv_assessments table with Phase 2 fields"""

    template_id: Optional[UUID] = None
    job_id: Optional[UUID] = None
    tags: Optional[Dict[str, Any]] = Field(default_factory=dict)
    experience_level: Optional[JobLevel] = None
    tag_extraction_complete: bool = False


class CVAssessment(BaseModel):
    """Complete CV Assessment model including Phase 2 fields"""

    # Existing fields
    id: Optional[int] = None
    session_id: str
    name: Optional[str] = None
    email: Optional[str] = None
    pdf_blob: Optional[str] = None  # Base64 encoded PDF
    pdf_text: Optional[str] = None
    assessment_state: Optional[str] = None  # JSON string of QA history
    is_complete: bool = False
    score: Optional[int] = Field(None, ge=0, le=100)
    assessment_description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # Phase 2 fields
    template_id: Optional[UUID] = None
    job_id: Optional[UUID] = None
    tags: Optional[Dict[str, Any]] = Field(default_factory=dict)
    experience_level: Optional[JobLevel] = None
    tag_extraction_complete: bool = False

    # Relationships (populated when needed)
    template: Optional[ScoringTemplate] = None
    job: Optional[Job] = None
    candidate_tags: Optional[List[CandidateTag]] = Field(default_factory=list)
    tag_coverage: Optional[List[CandidateTagCoverage]] = Field(default_factory=list)
    candidate_score: Optional[CandidateScore] = None
    human_assessment: Optional[HumanAssessment] = None

    @validator("assessment_state", pre=True)
    def validate_assessment_state(cls, v):
        """Ensure assessment_state is valid JSON"""
        if v and isinstance(v, str):
            try:
                json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("assessment_state must be valid JSON")
        return v

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None,
        }
