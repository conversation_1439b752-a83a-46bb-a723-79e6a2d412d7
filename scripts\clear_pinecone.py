#!/usr/bin/env python
"""
Pinecone Index Management Script

This script provides functionality to manage Pinecone indexes used by the RAG system:
1. Show statistics about an index
2. Clear all vectors from an index without deleting the index itself
3. List all vectors in an index with their content and metadata

Usage:
    python clear_pinecone.py --stats               # Show statistics about the index
    python clear_pinecone.py --clear --confirm     # Clear all vectors without deleting the index
    python clear_pinecone.py --list-vectors        # List all vectors in the index
    python clear_pinecone.py --list-vectors --limit 10  # List only 10 vectors
"""

# =============================================================================
# IMPORTS AND SETUP
# =============================================================================

import os
import argparse
from dotenv import load_dotenv
import pinecone
import time
import pinecone.exceptions

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Load environment variables
load_dotenv()

# Get API keys from environment
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")

# Get the host domain from environment variables (required)
HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
if not HOST_DOMAIN:
    raise ValueError("HOST_DOMAIN environment variable must be set")

# =============================================================================
# PINECONE MANAGEMENT FUNCTIONS
# =============================================================================


def clear_index(index_name, confirm=False):
    """
    Clear all vectors from a Pinecone index without deleting the index itself.

    Args:
        index_name: Name of the index to clear
        confirm: Whether the user has confirmed the clearing
    """
    if not confirm:
        print(
            "WARNING: This will delete ALL vectors in the index but keep the index structure."
        )
        print("To confirm, run the script with --clear --confirm flags.")
        return False

    # Initialize Pinecone
    pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)

    # Check if index exists
    indexes = pc.list_indexes()
    index_names = [index.name for index in indexes]

    if index_name not in index_names:
        print(f"Error: Index '{index_name}' does not exist.")
        return False

    # Get the index
    index = pc.Index(index_name)

    try:
        stats = index.describe_index_stats()

        if stats.get("total_vector_count", 0) == 0:
            print(f"Index '{index_name}' is already empty. No action needed.")
            return True

        actual_namespaces_from_stats = stats.get("namespaces", {})

        if not actual_namespaces_from_stats:
            # No specific namespaces listed by stats.
            # If total_vector_count > 0 (checked above), all vectors are in the default namespace.
            print(
                f"No specific namespaces reported by stats for index '{index_name}'. Attempting to clear default namespace..."
            )
            try:
                index.delete(
                    delete_all=True, namespace=""
                )  # Explicitly target default namespace ""
                print(
                    f"Successfully initiated clearing of default namespace for index '{index_name}'."
                )
            except pinecone.exceptions.NotFoundException:
                # This is the key fix: if default namespace is "not found" during delete, it's effectively empty.
                print(
                    f"Default namespace for index '{index_name}' reported as not found or already empty. Considered cleared."
                )
            # Other exceptions will propagate to the outer catch block
        else:
            # Iterate through all namespaces reported by stats and clear them.
            # The key in actual_namespaces_from_stats can be "" for the default namespace.
            for ns_name_key in actual_namespaces_from_stats.keys():
                ns_display = (
                    f"namespace '{ns_name_key}'" if ns_name_key else "default namespace"
                )
                print(
                    f"Attempting to clear vectors from {ns_display} in index '{index_name}'..."
                )
                try:
                    index.delete(
                        delete_all=True, namespace=ns_name_key
                    )  # Pass the key (which can be "")
                    print(
                        f"Successfully initiated clearing for {ns_display} in index '{index_name}'."
                    )
                except pinecone.exceptions.NotFoundException:
                    print(
                        f"{ns_display} in index '{index_name}' not found or already empty. Considered cleared."
                    )
                # Let other exceptions propagate if they are not NotFoundException for that namespace

        # Add a small delay and final check for overall clearance
        print(f"Waiting a moment for deletions in '{index_name}' to reflect...")
        time.sleep(3)

        final_stats = index.describe_index_stats()
        if final_stats.get("total_vector_count", 0) == 0:
            print(f"Successfully cleared all vectors from index '{index_name}'.")
        else:
            print(
                f"Warning: Index '{index_name}' may not be fully cleared after operations. Vectors remaining: {final_stats.get('total_vector_count', 0)}. Deletion is an asynchronous process in Pinecone, final state might take longer to reflect or an issue occurred."
            )

        print(
            f"Pinecone index '{index_name}' clearing process complete. You can now run the embedding script."
        )
        return True

    except pinecone.exceptions.PineconeException as pe:
        print(
            f"A Pinecone error occurred during vector clearing for index '{index_name}': {str(pe)}"
        )
        return False
    except Exception as e:
        print(
            f"An unexpected error occurred during vector clearing for index '{index_name}': {str(e)}"
        )
        return False


def show_stats(index_name):
    """
    Show statistics about a Pinecone index.

    Args:
        index_name: Name of the index to show stats for
    """
    # Initialize Pinecone
    pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)

    # Check if index exists
    indexes = pc.list_indexes()
    index_names = [index.name for index in indexes]

    if index_name not in index_names:
        print(f"Error: Index '{index_name}' does not exist.")
        return False

    # Get the index
    index = pc.Index(index_name)

    # Get stats
    stats = index.describe_index_stats()
    print(f"\nStatistics for index '{index_name}':")
    print(f"Total vector count: {stats.get('total_vector_count', 0)}")
    print("Namespaces:")
    for namespace, ns_stats in stats.get("namespaces", {}).items():
        print(f"  - {namespace}: {ns_stats.get('vector_count', 0)} vectors")

    return True


def list_vectors(index_name, limit=None):
    """
    List all vectors in a Pinecone index with their content and metadata.

    Args:
        index_name: Name of the index to list vectors from
        limit: Maximum number of vectors to list (None for all)
    """
    # Initialize Pinecone
    pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)

    # Check if index exists
    indexes = pc.list_indexes()
    index_names = [index.name for index in indexes]

    if index_name not in index_names:
        print(f"Error: Index '{index_name}' does not exist.")
        return False

    # Get the index
    index = pc.Index(index_name)

    # Get stats to find namespaces and total count
    stats = index.describe_index_stats()
    total_vectors = stats.get("total_vector_count", 0)

    if total_vectors == 0:
        print(f"Index '{index_name}' is empty. No vectors to list.")
        return True

    # Set a reasonable default limit if none provided
    if limit is None:
        # Default to 20 vectors if not specified
        limit = 20
        print(f"Limiting output to {limit} vectors. Use --limit to change this.")

    # Get namespaces
    namespaces = list(stats.get("namespaces", {}).keys())
    if not namespaces:
        namespaces = [""]  # Default namespace

    print(f"\nListing vectors from index '{index_name}':")

    # Track how many vectors we've listed
    listed_count = 0

    # Query vectors from each namespace
    for namespace in namespaces:
        ns_display = namespace if namespace else "default namespace"
        print(f"\nNamespace: {ns_display}")

        # Query vectors from this namespace
        try:
            # Use fetch to get vectors with their metadata
            # We need to get IDs first
            query_response = index.query(
                namespace=namespace,
                vector=[0.0] * 1536,  # Dummy vector for querying
                top_k=limit,
                include_metadata=True,
            )

            # If we got matches, fetch them
            if query_response.matches:
                vector_ids = [match.id for match in query_response.matches]
                fetch_response = index.fetch(ids=vector_ids, namespace=namespace)

                # Display each vector
                for i, (vector_id, vector_data) in enumerate(
                    fetch_response.vectors.items(), 1
                ):
                    metadata = vector_data.metadata

                    # Print vector information
                    print(f"\nVector {i}:")
                    print(f"  ID: {vector_id}")

                    # Print metadata
                    if metadata:
                        print("  Metadata:")
                        # Print source and title if available
                        if "source" in metadata:
                            print(f"    Source: {metadata['source']}")
                        if "title" in metadata:
                            print(f"    Title: {metadata['title']}")

                        # Print text content (truncated if too long)
                        if "text" in metadata:
                            text = metadata["text"]
                            if len(text) > 300:
                                text = text[:297] + "..."
                            print(f"    Text: {text}")

                    listed_count += 1

                    # Check if we've reached the limit
                    if limit is not None and listed_count >= limit:
                        break
            else:
                print("  No vectors found in this namespace.")

            # If we've reached the limit, stop querying more namespaces
            if limit is not None and listed_count >= limit:
                break

        except Exception as e:
            print(f"  Error listing vectors in namespace '{ns_display}': {str(e)}")

    print(f"\nListed {listed_count} vectors out of {total_vectors} total.")
    if listed_count < total_vectors:
        print(f"Use --limit with a higher number to see more vectors.")

    return True


# =============================================================================
# MAIN EXECUTION
# =============================================================================


def main():
    """Main function to run the index management process."""
    parser = argparse.ArgumentParser(
        description="Manage Pinecone indexes for the RAG system"
    )
    parser.add_argument(
        "--confirm", action="store_true", help="Confirm clearing operation"
    )
    parser.add_argument(
        "--clear",
        action="store_true",
        help="Clear all vectors from the index without deleting it",
    )
    parser.add_argument(
        "--index",
        type=str,
        default=PINECONE_INDEX_NAME,
        help=f"Name of the index to manage (default: {PINECONE_INDEX_NAME})",
    )
    parser.add_argument(
        "--stats", action="store_true", help="Show statistics about the index"
    )
    parser.add_argument(
        "--list-vectors",
        action="store_true",
        help="List vectors in the index with their content",
    )
    parser.add_argument(
        "--limit", type=int, help="Limit the number of vectors to list (default: 20)"
    )

    args = parser.parse_args()

    if not PINECONE_API_KEY:
        print("Error: PINECONE_API_KEY environment variable is not set.")
        print("Please set it in your .env file or environment variables.")
        return

    if args.stats:
        show_stats(args.index)
        return

    if args.list_vectors:
        list_vectors(args.index, args.limit)
        return

    if args.clear:
        clear_index(args.index, args.confirm)
    else:
        # If no specific action is specified, show help
        parser.print_help()


if __name__ == "__main__":
    main()
