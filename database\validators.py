"""
Phase 2 Data Validation Module

This module provides validation functions for Phase 2 data integrity
and business rule enforcement.
"""

from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime
import re
import logging

from database.models import (
    ScoringTemplate,
    TagCategory,
    TagDefinition,
    CandidateTag,
    JobLevel,
    TagDataType,
    TagSource,
)

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom validation error"""

    pass


# =====================================================
# SCORING TEMPLATE VALIDATORS
# =====================================================


def validate_scoring_weights(template: ScoringTemplate) -> bool:
    """
    Validate that scoring template weights sum to 1.0

    Args:
        template: ScoringTemplate instance

    Returns:
        True if valid

    Raises:
        ValidationError if weights don't sum to 1.0
    """
    component_sum = (
        template.cv_analysis_weight
        + template.questionnaire_weight
        + template.case_study_weight
        + template.psychometric_weight
        + template.background_check_weight
    )

    if abs(component_sum - Decimal("1.0")) > Decimal("0.001"):
        raise ValidationError(f"Component weights must sum to 1.0, got {component_sum}")

    ai_human_sum = template.ai_percentage + template.human_percentage

    if abs(ai_human_sum - Decimal("1.0")) > Decimal("0.001"):
        raise ValidationError(
            f"AI and human percentages must sum to 1.0, got {ai_human_sum}"
        )

    return True


def validate_category_weights(category_weights: Dict[str, float]) -> bool:
    """
    Validate category weights (should sum to 1.0 if provided)

    Args:
        category_weights: Dictionary of category_id to weight

    Returns:
        True if valid or empty

    Raises:
        ValidationError if weights don't sum to 1.0
    """
    if not category_weights:
        return True

    total_weight = sum(category_weights.values())

    if abs(total_weight - 1.0) > 0.001:
        raise ValidationError(f"Category weights must sum to 1.0, got {total_weight}")

    return True


# =====================================================
# TAG VALIDATORS
# =====================================================


def validate_tag_value(value: str, tag_definition: TagDefinition) -> bool:
    """
    Validate a tag value against its definition

    Args:
        value: The tag value to validate
        tag_definition: The tag definition with validation rules

    Returns:
        True if valid

    Raises:
        ValidationError if value doesn't meet validation rules
    """
    if not value and tag_definition.is_required:
        raise ValidationError(f"Tag '{tag_definition.tag_key}' is required")

    # Type-specific validation
    if tag_definition.data_type == TagDataType.NUMBER:
        try:
            num_value = float(value)

            # Check min/max if specified
            if tag_definition.validation_rules:
                min_val = tag_definition.validation_rules.get("min")
                max_val = tag_definition.validation_rules.get("max")

                if min_val is not None and num_value < min_val:
                    raise ValidationError(
                        f"Value {num_value} is below minimum {min_val}"
                    )
                if max_val is not None and num_value > max_val:
                    raise ValidationError(
                        f"Value {num_value} is above maximum {max_val}"
                    )

        except ValueError:
            raise ValidationError(f"Value '{value}' is not a valid number")

    elif tag_definition.data_type == TagDataType.DATE:
        # Validate date format
        date_formats = ["%Y-%m-%d", "%Y/%m/%d", "%d-%m-%Y", "%d/%m/%Y", "%Y"]
        valid_date = False

        for fmt in date_formats:
            try:
                datetime.strptime(value, fmt)
                valid_date = True
                break
            except ValueError:
                continue

        if not valid_date:
            raise ValidationError(f"Value '{value}' is not a valid date")

    elif tag_definition.data_type == TagDataType.BOOLEAN:
        if value.lower() not in ["true", "false", "yes", "no", "1", "0"]:
            raise ValidationError(f"Value '{value}' is not a valid boolean")

    # Check regex pattern if specified
    if tag_definition.validation_rules and "pattern" in tag_definition.validation_rules:
        pattern = tag_definition.validation_rules["pattern"]
        if not re.match(pattern, value):
            raise ValidationError(
                f"Value '{value}' doesn't match required pattern: {pattern}"
            )

    # Check allowed values if specified
    if (
        tag_definition.validation_rules
        and "allowed_values" in tag_definition.validation_rules
    ):
        allowed = tag_definition.validation_rules["allowed_values"]
        if value not in allowed:
            raise ValidationError(f"Value '{value}' not in allowed values: {allowed}")

    return True


def validate_tag_coverage(
    coverage_percentage: float, threshold_percentage: float, is_mandatory: bool = True
) -> bool:
    """
    Validate tag coverage against threshold

    Args:
        coverage_percentage: Current coverage percentage
        threshold_percentage: Required threshold
        is_mandatory: Whether this coverage is mandatory

    Returns:
        True if coverage meets threshold or not mandatory

    Raises:
        ValidationError if mandatory coverage not met
    """
    if is_mandatory and coverage_percentage < threshold_percentage:
        raise ValidationError(
            f"Coverage {coverage_percentage}% is below required threshold {threshold_percentage}%"
        )

    return True


# =====================================================
# EXPERIENCE LEVEL VALIDATORS
# =====================================================


def detect_experience_level(
    cv_text: str, total_years: Optional[int] = None
) -> JobLevel:
    """
    Detect experience level from CV text and years of experience

    Args:
        cv_text: The CV text content
        total_years: Extracted total years of experience

    Returns:
        Detected JobLevel
    """
    cv_lower = cv_text.lower()

    # Keywords for different levels
    executive_keywords = [
        "ceo",
        "cto",
        "cfo",
        "coo",
        "chief",
        "president",
        "vp",
        "vice president",
        "director",
        "head of",
        "executive",
    ]

    senior_keywords = [
        "senior",
        "lead",
        "principal",
        "architect",
        "manager",
        "team lead",
        "technical lead",
        "10+ years",
        "15+ years",
    ]

    mid_keywords = [
        "5+ years",
        "7+ years",
        "experienced",
        "specialist",
        "analyst",
        "developer",
        "engineer",
    ]

    junior_keywords = [
        "junior",
        "associate",
        "entry level",
        "1-3 years",
        "graduate",
        "trainee",
    ]

    entry_keywords = [
        "fresh graduate",
        "intern",
        "no experience",
        "student",
        "0-1 year",
        "beginner",
    ]

    # Check keywords
    if any(keyword in cv_lower for keyword in executive_keywords):
        return JobLevel.EXECUTIVE

    # Use years of experience if available
    if total_years is not None:
        if total_years >= 15:
            return JobLevel.SENIOR
        elif total_years >= 10:
            return JobLevel.SENIOR
        elif total_years >= 5:
            return JobLevel.MID
        elif total_years >= 2:
            return JobLevel.JUNIOR
        else:
            return JobLevel.ENTRY

    # Fall back to keyword detection
    if any(keyword in cv_lower for keyword in senior_keywords):
        return JobLevel.SENIOR
    elif any(keyword in cv_lower for keyword in mid_keywords):
        return JobLevel.MID
    elif any(keyword in cv_lower for keyword in junior_keywords):
        return JobLevel.JUNIOR
    elif any(keyword in cv_lower for keyword in entry_keywords):
        return JobLevel.ENTRY

    # Default to mid-level if unclear
    return JobLevel.MID


# =====================================================
# EMAIL & PHONE VALIDATORS
# =====================================================


def validate_email(email: str) -> bool:
    """
    Validate email format

    Args:
        email: Email address to validate

    Returns:
        True if valid

    Raises:
        ValidationError if invalid format
    """
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

    if not re.match(pattern, email):
        raise ValidationError(f"Invalid email format: {email}")

    return True


def validate_phone(phone: str) -> bool:
    """
    Validate phone number format

    Args:
        phone: Phone number to validate

    Returns:
        True if valid

    Raises:
        ValidationError if invalid format
    """
    # Remove common separators
    cleaned = re.sub(r"[\s\-\(\)\.]+", "", phone)

    # Check if it's a valid phone number (7-15 digits, optionally starting with +)
    pattern = r"^\+?\d{7,15}$"

    if not re.match(pattern, cleaned):
        raise ValidationError(f"Invalid phone number format: {phone}")

    return True


# =====================================================
# BUSINESS RULE VALIDATORS
# =====================================================


def validate_assessment_completion(
    tag_coverage: List[Dict[str, Any]], required_components: List[str]
) -> bool:
    """
    Validate if an assessment meets completion criteria

    Args:
        tag_coverage: List of coverage records
        required_components: List of required assessment components

    Returns:
        True if all requirements met

    Raises:
        ValidationError if requirements not met
    """
    # Check tag coverage
    incomplete_categories = []

    for coverage in tag_coverage:
        if not coverage.get("is_complete", False):
            category_name = coverage.get("category", {}).get("display_name", "Unknown")
            incomplete_categories.append(category_name)

    if incomplete_categories:
        raise ValidationError(
            f"Incomplete tag coverage for categories: {', '.join(incomplete_categories)}"
        )

    # Additional component checks can be added here

    return True


def validate_score_range(
    score: float, min_score: float = 0, max_score: float = 100
) -> bool:
    """
    Validate score is within acceptable range

    Args:
        score: The score to validate
        min_score: Minimum acceptable score
        max_score: Maximum acceptable score

    Returns:
        True if valid

    Raises:
        ValidationError if out of range
    """
    if score < min_score or score > max_score:
        raise ValidationError(
            f"Score {score} is out of range [{min_score}, {max_score}]"
        )

    return True


# =====================================================
# UTILITY FUNCTIONS
# =====================================================


def sanitize_user_input(input_text: str, max_length: int = 1000) -> str:
    """
    Sanitize user input to prevent injection attacks

    Args:
        input_text: Raw user input
        max_length: Maximum allowed length

    Returns:
        Sanitized input
    """
    if not input_text:
        return ""

    # Truncate to max length
    sanitized = input_text[:max_length]

    # Remove potentially dangerous characters
    sanitized = re.sub(r"[<>\"\'&]", "", sanitized)

    # Normalize whitespace
    sanitized = " ".join(sanitized.split())

    return sanitized


def validate_date_range(
    start_date: datetime, end_date: datetime, allow_future: bool = False
) -> bool:
    """
    Validate date range

    Args:
        start_date: Start date
        end_date: End date
        allow_future: Whether to allow future dates

    Returns:
        True if valid

    Raises:
        ValidationError if invalid range
    """
    if start_date > end_date:
        raise ValidationError("Start date cannot be after end date")

    if not allow_future:
        now = datetime.now()
        if start_date > now or end_date > now:
            raise ValidationError("Future dates are not allowed")

    return True
