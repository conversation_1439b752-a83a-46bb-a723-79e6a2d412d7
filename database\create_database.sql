-- Database setup for LeaderScout AI Chatbot
-- This script creates all necessary tables, indexes, and constraints
-- Including Phase 2: Hierarchical Tag System, Weighted Scoring, and User Management

-- =====================================================
-- EXISTING TABLES (Phase 1)
-- =====================================================

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    session_id UUID PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    host_name TEXT,
    language TEXT DEFAULT 'English' CHECK (language IN ('English', 'Arabic'))
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    session_id UUID,
    role TEXT NOT NULL CHECK (role IN ('system', 'user', 'assistant')),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Foreign key constraint with cascade delete
    CONSTRAINT fk_messages_conversation FOREIGN KEY (session_id) REFERENCES conversations(session_id) ON DELETE CASCADE
);

-- Create index for faster queries on messages
CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);

-- Create composite index for faster ordering of messages by session and timestamp
CREATE INDEX IF NOT EXISTS idx_messages_session_created_at ON messages(session_id, created_at);

-- Create leads table with proper constraints
CREATE TABLE IF NOT EXISTS leads (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    session_id UUID,
    CONSTRAINT fk_leads_conversation FOREIGN KEY (session_id) REFERENCES conversations(session_id) ON DELETE NO ACTION,
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    lead_type TEXT CHECK (lead_type IN ('company', 'recruitment_partner')),
    company TEXT,
    industry TEXT,
    num_locations INTEGER,
    num_employees INTEGER,
    additional_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    consent_given BOOLEAN DEFAULT FALSE,

    -- Constraint to ensure either email, phone, or session_id is provided
    CONSTRAINT email_or_phone_or_session_required CHECK (
        email IS NOT NULL OR phone IS NOT NULL OR session_id IS NOT NULL
    )
);

-- Create index for faster queries on leads
CREATE INDEX IF NOT EXISTS idx_leads_session_id ON leads(session_id);

-- Create unique indexes for email and phone that properly handle NULL values
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_email ON leads (email) WHERE email IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_phone ON leads (phone) WHERE phone IS NOT NULL;

-- Table to store CV assessments and results
CREATE TABLE IF NOT EXISTS cv_assessments (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID NOT NULL,
    name TEXT,
    email TEXT,
    pdf_blob TEXT NOT NULL, -- base64-encoded PDF data
    pdf_text TEXT NOT NULL,
    score FLOAT,
    assessment_description TEXT,
    assessment_state TEXT, -- JSON string of Q&A history for multi-turn assessment
    is_complete BOOLEAN DEFAULT FALSE, -- Whether the assessment is finished
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Phase 2 additions
    template_id UUID,
    job_id UUID,
    tags JSONB DEFAULT '{}',
    experience_level VARCHAR(50),
    tag_extraction_complete BOOLEAN DEFAULT false,
    
    CONSTRAINT fk_cv_assessments_conversation FOREIGN KEY (session_id) REFERENCES conversations(session_id) ON DELETE CASCADE
);

-- =====================================================
-- PHASE 2 TABLES - HIERARCHICAL TAG SYSTEM
-- =====================================================

-- Main tag categories (Education, Experience, Skills, etc.)
CREATE TABLE IF NOT EXISTS tag_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    default_threshold DECIMAL(3,2) DEFAULT 0.70 CHECK (default_threshold >= 0 AND default_threshold <= 1),
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Subtag definitions within each category
CREATE TABLE IF NOT EXISTS tag_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID NOT NULL REFERENCES tag_categories(id) ON DELETE CASCADE,
    tag_key VARCHAR(100) NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    data_type VARCHAR(50) NOT NULL DEFAULT 'text',
    validation_rules JSONB,
    extraction_hints TEXT[],
    question_templates TEXT[],
    is_required BOOLEAN DEFAULT false,
    weight DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category_id, tag_key)
);

-- Candidate tags extracted from CVs and assessments
CREATE TABLE IF NOT EXISTS candidate_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cv_assessment_id BIGINT NOT NULL REFERENCES cv_assessments(id) ON DELETE CASCADE,
    tag_definition_id UUID NOT NULL REFERENCES tag_definitions(id) ON DELETE CASCADE,
    value TEXT NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 1.0 CHECK (confidence_score >= 0 AND confidence_score <= 1),
    source VARCHAR(50) NOT NULL,
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(cv_assessment_id, tag_definition_id)
);

-- Track coverage per category for each assessment
CREATE TABLE IF NOT EXISTS candidate_tag_coverage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cv_assessment_id BIGINT NOT NULL REFERENCES cv_assessments(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES tag_categories(id) ON DELETE CASCADE,
    total_tags INTEGER NOT NULL DEFAULT 0,
    filled_tags INTEGER NOT NULL DEFAULT 0,
    coverage_percentage DECIMAL(5,2) DEFAULT 0.00,
    threshold_percentage DECIMAL(5,2) DEFAULT 70.00,
    is_complete BOOLEAN DEFAULT false,
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(cv_assessment_id, category_id)
);

-- Configurable coverage thresholds per job/template
CREATE TABLE IF NOT EXISTS coverage_thresholds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID,
    template_id UUID,
    category_id UUID NOT NULL REFERENCES tag_categories(id) ON DELETE CASCADE,
    required_percentage DECIMAL(5,2) NOT NULL CHECK (required_percentage >= 0 AND required_percentage <= 100),
    is_mandatory BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create a unique index to handle NULL values properly
CREATE UNIQUE INDEX IF NOT EXISTS idx_coverage_thresholds_unique 
ON coverage_thresholds (
    COALESCE(job_id, '00000000-0000-0000-0000-000000000000'::uuid), 
    COALESCE(template_id, '00000000-0000-0000-0000-000000000000'::uuid), 
    category_id
);

-- =====================================================
-- PHASE 2 TABLES - SCORING TEMPLATES
-- =====================================================

-- Comprehensive scoring templates for recruiter reuse
CREATE TABLE IF NOT EXISTS scoring_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    industry VARCHAR(100),
    job_level VARCHAR(50),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Component weights (must sum to 1.0)
    cv_analysis_weight DECIMAL(3,2) DEFAULT 0.30 CHECK (cv_analysis_weight >= 0 AND cv_analysis_weight <= 1),
    questionnaire_weight DECIMAL(3,2) DEFAULT 0.30 CHECK (questionnaire_weight >= 0 AND questionnaire_weight <= 1),
    case_study_weight DECIMAL(3,2) DEFAULT 0.20 CHECK (case_study_weight >= 0 AND case_study_weight <= 1),
    psychometric_weight DECIMAL(3,2) DEFAULT 0.10 CHECK (psychometric_weight >= 0 AND psychometric_weight <= 1),
    background_check_weight DECIMAL(3,2) DEFAULT 0.10 CHECK (background_check_weight >= 0 AND background_check_weight <= 1),
    
    -- AI/Human ratio
    ai_percentage DECIMAL(3,2) DEFAULT 0.70 CHECK (ai_percentage >= 0 AND ai_percentage <= 1),
    human_percentage DECIMAL(3,2) DEFAULT 0.30 CHECK (human_percentage >= 0 AND human_percentage <= 1),
    
    -- Tag category weights (category_id as key, weight as value)
    category_weights JSONB DEFAULT '{}',
    
    created_by UUID REFERENCES recruiters(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure component weights sum to 1.0 (allowing small floating point variance)
    CONSTRAINT check_component_weights_sum CHECK (
        ABS((cv_analysis_weight + questionnaire_weight + case_study_weight + psychometric_weight + background_check_weight) - 1.0) < 0.001
    ),
    
    -- Ensure AI/Human percentages sum to 1.0
    CONSTRAINT check_ai_human_ratio_sum CHECK (
        ABS((ai_percentage + human_percentage) - 1.0) < 0.001
    )
);


-- =====================================================
-- PHASE 2 TABLES - USER MANAGEMENT
-- =====================================================

-- Recruiters/HR users
CREATE TABLE IF NOT EXISTS recruiters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(200) NOT NULL,
    company VARCHAR(200),
    role VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- PHASE 2 TABLES - JOB MANAGEMENT
-- =====================================================

-- Job postings
CREATE TABLE IF NOT EXISTS jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(300) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    company VARCHAR(200),
    location VARCHAR(200),
    job_type VARCHAR(50),
    experience_level VARCHAR(50),
    salary_range VARCHAR(100),
    
    template_id UUID REFERENCES scoring_templates(id),  -- Use saved template
    custom_weights JSONB,  -- One-off custom weights (overrides template)
    recruiter_id UUID REFERENCES recruiters(id),
    
    -- Assessment configuration
    assessment_length INTEGER DEFAULT 15 CHECK (assessment_length >= 1 AND assessment_length <= 50),
    
    is_active BOOLEAN DEFAULT true,
    applications_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- PHASE 2 TABLES - ASSESSMENT & SCORING
-- =====================================================

-- Combined AI and human scores
CREATE TABLE IF NOT EXISTS candidate_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cv_assessment_id BIGINT NOT NULL REFERENCES cv_assessments(id) ON DELETE CASCADE,
    job_id UUID REFERENCES jobs(id),
    template_id UUID REFERENCES scoring_templates(id),
    
    -- Component scores (0-100)
    cv_analysis_score DECIMAL(5,2),
    questionnaire_score DECIMAL(5,2),
    case_study_score DECIMAL(5,2),
    psychometric_score DECIMAL(5,2),
    background_check_score DECIMAL(5,2),
    
    -- Tag-based scores per category
    tag_scores JSONB DEFAULT '{}',
    
    -- Combined scores
    ai_total_score DECIMAL(5,2),
    human_total_score DECIMAL(5,2),
    final_weighted_score DECIMAL(5,2),
    
    -- Score breakdown and explanations
    score_breakdown JSONB,
    ai_feedback TEXT,
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Human assessments and overrides
CREATE TABLE IF NOT EXISTS human_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cv_assessment_id BIGINT NOT NULL REFERENCES cv_assessments(id) ON DELETE CASCADE,
    recruiter_id UUID REFERENCES recruiters(id),
    
    -- Manual scores
    technical_score DECIMAL(5,2),
    soft_skills_score DECIMAL(5,2),
    cultural_fit_score DECIMAL(5,2),
    overall_score DECIMAL(5,2),
    
    -- Case study evaluation
    case_study_score DECIMAL(5,2),
    case_study_feedback TEXT,
    
    -- Background check
    background_check_status VARCHAR(50),
    background_check_notes TEXT,
    
    -- General feedback
    strengths TEXT[],
    weaknesses TEXT[],
    recommendation VARCHAR(50),
    notes TEXT,
    
    assessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Phase 1 indexes
CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);
CREATE INDEX IF NOT EXISTS idx_messages_session_created_at ON messages(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_leads_session_id ON leads(session_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_email ON leads (email) WHERE email IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_phone ON leads (phone) WHERE phone IS NOT NULL;

-- Phase 2 indexes
CREATE INDEX IF NOT EXISTS idx_candidate_tags_assessment ON candidate_tags(cv_assessment_id);
CREATE INDEX IF NOT EXISTS idx_candidate_tags_definition ON candidate_tags(tag_definition_id);
CREATE INDEX IF NOT EXISTS idx_tag_definitions_category ON tag_definitions(category_id);
CREATE INDEX IF NOT EXISTS idx_candidate_coverage_assessment ON candidate_tag_coverage(cv_assessment_id);
CREATE INDEX IF NOT EXISTS idx_jobs_recruiter ON jobs(recruiter_id);
CREATE INDEX IF NOT EXISTS idx_jobs_template ON jobs(template_id);
CREATE INDEX IF NOT EXISTS idx_candidate_scores_assessment ON candidate_scores(cv_assessment_id);
CREATE INDEX IF NOT EXISTS idx_human_assessments_assessment ON human_assessments(cv_assessment_id);
CREATE INDEX IF NOT EXISTS idx_human_assessments_recruiter ON human_assessments(recruiter_id);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Create trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at column
CREATE TRIGGER update_tag_categories_updated_at BEFORE UPDATE ON tag_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_tag_definitions_updated_at BEFORE UPDATE ON tag_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_candidate_tags_updated_at BEFORE UPDATE ON candidate_tags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_scoring_templates_updated_at BEFORE UPDATE ON scoring_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_recruiters_updated_at BEFORE UPDATE ON recruiters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_jobs_updated_at BEFORE UPDATE ON jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_candidate_scores_updated_at BEFORE UPDATE ON candidate_scores
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
CREATE TRIGGER update_human_assessments_updated_at BEFORE UPDATE ON human_assessments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
