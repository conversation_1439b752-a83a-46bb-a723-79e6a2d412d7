"""Agent Selection Tool for Coordinator Agent

This tool is used by the coordinator to select which specialized agent
should handle a given request and execute the request through that agent.
"""

from typing import Dict, Any
from langchain_core.tools import StructuredTool
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger("agent_selection_tool")


class AgentSelectionInput(BaseModel):
    """Input schema for agent selection tool."""

    agent_type: str = Field(
        description="The type of agent to use. Must be either 'cv_assessment' or 'conversation'."
    )
    reason: str = Field(description="Brief explanation of why this agent was selected.")


def create_tool(coordinator_agent):
    """Create the agent selection tool bound to the coordinator agent instance."""

    def select_and_execute_agent(agent_type: str, reason: str) -> str:
        """
        Select the appropriate agent and execute the current request.

        Args:
            agent_type: Either 'cv_assessment' or 'conversation'
            reason: Explanation for the selection

        Returns:
            The response from the selected agent
        """
        logger.info(f"Routing to {agent_type} agent. Reason: {reason}")

        # Get the original message from the coordinator's context
        # The message is stored in the agent's memory or passed through
        if hasattr(coordinator_agent, "_current_message"):
            original_message = coordinator_agent._current_message
        elif coordinator_agent.memory and coordinator_agent.memory.chat_memory.messages:
            original_message = coordinator_agent.memory.chat_memory.messages[-1].content
        else:
            original_message = ""

        try:
            if agent_type == "cv_assessment":
                # Check if there's CV text in the assessment table
                if coordinator_agent.supabase_client and coordinator_agent.session_id:
                    assessment_row = (
                        coordinator_agent.supabase_client.table("cv_assessments")
                        .select("*")
                        .eq("session_id", coordinator_agent.session_id)
                        .order("created_at", desc=True)
                        .limit(1)
                        .execute()
                    )

                    if assessment_row.data:
                        # There's an ongoing CV assessment
                        assessment = assessment_row.data[0]

                        # For now, use the conversation agent with CV tool
                        # This will be replaced when CV assessment agent is implemented
                        response = (
                            coordinator_agent.cv_assessment_agent.process_message(
                                original_message
                            )
                        )
                        return response
                    else:
                        return "Please upload your CV first so I can assess it."
                else:
                    # Direct CV assessment request
                    response = coordinator_agent.cv_assessment_agent.process_message(
                        original_message
                    )
                    return response

            elif agent_type == "conversation":
                # Route to conversation agent
                response = coordinator_agent.conversation_agent.process_message(
                    original_message
                )
                return response

            else:
                logger.error(f"Invalid agent type: {agent_type}")
                return "I apologize, but I encountered an error routing your request. Please try again."

        except Exception as e:
            logger.error(f"Error executing agent: {str(e)}", exc_info=True)
            # Fallback to conversation agent
            return coordinator_agent.conversation_agent.process_message(
                original_message
            )

    return StructuredTool.from_function(
        func=select_and_execute_agent,
        name="agent_selection",
        description=(
            "Select which specialized agent should handle the current request and execute it. "
            "Use 'cv_assessment' for CV/resume related requests, 'conversation' for everything else."
        ),
        args_schema=AgentSelectionInput,
    )
