<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Search Results - Testing Interface</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
      }
      .search-summary {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
      }
      .summary-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-top: 15px;
      }
      .stat-item {
        text-align: center;
      }
      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #007bff;
      }
      .stat-label {
        font-size: 14px;
        color: #666;
        margin-top: 5px;
      }
      .candidate-card {
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 20px;
        background: #fafafa;
        transition: all 0.3s ease;
      }
      .candidate-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      .candidate-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #eee;
      }
      .candidate-name {
        font-size: 22px;
        font-weight: 600;
        color: #333;
      }
      .candidate-match {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 16px;
      }
      .candidate-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
      }
      .detail-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }
      .detail-label {
        font-weight: 600;
        color: #666;
        font-size: 12px;
        text-transform: uppercase;
        margin-bottom: 5px;
      }
      .detail-value {
        color: #333;
        font-size: 16px;
        font-weight: 500;
      }
      .dual-scoring {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
      }
      .scoring-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        font-size: 16px;
      }
      .score-breakdown {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
      }
      .score-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        border: 2px solid #e9ecef;
        transition: all 0.2s ease;
      }
      .score-item:hover {
        border-color: #007bff;
      }
      .score-value {
        font-size: 20px;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 5px;
      }
      .score-label {
        font-size: 11px;
        color: #666;
        text-transform: uppercase;
        font-weight: 600;
      }
      .candidate-summary {
        background: #f8f9fa;
        border-left: 4px solid #28a745;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
      }
      .summary-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
      }
      .summary-text {
        color: #555;
        line-height: 1.6;
      }
      .candidate-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #eee;
      }
      .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s ease;
      }
      .btn-primary {
        background: #007bff;
        color: white;
      }
      .btn-primary:hover {
        background: #0056b3;
      }
      .btn-secondary {
        background: #6c757d;
        color: white;
      }
      .btn-secondary:hover {
        background: #545b62;
      }
      .nav-links {
        text-align: center;
        margin-bottom: 20px;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 500;
      }
      .nav-links a:hover {
        text-decoration: underline;
      }
      .no-results {
        text-align: center;
        padding: 50px;
        color: #666;
      }
      .no-results-icon {
        font-size: 64px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <div class="nav-links">
      <a href="/testing-dashboard">← Back to Dashboard</a>
      <a href="/candidate-search">New Search</a>
      <a href="/job-creation">Job Management</a>
    </div>

    <div class="container">
      <h1>🎯 Search Results with Dual Scoring</h1>

      <div class="search-summary">
        <h3>Search Summary</h3>
        <p><strong>Query:</strong> <span id="searchQuery">Loading...</span></p>
        <div class="summary-stats">
          <div class="stat-item">
            <div class="stat-value" id="totalResults">0</div>
            <div class="stat-label">Total Results</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="avgScore">0%</div>
            <div class="stat-label">Avg Match Score</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="searchTime">0ms</div>
            <div class="stat-label">Search Time</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="topScore">0%</div>
            <div class="stat-label">Best Match</div>
          </div>
        </div>
      </div>
    </div>

    <div id="resultsContainer">
      <!-- Results will be populated here -->
    </div>

    <div class="container no-results" id="noResults" style="display: none">
      <div class="no-results-icon">🔍</div>
      <h3>No Candidates Found</h3>
      <p>
        Try adjusting your search criteria or filters to find more candidates.
      </p>
      <a href="/candidate-search" class="btn btn-primary">Try New Search</a>
    </div>

    <script>
      // This template can be used to display search results
      // It expects results data to be passed in or loaded via JavaScript

      function displaySearchResults(data) {
        const resultsContainer = document.getElementById("resultsContainer");
        const noResults = document.getElementById("noResults");

        // Update search summary
        document.getElementById("searchQuery").textContent =
          data.query || "N/A";
        document.getElementById("totalResults").textContent =
          data.candidates?.length || 0;

        if (data.candidates && data.candidates.length > 0) {
          // Calculate statistics
          const scores = data.candidates.map((c) => c.similarity_score || 0);
          const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
          const topScore = Math.max(...scores);

          document.getElementById("avgScore").textContent =
            Math.round(avgScore * 100) + "%";
          document.getElementById("topScore").textContent =
            Math.round(topScore * 100) + "%";
          document.getElementById("searchTime").textContent =
            data.search_time_ms || "0ms";

          // Display candidates
          let html = "";
          data.candidates.forEach((candidate) => {
            html += createCandidateCard(candidate);
          });

          resultsContainer.innerHTML = html;
          noResults.style.display = "none";
        } else {
          resultsContainer.innerHTML = "";
          noResults.style.display = "block";
        }
      }

      function createCandidateCard(candidate) {
        const matchScore = Math.round((candidate.similarity_score || 0) * 100);

        return `
                <div class="container">
                    <div class="candidate-card">
                        <div class="candidate-header">
                            <div class="candidate-name">${
                              candidate.name || "Unknown Candidate"
                            }</div>
                            <div class="candidate-match">${matchScore}% Match</div>
                        </div>
                        
                        <div class="candidate-details">
                            <div class="detail-item">
                                <div class="detail-label">Experience</div>
                                <div class="detail-value">${
                                  candidate.experience_years || "N/A"
                                } years</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Location</div>
                                <div class="detail-value">${
                                  candidate.location || "N/A"
                                }</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Current Salary</div>
                                <div class="detail-value">${
                                  candidate.current_salary
                                    ? "$" +
                                      candidate.current_salary.toLocaleString()
                                    : "N/A"
                                }</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Expected Salary</div>
                                <div class="detail-value">${
                                  candidate.expected_salary
                                    ? "$" +
                                      candidate.expected_salary.toLocaleString()
                                    : "N/A"
                                }</div>
                            </div>
                        </div>
                        
                        ${
                          candidate.scores
                            ? `
                            <div class="dual-scoring">
                                <div class="scoring-title">📊 Dual Scoring Breakdown</div>
                                <div class="score-breakdown">
                                    <div class="score-item">
                                        <div class="score-value">${matchScore}%</div>
                                        <div class="score-label">Semantic Match</div>
                                    </div>
                                    ${Object.entries(candidate.scores)
                                      .map(
                                        ([key, value]) => `
                                        <div class="score-item">
                                            <div class="score-value">${Math.round(
                                              value * 100
                                            )}%</div>
                                            <div class="score-label">${key.replace(
                                              "_",
                                              " "
                                            )}</div>
                                        </div>
                                    `
                                      )
                                      .join("")}
                                </div>
                            </div>
                        `
                            : ""
                        }
                        
                        ${
                          candidate.summary
                            ? `
                            <div class="candidate-summary">
                                <div class="summary-title">Professional Summary</div>
                                <div class="summary-text">${candidate.summary}</div>
                            </div>
                        `
                            : ""
                        }
                        
                        <div class="candidate-actions">
                            <a href="/candidate-profile/${
                              candidate.id
                            }" class="btn btn-primary" target="_blank">
                                View Full Profile
                            </a>
                            <a href="/candidates/${
                              candidate.id
                            }/similar" class="btn btn-secondary" target="_blank">
                                Find Similar
                            </a>
                        </div>
                    </div>
                </div>
            `;
      }

      // Example usage - this would typically be called with actual search results
      // displaySearchResults(searchResultsData);
    </script>
  </body>
</html>
