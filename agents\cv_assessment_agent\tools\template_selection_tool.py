"""Template Selection Tool for CV Assessment Agent

template selection:
- Uses scoring_service.get_scoring_weights()
- No complex logic, just weight retrieval
"""

from langchain.tools import tool
import logging
from typing import Optional, Dict, Any
from uuid import UUID
from services.scoring_service import get_scoring_weights

logger = logging.getLogger("template_selection_tool")


def create_tool(agent):
    """Return a LangChain tool for template selection bound to the given agent instance."""

    @tool
    async def select_scoring_template(
        job_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get scoring weights for a CV assessment.

        logic:
        1. If job_id provided → get job's weights (custom or template)
        2. Otherwise → use default platform weights

        Args:
            job_id: Optional job ID to get specific weights for

        Returns:
            Dict containing:
                - weights: Dictionary of component weights
                - source: Where the weights came from (for transparency)
        """
        try:
            # Convert job_id to UUID if provided
            job_uuid = UUID(job_id) if job_id else None

            # Get weights using simple service
            weights = get_scoring_weights(agent.supabase_client, job_uuid)

            # Determine source for transparency
            source = "default"
            if job_uuid:
                # Quick check to see what was actually used
                try:
                    from database import database_service

                    jobs = database_service.get_jobs(
                        agent.supabase_client, active_only=False
                    )
                    job = next((j for j in jobs if j.get("id") == job_id), None)

                    if job:
                        if job.get("custom_weights"):
                            source = "job_custom"
                        elif job.get("template_id"):
                            source = "job_template"
                except:
                    pass  # Fallback to default

            return {
                "weights": weights,
                "source": source,
                "job_id": job_id,
                "success": True,
            }

        except Exception as exc:
            logger.error(f"Error getting scoring weights: {str(exc)}", exc_info=True)

            # Return default weights on error
            from services.scoring_service import DEFAULT_WEIGHTS

            return {
                "weights": DEFAULT_WEIGHTS,
                "source": "default_fallback",
                "error": str(exc),
                "success": False,
            }

    return select_scoring_template
