# **CANDIDATE PROFILE SYSTEM DESIGN**

## **🎯 SYSTEM OVERVIEW**

The Candidate Profile System enables semantic search for recruiters by combining CV data and Q&A responses into rich, searchable candidate profiles stored as vector embeddings in Pinecone.

### **🔄 WORKFLOW**

```
CV Upload → Tag Extraction → Q&A Assessment → Profile Generation → Vector Storage → Recruiter Search
```

1. **CV Analysis** → Extract initial tags and basic information
2. **Targeted Assessment** → Fill missing tags through optimized questions
3. **Profile Generation** → Combine CV + Q&A into standardized template
4. **Vector Embedding** → Generate searchable profile embedding
5. **Metadata Storage** → Store structured data for filtering
6. **Recruiter Search** → Semantic + tag-based candidate discovery

---

## **📄 CANDIDATE PROFILE TEMPLATE FORMAT**

### **🎨 Profile Template Structure**

The candidate profile combines CV content and Q&A responses into a comprehensive, searchable format optimized for semantic search.

```
CANDIDATE PROFILE - [CANDIDATE_NAME]

SUMMARY:
[AI-generated professional summary combining CV highlights and assessment responses]

PERSONAL INFORMATION:
- Name: [Full Name]
- Email: [Email Address]
- Location: [City, Country]
- Experience Level: [Entry/Junior/Mid/Senior/Executive]
- Availability: [Available immediately/2 weeks notice/etc.]

PROFESSIONAL EXPERIENCE:
- Total Years: [X years]
- Current Role: [Job Title] at [Company Name]
- Industry Experience: [Industries worked in]
- Management Experience: [Team size, budget responsibility, scope]
- Key Achievements: [Major accomplishments and metrics]

EDUCATION:
- Highest Degree: [Degree Level] in [Field]
- University: [University Name] ([Graduation Year])
- GPA: [Grade Point Average]
- Academic Honors: [Distinctions, awards]
- Relevant Coursework: [Key subjects related to career]

TECHNICAL SKILLS:
- Programming Languages: [Languages with proficiency levels]
- Frameworks & Tools: [Technical tools and platforms]
- Certifications: [Professional certifications]
- Software Proficiency: [Business software skills]

SOFT SKILLS & COMPETENCIES:
- Leadership Style: [Leadership approach and philosophy]
- Communication Skills: [Verbal, written, presentation abilities]
- Problem-Solving: [Approach to challenges and innovation]
- Team Collaboration: [Working style and team dynamics]
- Learning Agility: [Adaptability and continuous learning]

ACHIEVEMENTS & RECOGNITION:
- Awards: [Professional awards and recognition]
- Publications: [Papers, articles, research]
- Patents: [Intellectual property]
- Speaking Engagements: [Conferences, presentations]
- Performance Metrics: [Quantified achievements]

CAREER GOALS & PREFERENCES:
- Career Objectives: [Short and long-term goals]
- Preferred Industries: [Target industries]
- Work Environment: [Remote, hybrid, on-site preferences]
- Company Size: [Startup, SME, enterprise preferences]
- Growth Areas: [Skills wanting to develop]

COMPENSATION & LOGISTICS:
- Current Salary: [Current compensation range]
- Expected Salary: [Salary expectations]
- Location Flexibility: [Willingness to relocate]
- Travel Willingness: [Travel requirements acceptance]
- Work Authorization: [Visa status, work permits]

CULTURAL FIT INDICATORS:
- Values Alignment: [Core professional values]
- Communication Style: [Direct, collaborative, diplomatic]
- Work-Life Balance: [Preferences and boundaries]
- Innovation Appetite: [Risk tolerance, change adaptability]
- Team Dynamics: [Collaboration preferences]

ASSESSMENT INSIGHTS:
[AI analysis of assessment responses highlighting key strengths, potential concerns,
and fit indicators based on Q&A performance and response quality]
```

### **🔧 Template Generation Logic**

```python
class CandidateProfileTemplate:
    """Generates standardized candidate profiles for semantic search"""

    def generate_profile(
        self,
        cv_data: Dict,
        candidate_tags: List[CandidateTag],
        qa_responses: List[Dict],
        assessment_metadata: Dict
    ) -> str:
        """
        Combine all candidate data into searchable profile format

        Returns:
            Formatted profile text optimized for embedding generation
        """
```

---

## **🗄️ PINECONE INDEX STRUCTURE**

### **🎯 Index Configuration**

```python
# Candidate Profile Index Specification
INDEX_NAME = "leadersscout-candidates"
DIMENSION = 1536  # OpenAI text-embedding-ada-002
METRIC = "cosine"  # Optimal for semantic similarity
PODS = 1  # Scale based on candidate volume
```

### **📊 Vector Metadata Schema**

Each candidate profile vector includes rich metadata for filtering and ranking:

```python
{
    # Core Identification
    "candidate_id": "uuid-string",
    "cv_assessment_id": "uuid-string",
    "profile_version": "v1.0",
    "created_at": "2025-07-09T10:30:00Z",
    "updated_at": "2025-07-09T10:30:00Z",

    # Personal Information
    "candidate_name": "John Smith",
    "email": "<EMAIL>",
    "location_city": "Riyadh",
    "location_country": "Saudi Arabia",
    "nationality": "Saudi",

    # Experience & Level
    "experience_level": "senior",  # entry/junior/mid/senior/executive
    "total_years_experience": 8,
    "current_role": "Senior Software Engineer",
    "current_company": "Tech Corp",
    "industry_primary": "technology",
    "industry_secondary": "fintech",

    # Skills & Qualifications
    "degree_level": "masters",  # bachelors/masters/phd
    "major_field": "computer_science",
    "university_tier": "top",  # top/mid/regional
    "gpa": 3.8,
    "certifications": ["aws_certified", "pmp", "cissp"],

    # Technical Skills (for easy filtering)
    "programming_languages": ["python", "java", "javascript"],
    "frameworks": ["react", "django", "spring"],
    "cloud_platforms": ["aws", "azure"],

    # Compensation & Logistics
    "current_salary_range": "15000-20000",  # Monthly SAR
    "expected_salary_range": "18000-25000",
    "currency": "SAR",
    "remote_work_preference": "hybrid",
    "relocation_willingness": true,
    "visa_sponsorship_needed": false,

    # Assessment Metrics
    "assessment_completion_percentage": 95.0,
    "tag_coverage_percentage": 87.5,
    "ai_total_score": 78.5,
    "final_weighted_score": 82.0,
    "response_quality_score": 85.0,

    # Tag Coverage Flags (for quick filtering)
    "has_education_tags": true,
    "has_experience_tags": true,
    "has_skills_tags": true,
    "has_achievements_tags": true,
    "has_personal_tags": true,
    "has_cultural_tags": true,

    # Availability & Status
    "availability": "2_weeks_notice",
    "job_search_status": "actively_looking",
    "last_active": "2025-07-09T10:30:00Z",

    # Profile Completeness
    "profile_completeness_score": 92.5,
    "has_photo": false,
    "has_portfolio": true,
    "linkedin_url": "https://linkedin.com/in/johnsmith",

    # Search Optimization
    "profile_text_length": 2847,  # Character count for relevance scoring
    "keyword_density_score": 88.0,
    "semantic_richness_score": 91.5
}
```

### **🔍 Index Partitioning Strategy**

```python
# Namespace Strategy for Efficient Querying
NAMESPACES = {
    "active": "Currently active job seekers",
    "passive": "Passive candidates (not actively searching)",
    "archived": "Older profiles for reference",
    "premium": "Premium/executive level candidates",
    "regional": "Location-specific partitioning if needed"
}
```

---

## **🏗️ CANDIDATE PROFILE DATA MODELS**

### **📋 CandidateProfile Model**

```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum

class ProfileStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"
    INCOMPLETE = "incomplete"

class CandidateProfile(BaseModel):
    """Complete candidate profile model for semantic search"""

    # Core Identification
    id: Optional[UUID] = None
    cv_assessment_id: UUID
    candidate_name: str
    email: str

    # Profile Content
    profile_text: str = Field(..., description="Full formatted profile for embedding")
    profile_summary: str = Field(..., description="AI-generated professional summary")

    # Metadata
    experience_level: JobLevel
    total_years_experience: Optional[int] = None
    current_role: Optional[str] = None
    current_company: Optional[str] = None
    location: Optional[str] = None

    # Assessment Data
    assessment_completion: Decimal = Field(default=Decimal("0.0"), ge=0, le=100)
    tag_coverage: Decimal = Field(default=Decimal("0.0"), ge=0, le=100)
    final_score: Optional[Decimal] = None

    # Profile Status
    status: ProfileStatus = ProfileStatus.DRAFT
    is_searchable: bool = False
    profile_version: str = "1.0"

    # Timestamps
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_searched: Optional[datetime] = None

    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None
        }

class ProfileEmbedding(BaseModel):
    """Vector embedding for candidate profile"""

    id: Optional[UUID] = None
    candidate_profile_id: UUID
    embedding_vector: List[float] = Field(..., description="1536-dimensional embedding")
    embedding_model: str = "text-embedding-ada-002"

    # Vector Metadata (flattened for Pinecone)
    vector_metadata: Dict[str, Any] = Field(default_factory=dict)

    # Performance Metrics
    similarity_threshold: float = Field(default=0.7, description="Minimum similarity for matches")
    embedding_quality_score: Optional[float] = None

    created_at: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat() if v else None}

class ProfileSearchResult(BaseModel):
    """Search result for candidate profile queries"""

    candidate_profile_id: UUID
    candidate_name: str
    similarity_score: float = Field(..., ge=0, le=1)
    tag_match_score: float = Field(..., ge=0, le=1)
    combined_score: float = Field(..., ge=0, le=1)

    # Key Highlights
    profile_summary: str
    experience_level: JobLevel
    current_role: Optional[str] = None
    location: Optional[str] = None

    # Match Insights
    matching_keywords: List[str] = Field(default_factory=list)
    strength_areas: List[str] = Field(default_factory=list)
    similarity_explanation: Optional[str] = None

    # Additional Metadata
    profile_completeness: float
    last_updated: Optional[datetime] = None
```

### **🔧 Profile Generation Models**

```python
class ProfileGenerationRequest(BaseModel):
    """Request model for generating candidate profiles"""

    cv_assessment_id: UUID
    force_regenerate: bool = False
    include_incomplete_tags: bool = True
    profile_template_version: str = "v1.0"

class ProfileGenerationResponse(BaseModel):
    """Response model for profile generation"""

    success: bool
    candidate_profile_id: Optional[UUID] = None
    profile_text: Optional[str] = None
    embedding_stored: bool = False

    # Generation Metrics
    profile_length: int = 0
    completeness_score: float = 0.0
    quality_score: float = 0.0

    # Error Handling
    error_message: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)

    generation_time_ms: int = 0
    created_at: datetime = Field(default_factory=datetime.utcnow)
```

---

## **🔍 SEMANTIC SEARCH ARCHITECTURE**

### **🎯 Search System Components**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Recruiter     │───▶│   Search Query   │───▶│  Query Analysis │
│   Interface     │    │   Processing     │    │   & Enhancement │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Tag-Based     │◀───│   Dual Search    │───▶│   Semantic      │
│   Filtering     │    │   Coordinator    │    │   Vector Search │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                        │
         ▼                       ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Filtered Pool  │───▶│   Result Merger  │◀───│  Semantic Matches│
│  (Hard Reqs)    │    │   & Ranking      │    │  (Soft Reqs)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌──────────────────┐
                    │  Final Ranked    │
                    │  Search Results  │
                    └──────────────────┘
```

### **🔧 Search Types & Use Cases**

#### **1. Semantic Job Description Search**

```python
# Use Case: "Find candidates for Senior Python Developer role in fintech"
query = {
    "job_description": "Senior Python Developer with 5+ years experience...",
    "hard_requirements": {
        "experience_level": ["senior"],
        "programming_languages": ["python"],
        "min_years_experience": 5
    },
    "soft_preferences": {
        "industry_experience": ["fintech", "banking"],
        "frameworks": ["django", "flask"],
        "location": ["riyadh", "dubai"]
    }
}
```

#### **2. Similar Candidate Search**

```python
# Use Case: "Find candidates similar to our top performer"
query = {
    "reference_candidate_id": "uuid-of-existing-candidate",
    "similarity_threshold": 0.8,
    "exclude_current_employees": True,
    "max_results": 10
}
```

#### **3. Skills-Based Discovery**

```python
# Use Case: "Find AI/ML experts with leadership experience"
query = {
    "required_skills": ["machine_learning", "artificial_intelligence"],
    "leadership_experience": True,
    "management_scope": {"min_team_size": 3},
    "semantic_query": "AI team lead with deep learning expertise"
}
```

### **🎛️ Search Configuration**

```python
class SearchConfiguration:
    """Configurable search parameters"""

    # Vector Search Settings
    similarity_threshold: float = 0.7
    max_vector_results: int = 100

    # Tag Filtering Settings
    hard_requirements_weight: float = 0.4
    soft_preferences_weight: float = 0.3
    semantic_similarity_weight: float = 0.3

    # Result Ranking
    recency_boost: float = 0.1  # Boost for recently updated profiles
    completeness_boost: float = 0.05  # Boost for complete profiles
    score_boost: float = 0.05  # Boost for high-scoring candidates

    # Performance Settings
    max_results: int = 50
    timeout_seconds: int = 30
```

### **⚡ Search Performance Optimization**

#### **1. Index Optimization**

```python
# Pre-computed embeddings for common queries
COMMON_QUERY_EMBEDDINGS = {
    "software_engineer": [0.1, 0.2, ...],  # Pre-computed vector
    "data_scientist": [0.3, 0.4, ...],
    "product_manager": [0.5, 0.6, ...]
}

# Cached filter combinations
CACHED_FILTERS = {
    "senior_tech": {"experience_level": "senior", "industry": "technology"},
    "junior_finance": {"experience_level": "junior", "industry": "finance"}
}
```

#### **2. Query Optimization**

```python
class QueryOptimizer:
    """Optimize search queries for performance and relevance"""

    def optimize_query(self, raw_query: str) -> Dict[str, Any]:
        """
        Transform natural language query into optimized search parameters

        Returns:
            Optimized query with tag filters and semantic components
        """
        return {
            "semantic_query": self.extract_semantic_intent(raw_query),
            "tag_filters": self.extract_structured_filters(raw_query),
            "boost_keywords": self.identify_boost_keywords(raw_query)
        }
```

---

## **🎯 SUCCESS METRICS**

### **Profile Quality Metrics**

- **Profile Completeness:** Average 85%+ across all sections
- **Embedding Quality:** Semantic coherence score 90%+
- **Tag Coverage:** 80%+ of relevant tags captured per candidate
- **Update Frequency:** Profiles refreshed within 24h of assessment completion

### **Search Performance Metrics**

- **Search Accuracy:** 90%+ relevance for top 10 results
- **Search Speed:** Sub-200ms average response time
- **Match Quality:** 85%+ recruiter satisfaction with search results
- **System Scalability:** Handle 1000+ concurrent searches

### **Business Impact Metrics**

- **Candidate Discovery:** 3x improvement in relevant candidate finding
- **Time to Hire:** 40% reduction in initial screening time
- **Search Success Rate:** 70%+ of searches yield viable candidates
- **System Adoption:** 90%+ active usage by recruiters

---

## **🔧 TECHNICAL CONSIDERATIONS**

### **🛡️ Data Privacy & Security**

- **PII Protection:** Encrypt sensitive personal information
- **Access Control:** Role-based permissions for candidate data
- **Data Retention:** Configurable retention policies
- **GDPR Compliance:** Right to deletion and data portability

### **📈 Scalability Planning**

- **Index Partitioning:** Namespace strategy for growth
- **Caching Strategy:** Multi-level caching for frequent queries
- **Load Balancing:** Distribute search load across nodes
- **Monitoring:** Real-time performance and quality metrics

### **🔄 Data Consistency**

- **Profile Versioning:** Track profile changes over time
- **Sync Mechanisms:** Keep embeddings updated with profile changes
- **Conflict Resolution:** Handle concurrent profile updates
- **Backup & Recovery:** Regular vector data backups

---
