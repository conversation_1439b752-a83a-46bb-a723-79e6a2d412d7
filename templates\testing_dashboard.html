<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LeadersScout AI - Testing Dashboard</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      .container {
        background: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        text-align: center;
      }
      h1 {
        color: #333;
        margin-bottom: 10px;
        font-size: 32px;
      }
      .subtitle {
        color: #666;
        margin-bottom: 40px;
        font-size: 18px;
      }
      .interfaces-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
      }
      .interface-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 30px;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      .interface-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #007bff;
      }
      .interface-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #007bff, #28a745);
      }
      .interface-icon {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
      }
      .interface-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
      }
      .interface-description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
      .existing-features {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 10px;
        padding: 25px;
        margin-top: 30px;
      }
      .existing-features h2 {
        color: #0066cc;
        margin-bottom: 20px;
      }
      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }
      .feature-link {
        background: white;
        padding: 15px;
        border-radius: 8px;
        text-decoration: none;
        color: #0066cc;
        font-weight: 500;
        transition: all 0.2s ease;
      }
      .feature-link:hover {
        background: #f0f8ff;
        transform: translateY(-2px);
      }
      .status-badge {
        display: inline-block;
        background: #28a745;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
      }
      .api-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        text-align: left;
      }
      .api-info h3 {
        color: #856404;
        margin-bottom: 15px;
      }
      .api-endpoint {
        background: #f8f9fa;
        padding: 8px 12px;
        border-radius: 4px;
        font-family: "Courier New", monospace;
        font-size: 14px;
        margin: 5px 0;
        border-left: 3px solid #007bff;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🚀 LeadersScout AI</h1>

      <div class="interfaces-grid">
        <a href="/candidate-search" class="interface-card">
          <span class="interface-icon">🔍</span>
          <div class="interface-title">Candidate Search</div>
          <div class="interface-description">
            Test semantic and advanced candidate search functionality. Search by
            job description, apply filters, and view dual scoring results.
          </div>
        </a>

        <a href="/job-creation" class="interface-card">
          <span class="interface-icon">💼</span>
          <div class="interface-title">Job Management</div>
          <div class="interface-description">
            Create and manage job postings with custom scoring weights. Test
            job-candidate matching and view existing jobs.
          </div>
        </a>

        <a href="/candidate-profile-viewer" class="interface-card">
          <span class="interface-icon">👤</span>
          <div class="interface-title">Candidate Profiles</div>
          <div class="interface-description">
            View detailed candidate profiles, assessment responses, and find
            similar candidates using vector search.
          </div>
        </a>

        <a href="/generate-candidate-profiles/" class="interface-card">
          <span class="interface-icon">⚡</span>
          <div class="interface-title">Generate Profiles</div>
          <div class="interface-description">
            Generate candidate profiles for existing CV assessments. Required
            before candidates can be searched or viewed.
          </div>
        </a>
      </div>

      <div
        style="
          background: #e7f3ff;
          border: 1px solid #b3d9ff;
          border-radius: 10px;
          padding: 25px;
          margin-top: 30px;
          text-align: center;
        "
      >
      </div>
    </div>
  </body>
</html>
