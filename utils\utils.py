"""
Utility functions for the chatbot application.
"""

import re
import time
import logging
from openai import OpenAI
import os

# Configure logging
logger = logging.getLogger("utils")


def sanitize_text(text):
    """
    Remove null bytes and other problematic characters from text.

    This function removes characters that can cause database errors,
    particularly null bytes (\u0000) that PostgreSQL cannot handle.

    Args:
        text: The text to sanitize

    Returns:
        Sanitized text with problematic characters removed
    """
    if not text:
        return text

    # Remove null bytes and other control characters except newlines and tabs
    sanitized = "".join(char for char in text if ord(char) >= 32 or char in "\n\t\r")
    return sanitized


def is_arabic(text):
    """
    Check if text contains ANY Arabic characters.

    Args:
        text: The text to check

    Returns:
        True if the text contains ANY Arabic characters, False otherwise
    """
    if not text:
        return False

    # Arabic Unicode range - detect even a single character
    arabic_pattern = re.compile(r"[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]")
    # Check if there are ANY Arabic characters in the text
    return bool(arabic_pattern.search(text))


def has_mixed_language(text):
    """
    Check if text contains a mix of Arabic and English content.

    Args:
        text: The text to check

    Returns:
        True if the text contains both Arabic and English content
    """
    if not text:
        return False

    # Check for Arabic characters
    has_arabic = is_arabic(text)

    # Check for English words (at least one word with 2+ letters)
    has_english = bool(re.search(r"[a-zA-Z]{2,}", text))

    # Return True if both Arabic and English are present
    return has_arabic and has_english


def translate_text(text, target_language="English", max_retries=3, retry_delay=2):
    """
    Translate text to the target language using OpenAI.

    NOTE: This function is now used ONLY for custom answers processing
    (translating Arabic custom answers to English for embedding when needed).
    All message translation is handled by the agent internally via prompts.

    Args:
        text: The text to translate
        target_language: The target language (default: English)
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 2)

    Returns:
        Translated text or original text if translation fails
    """
    if not text:
        return text

    # Initialize OpenAI client
    client = OpenAI(
        api_key=os.environ.get("OPENAI_API_KEY"),
    )

    # Implement exponential backoff for retries
    for attempt in range(max_retries + 1):
        try:
            response = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {
                        "role": "system",
                        "content": f"You are a translator. Translate the following text to {target_language}. Preserve technical terms or proper nouns. Keep the translation concise and brief.",
                    },
                    {"role": "user", "content": text},
                ],
                temperature=0.4,
                max_tokens=250,  # Standardized token limit
            )
            return response.choices[0].message.content.strip()

        except Exception as e:
            # Check if this is a rate limit or quota error
            error_str = str(e).lower()
            is_rate_limit = any(
                term in error_str
                for term in [
                    "rate limit",
                    "quota",
                    "capacity",
                    "too many requests",
                    "429",
                ]
            )

            # Log the error
            if is_rate_limit:
                logger.warning(
                    f"OpenAI API rate limit error (attempt {attempt+1}/{max_retries+1}): {str(e)}"
                )
            else:
                logger.error(
                    f"Translation error (attempt {attempt+1}/{max_retries+1}): {str(e)}"
                )

            # If this is the last attempt, return the original text
            if attempt >= max_retries:
                logger.error(
                    f"Translation failed after {max_retries+1} attempts. Returning original text."
                )
                return text

            # Calculate exponential backoff delay (2^attempt * retry_delay)
            backoff_delay = retry_delay * (2**attempt)
            logger.info(f"Retrying translation in {backoff_delay} seconds...")

            # Wait before retrying
            time.sleep(backoff_delay)
