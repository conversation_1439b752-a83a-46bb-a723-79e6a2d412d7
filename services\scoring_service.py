"""
Simple Scoring Service

Ultra-simple approach for scoring weights:
1. Default weights (hardcoded)
2. Job-level custom weights (one-off overrides)
3. Saved templates (for reuse)

No complex validation, just simple CRUD and weight logic.
"""

import logging
from typing import Dict, Optional, List, Any
from uuid import UUID
from database import database_service

logger = logging.getLogger("scoring_service")

# Default platform weights (hardcoded per BRD)
# Education: 30%, Experience: 30%, Skills: 30%, Achievements: 10%
DEFAULT_WEIGHTS = {
    "education": 0.30,
    "experience": 0.30,
    "skills": 0.30,
    "achievements": 0.10,
}


def get_scoring_weights(
    supabase_client, job_id: Optional[UUID] = None
) -> Dict[str, float]:
    """
    Get scoring weights for a job.

    Priority:
    1. Job custom_weights (one-off override)
    2. Job template_id (saved template)
    3. Default weights (fallback)

    Args:
        supabase_client: Supabase client
        job_id: Optional job ID

    Returns:
        Dictionary of weights
    """
    try:
        if job_id:
            # Get job details
            jobs = database_service.get_jobs(supabase_client, active_only=False)
            job = None
            for j in jobs:
                if j.get("id") == str(job_id):
                    job = j
                    break

            if job:
                # Option 1: Job has one-off custom weights
                if job.get("custom_weights"):
                    logger.info(f"Using custom weights for job {job_id}")
                    return job["custom_weights"]

                # Option 2: Job uses a saved template
                if job.get("template_id"):
                    template = get_template(supabase_client, UUID(job["template_id"]))
                    if template:
                        logger.info(
                            f"Using template '{template['name']}' for job {job_id}"
                        )
                        return template["weights"]

        # Option 3: Use default weights
        logger.info("Using default platform weights")
        return DEFAULT_WEIGHTS

    except Exception as e:
        logger.error(f"Error getting scoring weights: {str(e)}")
        return DEFAULT_WEIGHTS


def create_template(
    supabase_client,
    name: str,
    weights: Dict[str, float],
    created_by: Optional[UUID] = None,
) -> Optional[Dict[str, Any]]:
    """Create a new scoring template for reuse."""
    try:
        # Validate weights sum to 1.0 (approximately)
        total = sum(weights.values())
        if abs(total - 1.0) > 0.001:
            logger.error(f"Weights must sum to 1.0, got {total}")
            return None

        template_data = {
            "name": name,
            "weights": weights,
            "created_by": str(created_by) if created_by else None,
        }

        result = (
            supabase_client.table("scoring_templates").insert(template_data).execute()
        )

        if result.data:
            logger.info(f"Created template: {name}")
            return result.data[0]
        return None

    except Exception as e:
        logger.error(f"Error creating template: {str(e)}")
        return None


def get_template(supabase_client, template_id: UUID) -> Optional[Dict[str, Any]]:
    """Get a specific template by ID."""
    try:
        result = (
            supabase_client.table("scoring_templates")
            .select("*")
            .eq("id", str(template_id))
            .execute()
        )

        if result.data:
            return result.data[0]
        return None

    except Exception as e:
        logger.error(f"Error getting template: {str(e)}")
        return None


def list_templates(
    supabase_client, created_by: Optional[UUID] = None
) -> List[Dict[str, Any]]:
    """List all templates, optionally filtered by creator."""
    try:
        query = supabase_client.table("scoring_templates").select("*")

        if created_by:
            query = query.eq("created_by", str(created_by))

        result = query.order("created_at", desc=True).execute()
        return result.data or []

    except Exception as e:
        logger.error(f"Error listing templates: {str(e)}")
        return []


def delete_template(supabase_client, template_id: UUID) -> bool:
    """Delete a template."""
    try:
        result = (
            supabase_client.table("scoring_templates")
            .delete()
            .eq("id", str(template_id))
            .execute()
        )
        return len(result.data) > 0

    except Exception as e:
        logger.error(f"Error deleting template: {str(e)}")
        return False


def update_job_weights(
    supabase_client,
    job_id: UUID,
    weights: Optional[Dict[str, float]] = None,
    template_id: Optional[UUID] = None,
) -> bool:
    """
    Update job scoring weights.

    Args:
        supabase_client: Supabase client
        job_id: Job to update
        weights: Custom weights (one-off) - clears template_id
        template_id: Use saved template - clears custom_weights
    """
    try:
        if weights and template_id:
            logger.error("Cannot set both custom weights and template_id")
            return False

        update_data = {}

        if weights:
            # Validate weights
            total = sum(weights.values())
            if abs(total - 1.0) > 0.001:
                logger.error(f"Weights must sum to 1.0, got {total}")
                return False

            update_data["custom_weights"] = weights
            update_data["template_id"] = None  # Clear template

        elif template_id:
            update_data["template_id"] = str(template_id)
            update_data["custom_weights"] = None  # Clear custom weights

        else:
            # Clear both (use defaults)
            update_data["custom_weights"] = None
            update_data["template_id"] = None

        result = (
            supabase_client.table("jobs")
            .update(update_data)
            .eq("id", str(job_id))
            .execute()
        )
        return len(result.data) > 0

    except Exception as e:
        logger.error(f"Error updating job weights: {str(e)}")
        return False
