"""
Dual Matching Service

Implements the core dual matching system that combines:
1. Tag-Based Pre-filtering: Filter candidates by hard requirements
2. Semantic Ranking: Rank candidates by semantic similarity
3. Combined Scoring: Merge tag and semantic scores
4. Configurable Weights: Adjust importance of each matching type

This service orchestrates the dual matching workflow and provides
configurable matching strategies for different use cases.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from uuid import UUID
from dataclasses import dataclass
from enum import Enum

from database.models import Job, CandidateTag, TagDefinition, JobLevel
from services.candidate_search_service import CandidateSearchService
from services.job_service import JobService

logger = logging.getLogger("dual_matching_service")


class MatchingStrategy(str, Enum):
    """Enum for different matching strategies"""

    TAG_ONLY = "tag_only"
    SEMANTIC_ONLY = "semantic_only"
    HYBRID_BALANCED = "hybrid_balanced"
    TAG_HEAVY = "tag_heavy"
    SEMANTIC_HEAVY = "semantic_heavy"
    CUSTOM = "custom"


@dataclass
class MatchingWeights:
    """Configuration for matching weights"""

    tag_weight: float = 0.4
    semantic_weight: float = 0.6

    def __post_init__(self):
        """Validate weights sum to 1.0"""
        total = self.tag_weight + self.semantic_weight
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"Weights must sum to 1.0, got {total}")


@dataclass
class HardRequirements:
    """Hard requirements that candidates must meet"""

    required_skills: Optional[List[str]] = None
    min_years_experience: Optional[int] = None
    max_years_experience: Optional[int] = None
    experience_levels: Optional[List[JobLevel]] = None
    education_levels: Optional[List[str]] = None
    locations: Optional[List[str]] = None
    salary_range: Optional[Dict[str, int]] = None
    must_have_tags: Optional[List[str]] = None  # Specific tags that must be present
    exclude_tags: Optional[List[str]] = None  # Tags that disqualify candidates


@dataclass
class SearchConfiguration:
    """Complete search configuration"""

    strategy: MatchingStrategy = MatchingStrategy.HYBRID_BALANCED
    weights: Optional[MatchingWeights] = None
    hard_requirements: Optional[HardRequirements] = None
    similarity_threshold: float = 0.6
    top_k: int = 20
    enable_fuzzy_matching: bool = True
    boost_recent_candidates: bool = False
    boost_complete_profiles: bool = True


class DualMatchingService:
    """Service for dual matching system combining tag-based and semantic matching"""

    def __init__(self, db_service):
        """Initialize the dual matching service"""
        self.db_service = db_service
        self.candidate_search_service = CandidateSearchService(db_service)
        self.job_service = JobService(db_service.supabase_client)

        # Predefined matching strategies
        self.strategies = {
            MatchingStrategy.TAG_ONLY: MatchingWeights(
                tag_weight=1.0, semantic_weight=0.0
            ),
            MatchingStrategy.SEMANTIC_ONLY: MatchingWeights(
                tag_weight=0.0, semantic_weight=1.0
            ),
            MatchingStrategy.HYBRID_BALANCED: MatchingWeights(
                tag_weight=0.5, semantic_weight=0.5
            ),
            MatchingStrategy.TAG_HEAVY: MatchingWeights(
                tag_weight=0.7, semantic_weight=0.3
            ),
            MatchingStrategy.SEMANTIC_HEAVY: MatchingWeights(
                tag_weight=0.3, semantic_weight=0.7
            ),
        }

    async def find_matching_candidates(
        self,
        job_description: str,
        config: SearchConfiguration,
        job_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """
        Find candidates using the dual matching system.

        Args:
            job_description: Job description for semantic matching
            config: Search configuration including strategy and requirements
            job_id: Optional job ID for job-specific configuration

        Returns:
            Dictionary containing matching candidates and detailed scoring
        """
        try:
            logger.info(
                f"Starting dual matching search with strategy: {config.strategy}"
            )

            # Step 1: Get matching weights
            weights = self._get_matching_weights(config)

            # Step 2: Apply hard requirements pre-filtering
            pre_filtered_pool = await self._apply_hard_requirements_filter(
                config.hard_requirements
            )

            # Step 3: Perform dual matching based on strategy
            if config.strategy == MatchingStrategy.TAG_ONLY:
                results = await self._tag_only_matching(
                    job_description, config, pre_filtered_pool
                )
            elif config.strategy == MatchingStrategy.SEMANTIC_ONLY:
                results = await self._semantic_only_matching(
                    job_description, config, pre_filtered_pool
                )
            else:
                results = await self._hybrid_matching(
                    job_description, config, weights, pre_filtered_pool
                )

            # Step 4: Apply post-processing enhancements
            enhanced_results = await self._apply_post_processing(results, config)

            # Step 5: Generate matching insights
            insights = self._generate_matching_insights(enhanced_results, config)

            return {
                "success": True,
                "total_results": len(enhanced_results),
                "candidates": enhanced_results,
                "matching_strategy": config.strategy.value,
                "weights_used": {
                    "tag_weight": weights.tag_weight,
                    "semantic_weight": weights.semantic_weight,
                },
                "search_configuration": {
                    "similarity_threshold": config.similarity_threshold,
                    "top_k": config.top_k,
                    "hard_requirements_applied": config.hard_requirements is not None,
                    "fuzzy_matching_enabled": config.enable_fuzzy_matching,
                    "recent_boost_enabled": config.boost_recent_candidates,
                    "complete_profile_boost_enabled": config.boost_complete_profiles,
                },
                "matching_insights": insights,
                "job_description": job_description,
                "job_id": str(job_id) if job_id else None,
            }

        except Exception as e:
            logger.error(f"Error in dual matching search: {str(e)}")
            return {
                "success": False,
                "error_message": str(e),
                "total_results": 0,
                "candidates": [],
                "matching_strategy": config.strategy.value if config else "unknown",
            }

    def _get_matching_weights(self, config: SearchConfiguration) -> MatchingWeights:
        """Get matching weights based on strategy and configuration"""
        if config.weights:
            return config.weights
        elif config.strategy in self.strategies:
            return self.strategies[config.strategy]
        else:
            return MatchingWeights()  # Default balanced weights

    async def _apply_hard_requirements_filter(
        self, hard_requirements: Optional[HardRequirements]
    ) -> Optional[List[str]]:
        """
        Apply hard requirements to pre-filter candidate pool.
        Returns list of candidate IDs that meet hard requirements, or None for no filtering.
        """
        if not hard_requirements:
            return None

        try:
            logger.info("Applying hard requirements pre-filtering")

            # Get all candidates with their tags
            all_assessments = self.db_service.get_all_cv_assessments()
            qualified_candidates = []

            for assessment in all_assessments:
                if not assessment.get("is_complete", False):
                    continue  # Skip incomplete assessments

                cv_assessment_id = assessment["id"]
                candidate_tags = self.db_service.get_candidate_tags(
                    str(cv_assessment_id)
                )

                if self._meets_hard_requirements(candidate_tags, hard_requirements):
                    qualified_candidates.append(str(cv_assessment_id))

            logger.info(
                f"Hard requirements filtering: {len(qualified_candidates)} candidates qualified"
            )
            return qualified_candidates

        except Exception as e:
            logger.error(f"Error applying hard requirements filter: {str(e)}")
            return None

    def _meets_hard_requirements(
        self, candidate_tags: List[Dict[str, Any]], requirements: HardRequirements
    ) -> bool:
        """Check if candidate meets all hard requirements"""
        try:
            # Convert tags to searchable format
            tag_values = {}
            for tag_data in candidate_tags:
                tag = CandidateTag(**tag_data)
                if tag.tag_definition:
                    category = getattr(tag.tag_definition, "category", {}).get(
                        "name", "unknown"
                    )
                    tag_key = tag.tag_definition.tag_key
                    tag_values[f"{category}.{tag_key}"] = tag.value.lower()

            # Check required skills
            if requirements.required_skills:
                if not self._has_required_skills(
                    tag_values, requirements.required_skills
                ):
                    return False

            # Check experience requirements
            if requirements.min_years_experience or requirements.max_years_experience:
                years_exp = self._extract_years_experience(tag_values)
                if years_exp is not None:
                    if (
                        requirements.min_years_experience
                        and years_exp < requirements.min_years_experience
                    ):
                        return False
                    if (
                        requirements.max_years_experience
                        and years_exp > requirements.max_years_experience
                    ):
                        return False

            # Check experience levels
            if requirements.experience_levels:
                candidate_level = self._extract_experience_level(tag_values)
                if candidate_level not in [
                    level.value for level in requirements.experience_levels
                ]:
                    return False

            # Check education levels
            if requirements.education_levels:
                if not self._has_required_education(
                    tag_values, requirements.education_levels
                ):
                    return False

            # Check locations
            if requirements.locations:
                if not self._matches_location(tag_values, requirements.locations):
                    return False

            # Check must-have tags
            if requirements.must_have_tags:
                if not self._has_must_have_tags(
                    tag_values, requirements.must_have_tags
                ):
                    return False

            # Check exclude tags
            if requirements.exclude_tags:
                if self._has_exclude_tags(tag_values, requirements.exclude_tags):
                    return False

            return True

        except Exception as e:
            logger.warning(f"Error checking hard requirements: {str(e)}")
            return False

    def _has_required_skills(
        self, tag_values: Dict[str, str], required_skills: List[str]
    ) -> bool:
        """Check if candidate has required skills"""
        candidate_skills = []
        for key, value in tag_values.items():
            if any(
                skill_key in key.lower()
                for skill_key in ["skill", "programming", "technology", "tool"]
            ):
                candidate_skills.extend(
                    [skill.strip().lower() for skill in value.split(",")]
                )

        for required_skill in required_skills:
            if not any(required_skill.lower() in skill for skill in candidate_skills):
                return False
        return True

    def _extract_years_experience(self, tag_values: Dict[str, str]) -> Optional[int]:
        """Extract years of experience from tags"""
        for key, value in tag_values.items():
            if "years" in key.lower() and "experience" in key.lower():
                try:
                    return int(value)
                except ValueError:
                    continue
        return None

    def _extract_experience_level(self, tag_values: Dict[str, str]) -> Optional[str]:
        """Extract experience level from tags"""
        for key, value in tag_values.items():
            if "experience" in key.lower() and "level" in key.lower():
                return value.lower()
        return None

    def _has_required_education(
        self, tag_values: Dict[str, str], required_levels: List[str]
    ) -> bool:
        """Check if candidate has required education level"""
        education_hierarchy = {
            "high school": 1,
            "associate": 2,
            "bachelor": 3,
            "master": 4,
            "phd": 5,
            "doctorate": 5,
        }

        min_required_level = min(
            education_hierarchy.get(level.lower(), 3) for level in required_levels
        )

        for key, value in tag_values.items():
            if "degree" in key.lower() or "education" in key.lower():
                for edu_level, level_num in education_hierarchy.items():
                    if edu_level in value.lower() and level_num >= min_required_level:
                        return True
        return False

    def _matches_location(
        self, tag_values: Dict[str, str], required_locations: List[str]
    ) -> bool:
        """Check if candidate matches location requirements"""
        for key, value in tag_values.items():
            if "location" in key.lower() or "city" in key.lower():
                for required_location in required_locations:
                    if required_location.lower() in value.lower():
                        return True
        return False

    def _has_must_have_tags(
        self, tag_values: Dict[str, str], must_have_tags: List[str]
    ) -> bool:
        """Check if candidate has all must-have tags"""
        for must_have_tag in must_have_tags:
            found = False
            for key, value in tag_values.items():
                if (
                    must_have_tag.lower() in key.lower()
                    or must_have_tag.lower() in value.lower()
                ):
                    found = True
                    break
            if not found:
                return False
        return True

    def _has_exclude_tags(
        self, tag_values: Dict[str, str], exclude_tags: List[str]
    ) -> bool:
        """Check if candidate has any exclude tags"""
        for exclude_tag in exclude_tags:
            for key, value in tag_values.items():
                if (
                    exclude_tag.lower() in key.lower()
                    or exclude_tag.lower() in value.lower()
                ):
                    return True
        return False

    async def _tag_only_matching(
        self,
        job_description: str,
        config: SearchConfiguration,
        pre_filtered_pool: Optional[List[str]],
    ) -> List[Dict[str, Any]]:
        """Perform tag-only matching"""
        logger.info("Performing tag-only matching")

        # Use existing candidate search with tag-only mode
        filters = self._build_search_filters(config.hard_requirements)

        results = await self.candidate_search_service.search_candidates(
            query_text=job_description,
            filters=filters,
            top_k=config.top_k,
            similarity_threshold=0.0,  # Don't filter by semantic similarity
            hybrid_mode=False,  # Tag-only mode
            tag_weight=1.0,
            semantic_weight=0.0,
        )

        return results.get("candidates", [])

    async def _semantic_only_matching(
        self,
        job_description: str,
        config: SearchConfiguration,
        pre_filtered_pool: Optional[List[str]],
    ) -> List[Dict[str, Any]]:
        """Perform semantic-only matching"""
        logger.info("Performing semantic-only matching")

        results = await self.candidate_search_service.search_candidates(
            query_text=job_description,
            filters=None,  # No tag filtering
            top_k=config.top_k,
            similarity_threshold=config.similarity_threshold,
            hybrid_mode=False,  # Semantic-only mode
            tag_weight=0.0,
            semantic_weight=1.0,
        )

        return results.get("candidates", [])

    async def _hybrid_matching(
        self,
        job_description: str,
        config: SearchConfiguration,
        weights: MatchingWeights,
        pre_filtered_pool: Optional[List[str]],
    ) -> List[Dict[str, Any]]:
        """Perform hybrid matching combining tag and semantic approaches"""
        logger.info(
            f"Performing hybrid matching with weights: tag={weights.tag_weight}, semantic={weights.semantic_weight}"
        )

        filters = self._build_search_filters(config.hard_requirements)

        results = await self.candidate_search_service.search_candidates(
            query_text=job_description,
            filters=filters,
            top_k=config.top_k,
            similarity_threshold=config.similarity_threshold,
            hybrid_mode=True,
            tag_weight=weights.tag_weight,
            semantic_weight=weights.semantic_weight,
        )

        return results.get("candidates", [])

    def _build_search_filters(
        self, hard_requirements: Optional[HardRequirements]
    ) -> Optional[Dict[str, Any]]:
        """Build search filters from hard requirements"""
        if not hard_requirements:
            return None

        filters = {}

        if hard_requirements.required_skills:
            filters["required_skills"] = hard_requirements.required_skills
        if hard_requirements.min_years_experience:
            filters["min_years_experience"] = hard_requirements.min_years_experience
        if hard_requirements.experience_levels:
            filters["experience_level"] = hard_requirements.experience_levels[
                0
            ].value  # Use first level
        if hard_requirements.education_levels:
            filters["education_level"] = hard_requirements.education_levels[
                0
            ]  # Use first level
        if hard_requirements.locations:
            filters["location"] = hard_requirements.locations[0]  # Use first location
        if hard_requirements.salary_range:
            filters["salary_range"] = hard_requirements.salary_range

        return filters if filters else None

    async def _apply_post_processing(
        self, candidates: List[Dict[str, Any]], config: SearchConfiguration
    ) -> List[Dict[str, Any]]:
        """Apply post-processing enhancements to search results"""
        enhanced_candidates = []

        for candidate in candidates:
            enhanced_candidate = candidate.copy()

            # Apply recency boost
            if config.boost_recent_candidates:
                enhanced_candidate = self._apply_recency_boost(enhanced_candidate)

            # Apply profile completeness boost
            if config.boost_complete_profiles:
                enhanced_candidate = self._apply_completeness_boost(enhanced_candidate)

            # Recalculate final score with boosts
            enhanced_candidate = self._recalculate_final_score(enhanced_candidate)

            enhanced_candidates.append(enhanced_candidate)

        # Re-sort by final score
        enhanced_candidates.sort(key=lambda x: x.get("final_score", 0), reverse=True)

        return enhanced_candidates

    def _apply_recency_boost(self, candidate: Dict[str, Any]) -> Dict[str, Any]:
        """Apply boost for recently created profiles"""
        # Implementation would check candidate creation date and apply boost
        # For now, just add the boost factor
        candidate["recency_boost"] = 0.05  # 5% boost for recent candidates
        return candidate

    def _apply_completeness_boost(self, candidate: Dict[str, Any]) -> Dict[str, Any]:
        """Apply boost for complete profiles"""
        completeness_score = candidate.get("profile_completeness", 0)
        if completeness_score > 0.8:  # 80% complete
            candidate["completeness_boost"] = 0.1  # 10% boost
        elif completeness_score > 0.6:  # 60% complete
            candidate["completeness_boost"] = 0.05  # 5% boost
        else:
            candidate["completeness_boost"] = 0.0
        return candidate

    def _recalculate_final_score(self, candidate: Dict[str, Any]) -> Dict[str, Any]:
        """Recalculate final score including all boosts"""
        base_score = candidate.get(
            "hybrid_score",
            candidate.get("semantic_score", candidate.get("tag_score", 0)),
        )
        recency_boost = candidate.get("recency_boost", 0)
        completeness_boost = candidate.get("completeness_boost", 0)

        final_score = base_score + recency_boost + completeness_boost
        candidate["final_score"] = min(final_score, 1.0)  # Cap at 1.0

        return candidate

    def _generate_matching_insights(
        self, candidates: List[Dict[str, Any]], config: SearchConfiguration
    ) -> Dict[str, Any]:
        """Generate insights about the matching results"""
        if not candidates:
            return {"message": "No candidates found matching the criteria"}

        # Calculate score distribution
        scores = [c.get("final_score", 0) for c in candidates]
        avg_score = sum(scores) / len(scores) if scores else 0
        max_score = max(scores) if scores else 0
        min_score = min(scores) if scores else 0

        # Analyze matching quality
        high_quality_matches = len([s for s in scores if s > 0.8])
        medium_quality_matches = len([s for s in scores if 0.6 <= s <= 0.8])
        low_quality_matches = len([s for s in scores if s < 0.6])

        return {
            "total_candidates": len(candidates),
            "score_distribution": {
                "average": round(avg_score, 3),
                "maximum": round(max_score, 3),
                "minimum": round(min_score, 3),
            },
            "quality_breakdown": {
                "high_quality": high_quality_matches,
                "medium_quality": medium_quality_matches,
                "low_quality": low_quality_matches,
            },
            "matching_effectiveness": {
                "excellent_matches": high_quality_matches,
                "good_matches": medium_quality_matches,
                "fair_matches": low_quality_matches,
                "recommendation": self._get_search_recommendation(
                    high_quality_matches, len(candidates)
                ),
            },
        }

    def _get_search_recommendation(
        self, high_quality_count: int, total_count: int
    ) -> str:
        """Get recommendation based on search results quality"""
        if total_count == 0:
            return "No candidates found. Consider broadening search criteria."

        quality_ratio = high_quality_count / total_count

        if quality_ratio > 0.5:
            return "Excellent match quality. Consider these top candidates."
        elif quality_ratio > 0.3:
            return "Good match quality. Review top candidates and consider expanding search."
        elif quality_ratio > 0.1:
            return "Fair match quality. Consider adjusting search criteria or weights."
        else:
            return "Low match quality. Recommend broadening criteria or using different matching strategy."

    async def find_candidates_for_job(
        self, job_id: UUID, config: Optional[SearchConfiguration] = None
    ) -> Dict[str, Any]:
        """Find candidates for a specific job using its requirements"""
        try:
            # Get job details
            job = self.job_service.get_job(job_id)
            if not job:
                return {
                    "success": False,
                    "error_message": f"Job {job_id} not found",
                    "total_results": 0,
                    "candidates": [],
                }

            # Use job description for semantic matching
            job_description = f"{job.title}\n\n{job.description}"
            if job.requirements:
                job_description += f"\n\nRequirements:\n{job.requirements}"

            # Use default config if none provided
            if not config:
                config = SearchConfiguration()

            # Find matching candidates
            results = await self.find_matching_candidates(
                job_description=job_description, config=config, job_id=job_id
            )

            # Add job information to results
            results["job_info"] = {
                "id": str(job.id),
                "title": job.title,
                "company": job.company,
                "location": job.location,
                "experience_level": (
                    job.experience_level.value if job.experience_level else None
                ),
                "job_type": job.job_type.value if job.job_type else None,
            }

            return results

        except Exception as e:
            logger.error(f"Error finding candidates for job {job_id}: {str(e)}")
            return {
                "success": False,
                "error_message": str(e),
                "total_results": 0,
                "candidates": [],
                "job_id": str(job_id),
            }


# Convenience functions
def get_dual_matching_service(db_service) -> DualMatchingService:
    """Get initialized dual matching service"""
    return DualMatchingService(db_service)


def create_search_config(
    strategy: MatchingStrategy = MatchingStrategy.HYBRID_BALANCED,
    tag_weight: float = 0.4,
    semantic_weight: float = 0.6,
    **kwargs,
) -> SearchConfiguration:
    """Create a search configuration with custom parameters"""
    weights = MatchingWeights(tag_weight=tag_weight, semantic_weight=semantic_weight)
    return SearchConfiguration(strategy=strategy, weights=weights, **kwargs)


def create_hard_requirements(**kwargs) -> HardRequirements:
    """Create hard requirements configuration"""
    return HardRequirements(**kwargs)
