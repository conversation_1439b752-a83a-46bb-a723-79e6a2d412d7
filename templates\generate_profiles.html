<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Generate Candidate Profiles - Testing Interface</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
      }
      .nav-links {
        text-align: center;
        margin-bottom: 20px;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 500;
      }
      .nav-links a:hover {
        text-decoration: underline;
      }
      .action-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 20px;
        text-align: center;
      }
      .action-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
      }
      .action-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
      }
      button {
        background: #28a745;
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        margin: 10px;
      }
      button:hover {
        background: #218838;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .btn-secondary {
        background: #007bff;
      }
      .btn-secondary:hover {
        background: #0056b3;
      }
      .loading {
        display: none;
        text-align: center;
        padding: 20px;
        color: #666;
      }
      .result {
        display: none;
        padding: 20px;
        border-radius: 6px;
        margin-top: 20px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .single-profile {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
      }
      .single-profile h3 {
        color: #856404;
        margin-bottom: 15px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #555;
      }
      input {
        width: 200px;
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }
      .stat-item {
        background: white;
        padding: 15px;
        border-radius: 6px;
        text-align: center;
        border: 1px solid #ddd;
      }
      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #007bff;
      }
      .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
      }
      .errors-list {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-top: 15px;
      }
      .errors-list h4 {
        color: #dc3545;
        margin-bottom: 10px;
      }
      .error-item {
        background: white;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 8px;
        border-left: 3px solid #dc3545;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="nav-links">
      <a href="/testing-dashboard">← Back to Dashboard</a>
      <a href="/candidate-search">Candidate Search</a>
      <a href="/candidate-profile-viewer">Profile Viewer</a>
    </div>

    <div class="container">
      <h1>⚡ Generate Candidate Profiles</h1>

      <div class="action-section">
        <div class="action-title">Bulk Profile Generation</div>
        <div class="action-description">
          Generate candidate profiles for all completed CV assessments.
        </div>
        <button onclick="generateAllProfiles()">Generate All Profiles</button>
      </div>

      <div class="single-profile">
        <h3>Generate Single Profile</h3>
        <p>Generate a profile for a specific CV assessment ID:</p>
        <div class="form-group">
          <label for="assessmentId">Assessment ID:</label>
          <input
            type="number"
            id="assessmentId"
            placeholder="e.g., 99"
            min="1"
          />
          <button class="btn-secondary" onclick="generateSingleProfile()">
            Generate Profile
          </button>
        </div>
      </div>

      <div class="loading" id="loading">
        <p>⚡ Generating candidate profiles...</p>
      </div>

      <div class="result" id="result"></div>
    </div>

    <script>
      async function generateAllProfiles() {
        const loading = document.getElementById("loading");
        const result = document.getElementById("result");

        loading.style.display = "block";
        result.style.display = "none";

        try {
          const response = await fetch("/api/generate-candidate-profiles/");
          const data = await response.json();

          displayResult(data, "bulk");
        } catch (err) {
          console.error("Generation error:", err);
          displayError(`Failed to generate profiles: ${err.message}`);
        } finally {
          loading.style.display = "none";
        }
      }

      async function generateSingleProfile() {
        const assessmentId = document.getElementById("assessmentId").value;
        if (!assessmentId) {
          alert("Please enter an assessment ID");
          return;
        }

        const loading = document.getElementById("loading");
        const result = document.getElementById("result");

        loading.style.display = "block";
        result.style.display = "none";

        try {
          const response = await fetch(`/generate-profile/${assessmentId}`);
          const data = await response.json();

          displayResult(data, "single");
        } catch (err) {
          console.error("Generation error:", err);
          displayError(`Failed to generate profile: ${err.message}`);
        } finally {
          loading.style.display = "none";
        }
      }

      function displayResult(data, type) {
        const result = document.getElementById("result");

        if (data.status === "success") {
          result.className = "result success";

          if (type === "bulk") {
            let html = `
                        <h3>✅ Bulk Generation Complete</h3>
                        <p>${data.message}</p>
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-value">${data.processed}</div>
                                <div class="stat-label">Processed</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${
                                  data.total_assessments
                                }</div>
                                <div class="stat-label">Total Assessments</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${
                                  data.errors ? data.errors.length : 0
                                }</div>
                                <div class="stat-label">Errors</div>
                            </div>
                        </div>
                    `;

            if (data.errors && data.errors.length > 0) {
              html += `
                            <div class="errors-list">
                                <h4>Errors Encountered:</h4>
                                ${data.errors
                                  .map(
                                    (error) =>
                                      `<div class="error-item">${error}</div>`
                                  )
                                  .join("")}
                            </div>
                        `;
            }

            result.innerHTML = html;
          } else {
            result.innerHTML = `
                        <h3>✅ Profile Generated Successfully</h3>
                        <p>${data.message}</p>
                        <p><strong>Assessment ID:</strong> ${data.assessment_id}</p>
                        <div style="margin-top: 15px;">
                            <a href="/candidate-profile/${data.assessment_id}" target="_blank" style="color: #007bff;">View Profile →</a>
                        </div>
                    `;
          }
        } else if (data.status === "info") {
          result.className = "result info";
          result.innerHTML = `
                    <h3>ℹ️ Information</h3>
                    <p>${data.message}</p>
                `;
        } else {
          displayError(data.message || "Unknown error occurred");
        }

        result.style.display = "block";
      }

      function displayError(message) {
        const result = document.getElementById("result");
        result.className = "result error";
        result.innerHTML = `
                <h3>❌ Error</h3>
                <p>${message}</p>
            `;
        result.style.display = "block";
      }
    </script>
  </body>
</html>
