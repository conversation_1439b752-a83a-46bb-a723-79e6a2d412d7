"""
RAG (Retrieval-Augmented Generation) Service Module

This module provides functionality for retrieving and generating responses
using a combination of vector search and language models.
"""

import os
import logging
import pinecone


# Lang<PERSON>hain imports
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_core.prompts import Chat<PERSON><PERSON>ptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableMap
from langchain.schema import HumanMessage, AIMessage

# Use the dedicated langchain-pinecone package instead of the community one
try:
    # Try to import from the dedicated package first
    from langchain_pinecone import PineconeVectorStore
except ImportError:
    # Fall back to the community package if the dedicated one is not installed
    from langchain_community.vectorstores.pinecone import (
        Pinecone as PineconeVectorStore,
    )

# Import custom answers service
from services import custom_answers_service


# Configure logging
logger = logging.getLogger("rag_service")

# Get environment variables
HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
HOST_DOMAIN_ARABIC = os.environ.get("HOST_DOMAIN_ARABIC")


def initialize_rag_components():
    """
    Initialize and return the RAG components.

    Returns:
        Dictionary containing the initialized components or None if initialization fails
    """
    # Get Pinecone configuration from environment variables
    pinecone_api_key = os.environ.get("PINECONE_API_KEY")
    pinecone_index_name = os.environ.get("PINECONE_INDEX_NAME")

    if not pinecone_api_key:
        logger.error("Pinecone API key not found in environment variables")
        return None

    # Initialize Pinecone
    try:
        pc = pinecone.Pinecone(api_key=pinecone_api_key)
        return {"pinecone_client": pc, "index_name": pinecone_index_name}
    except Exception as e:
        logger.error(f"Error initializing Pinecone: {e}")
        return None


def get_rag_chain(supabase_client):
    """
    Initialize and return the RAG chain.
    Creates a new instance for each request to prevent memory issues.

    Args:
        supabase_client: Initialized Supabase client for database operations

    Returns:
        Initialized RAG chain or None if initialization fails
    """
    # Initialize RAG components
    rag_components = initialize_rag_components()
    if not rag_components:
        logger.error("Failed to initialize RAG components")
        return None

    # Initialize embeddings
    embeddings = OpenAIEmbeddings()

    # Get Pinecone client and index name
    pc = rag_components["pinecone_client"]
    pinecone_index_name = rag_components["index_name"]

    # Connect to the existing index
    try:
        # Check if index exists
        existing_indexes = [index.name for index in pc.list_indexes()]

        if pinecone_index_name not in existing_indexes:
            # If running in production and index doesn't exist, we'll use a fallback approach
            # without RAG until the index is created
            logger.warning(f"Pinecone index '{pinecone_index_name}' not found")
            return None

        # Get the index
        index = pc.Index(pinecone_index_name)

        # Check if the index is empty
        stats = index.describe_index_stats()
        total_vectors = stats.get("total_vector_count", 0)

        if total_vectors == 0:
            logger.warning(f"Pinecone index '{pinecone_index_name}' is empty")
            return None

        # Initialize the vector store with the existing index
        try:
            # Create the vector store using the index name
            vectorstore = PineconeVectorStore.from_existing_index(
                index_name=pinecone_index_name,
                embedding=embeddings,
                text_key="text",  # Default text key
            )
        except Exception as exc:
            # Log the exception before re-raising
            logger.error(f"Error initializing Pinecone vector store: {exc}")
            # Re-raise the exception
            raise

        # Create a retriever number of documents
        retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 5},
        )

        # Get company name from environment variable
        company_name = os.environ.get("COMPANY_NAME")

        # Create the RAG prompt using MessagesPlaceholder for conversation history
        system_message = f"""
        You are a knowledge base search tool used by a chatbot agent that serves as a customer service representative for {company_name}. You MUST ONLY answer questions based on the following context information.

        Context:
        {{context}}

        Important instructions:
        1. Answer ONLY based on the context provided. If the information isn't in the context, say you don't have that information.
        2. EXTREMELY IMPORTANT: If there is a "CUSTOM ANSWER (PRIORITY INFORMATION)" section in the context, prioritize this information over all other context. This is the most accurate and up-to-date information.
        3. If the custom answer is sufficient to fully answer the user's question, use it exclusively. If not, combine it with other context information.
        4. Do not use any prior knowledge or information not contained in the context.
        5. Do not make up or infer information that is not explicitly stated in the context.
        6. Keep your answers focused on the company's services and solutions.
        7. Keep your answers simple and easy to understand - avoid technical jargon.
        8. Use simple language that non-technical people can easily understand.
        9. if the user asks a question that is not related to the company's services or solutions or it has no context in the knowledge base, give a response that you are not able to answer.
        """

        # Create the prompt with MessagesPlaceholder for conversation history
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_message),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{question}"),
            ]
        )

        # Initialize the language model with standardized token limit
        model = ChatOpenAI(model="gpt-4.1-mini", temperature=0.4, max_tokens=250)

        # Create the RAG chain with conversation history support using LangChain memory
        from services.memory_service import SupabaseMemory

        # Create a memory instance for the RAG chain
        def get_memory_for_session(session_id):
            if not session_id:
                return None

            return SupabaseMemory(
                conversation_id=session_id,
                supabase_client=supabase_client,
                max_message_limit=15,  # Keep the most recent 15 messages
            )

        def add_history(input_dict):
            # Get the session ID from the input
            session_id = input_dict.get("session_id", "")

            # Get the language preference from the input
            language = input_dict.get("language", "English")

            # Log the language being passed to the RAG chain
            logger.info(f"Language passed to RAG chain: {language}")

            # Get conversation history as LangChain message objects
            chat_history = []
            if session_id:
                # Get memory for this session
                memory = get_memory_for_session(session_id)

                if memory:
                    # Load memory variables
                    memory_vars = memory.load_memory_variables({})

                    # Log memory variables
                    if memory_vars.get("chat_history"):
                        logger.info(
                            f"Using memory with {len(memory_vars['chat_history'])} messages"
                        )
                        for i, msg in enumerate(memory_vars["chat_history"]):
                            logger.info(
                                f"Memory message {i+1}: {type(msg).__name__} - {msg.content[:50]}..."
                            )
                        # Use the chat history directly as LangChain message objects
                        chat_history = memory_vars["chat_history"]
                    else:
                        logger.info("Memory is empty or chat_history not found")
                else:
                    # Fallback to the old approach if memory is not available
                    try:
                        messages_result = (
                            supabase_client.table("messages")
                            .select("role, content")
                            .eq("session_id", session_id)
                            .order("created_at", desc=False)
                            .execute()
                        )

                        if messages_result.data and len(messages_result.data) > 0:
                            # Convert database messages to LangChain message objects
                            for msg in messages_result.data:
                                # Skip system messages as they're not part of the actual conversation
                                if msg["role"] == "human" or msg["role"] == "user":
                                    chat_history.append(
                                        HumanMessage(content=msg["content"])
                                    )
                                elif msg["role"] == "assistant":
                                    chat_history.append(
                                        AIMessage(content=msg["content"])
                                    )
                    except Exception as e:
                        # Log the error but continue without history
                        logger.error(
                            f"Error retrieving conversation history from database: {e}"
                        )

            # Return the input with conversation history as LangChain message objects
            return {
                "context": input_dict["context"],
                "question": input_dict["question"],
                "chat_history": chat_history,  # Use chat_history instead of conversation_history
                "language": language,  # Pass the language preference to the prompt
            }

        # Create a function to get context from the retriever and custom answers
        def get_context(input_dict):
            query = input_dict["question"]

            # First, check for custom answers (query should already be in English from agent)
            custom_answer = None
            try:
                # Find matching custom answer (expecting English query from agent)
                custom_answer = custom_answers_service.find_matching_custom_answer(
                    query
                )
            except Exception as e:
                logger.error(f"Error finding custom answer: {str(e)}")

            # Get context from Pinecone (expecting English query from agent)
            pinecone_context = retriever.invoke(query)

            # If custom answer is found, add it to the beginning of the context
            if custom_answer:
                # Prepare custom answer context
                custom_context = f"CUSTOM ANSWER (PRIORITY INFORMATION):\nQuestion: {custom_answer.question_pattern}\nAnswer: {custom_answer.answer}\n\n"

                # Add custom answer to the beginning of the context
                combined_context = [custom_context] + pinecone_context

                logger.info(f"Using custom answer for query: {query[:50]}...")
                return combined_context
            else:
                logger.info(f"No custom answer found for query: {query[:50]}...")
                return pinecone_context

        # Create the RAG chain
        rag_chain = (
            RunnableMap(
                {
                    "question": lambda x: x["question"],
                    "session_id": lambda x: x["session_id"],
                    "language": lambda x: x.get(
                        "language", "English"
                    ),  # Include language parameter
                }
            )
            .pipe(
                RunnableMap(
                    {
                        "context": get_context,
                        "question": lambda x: x["question"],
                        "session_id": lambda x: x["session_id"],
                        "language": lambda x: x.get(
                            "language", "English"
                        ),  # Include language parameter
                    }
                )
            )
            .pipe(add_history)
            .pipe(prompt)
            .pipe(model)
            .pipe(StrOutputParser())
        )

        return rag_chain

    except Exception as exc:
        # Log the error and return None to use fallback
        logger.error(f"Error initializing RAG chain: {exc}")
        # If there's an error initializing the RAG chain, return None to use fallback
        return None
