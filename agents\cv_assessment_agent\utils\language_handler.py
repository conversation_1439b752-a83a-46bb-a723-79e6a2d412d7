"""
Language Handler Module

Handles language detection and response formatting for the CV Assessment Agent.
Extracted from the original agent to provide clean separation of concerns.
"""

import re
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger("language_handler")


class LanguageHandler:
    """Handles language detection and response formatting for assessments."""

    @staticmethod
    def detect_language(text: str) -> str:
        """
        Detect if text contains Arabic characters.

        Args:
            text: Text to analyze

        Returns:
            'arabic' if Arabic characters detected, 'english' otherwise
        """
        if not text:
            return "english"

        # Check for Arabic characters (Unicode range)
        arabic_pattern = (
            r"[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]"
        )
        if re.search(arabic_pattern, text):
            return "arabic"

        # Check for common Arabic phrases that might indicate language preference
        arabic_indicators = [
            "لا أفهم",
            "اتحدث العربية فقط",
            "أريد",
            "أعمل",
            "لدي",
            "عندي",
        ]

        for indicator in arabic_indicators:
            if indicator in text:
                return "arabic"

        return "english"

    @staticmethod
    def should_switch_language(current_language: str, user_response: str) -> str:
        """
        Determine if language should switch based on user response.

        Args:
            current_language: Current assessment language
            user_response: Latest user response

        Returns:
            Language to use for next response ('english' or 'arabic')
        """
        detected_language = LanguageHandler.detect_language(user_response)

        # Switch if user responds in different language
        if detected_language != current_language:
            logger.info(
                f"Switching language from {current_language} to {detected_language}"
            )
            return detected_language

        return current_language

    @staticmethod
    def format_question_with_count(
        question: str, current_num: int, total_num: int, language: str = "english"
    ) -> str:
        """
        Format question with count indicator in appropriate language.

        Args:
            question: The question text
            current_num: Current question number
            total_num: Total number of questions
            language: Language to use for formatting

        Returns:
            Formatted question with count
        """
        if language == "arabic":
            count_text = f"(السؤال {current_num} من {total_num})"
        else:
            count_text = f"(Question {current_num} out of {total_num})"

        return f"{question}\n\n{count_text}"

    @staticmethod
    def get_language_specific_prompt_additions(language: str) -> Dict[str, str]:
        """
        Get language-specific additions for AI prompts.

        Args:
            language: Target language ('english' or 'arabic')

        Returns:
            Dictionary with language-specific prompt components
        """
        if language == "arabic":
            return {
                "language_instruction": """
                IMPORTANT LANGUAGE RULE: Respond in Arabic since the candidate is using Arabic.
                - Ask all questions in Arabic
                - Provide all feedback in Arabic
                - Use proper Arabic grammar and formal tone
                """,
                "response_format": "اكتب إجابتك باللغة العربية",
                "error_message": "عذراً، حدث خطأ في التقييم. يرجى المحاولة مرة أخرى.",
            }
        else:
            return {
                "language_instruction": """
                IMPORTANT LANGUAGE RULE: Respond in English since the candidate is using English.
                - Ask all questions in English
                - Provide all feedback in English
                - Use professional but friendly tone
                """,
                "response_format": "Please provide your answer in English",
                "error_message": "I apologize, but I encountered an error during the assessment. Please try again.",
            }

    @staticmethod
    def extract_language_from_qa_history(qa_history: list) -> str:
        """
        Extract the current language from Q&A history.

        Args:
            qa_history: List of Q&A dictionaries

        Returns:
            Current language based on most recent answer
        """
        if not qa_history:
            return "english"  # Default to English

        # Check most recent answer
        for qa in reversed(qa_history):
            answer = qa.get("answer", "")
            if answer and answer.strip():
                return LanguageHandler.detect_language(answer)

        return "english"  # Default if no answers found

    @staticmethod
    def validate_language_consistency(questions: list, target_language: str) -> bool:
        """
        Validate that generated questions are in the target language.

        Args:
            questions: List of question strings
            target_language: Expected language

        Returns:
            True if questions match target language
        """
        for question in questions:
            detected = LanguageHandler.detect_language(question)
            if detected != target_language:
                logger.warning(
                    f"Language mismatch: expected {target_language}, detected {detected} in question: {question[:50]}..."
                )
                return False

        return True
