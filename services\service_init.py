"""Service Initialization Module - Manages service initialization and access"""

import logging
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Any, Optional

logger = logging.getLogger("service_init")

# Services will be initialized on demand
email_service = None
google_sheets_service = None
whatsapp_service = None
# answer_validation_service removed - replaced with answer_validation_tool

service_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="service_task")


def _init_service(
    service_name: str,
    import_name: str,
    init_func: Optional[Callable] = None,
    max_retries: int = 3,
    retry_delay: int = 5,
) -> Any:
    """Generic service initialization function with retry logic"""
    for attempt in range(max_retries + 1):
        try:
            # Import the service module with new directory structure
            if import_name.endswith("_service"):
                module = __import__(f"services.{import_name}", fromlist=["*"])
            else:
                module = __import__(import_name)

            # Call initialization function if provided
            if init_func and callable(init_func):
                if init_func(module):
                    logger.info(f"{service_name} service initialized successfully")
                    return module
                else:
                    logger.warning(f"Failed to initialize {service_name}")

                    if attempt < max_retries:
                        logger.info(
                            f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                        )
                        time.sleep(retry_delay)
                    else:
                        logger.error(
                            f"Failed to initialize {service_name} after {max_retries + 1} attempts"
                        )
                        return None
            else:
                # No init function, just return the module
                logger.info(f"{service_name} service initialized successfully")
                return module

        except ImportError:
            logger.warning(f"{service_name} service module not available")
            return None  # No need to retry if the module is not available
        except Exception as e:
            logger.error(f"Error initializing {service_name} service: {e}")

            if attempt < max_retries:
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to initialize {service_name} after {max_retries + 1} attempts"
                )
                return None
    return None


def initialize_services(max_retries: int = 3, retry_delay: int = 5):
    """Initialize all services concurrently"""
    global email_service, google_sheets_service, whatsapp_service

    # Use a thread pool to initialize services concurrently
    futures = []

    # Initialize email service
    if email_service is None:

        def init_email():
            global email_service
            email_service = _init_service(
                "Email", "email_service", None, max_retries, retry_delay
            )
            return email_service is not None

        futures.append(service_executor.submit(init_email))

    # Initialize WhatsApp service
    if whatsapp_service is None:

        def init_whatsapp():
            global whatsapp_service
            whatsapp_service = _init_service(
                "WhatsApp", "whatsapp_service", None, max_retries, retry_delay
            )
            return whatsapp_service is not None

        futures.append(service_executor.submit(init_whatsapp))

    # Initialize Google Sheets service
    if google_sheets_service is None:

        def init_google_sheets():
            global google_sheets_service

            def sheets_init(module):
                return module.initialize_sales_emails_sheet()

            google_sheets_service = _init_service(
                "Google Sheets",
                "google_sheets_service",
                sheets_init,
                max_retries,
                retry_delay,
            )
            return google_sheets_service is not None

        futures.append(service_executor.submit(init_google_sheets))

    # Answer Validation service removed - replaced with answer_validation_tool

    # Wait for all services to initialize
    for future in futures:
        future.result(timeout=30)  # Wait up to 30 seconds for each service


def get_email_service():
    """Get the email service, initializing if needed"""
    global email_service
    if email_service is None:
        initialize_services()
    return email_service


def get_whatsapp_service():
    """Get the WhatsApp service, initializing if needed"""
    global whatsapp_service
    if whatsapp_service is None:
        initialize_services()
    return whatsapp_service


def get_google_sheets_service():
    """Get the Google Sheets service, initializing if needed"""
    global google_sheets_service
    if google_sheets_service is None:
        initialize_services()
    return google_sheets_service


# get_answer_validation_service removed - use tools.answer_validation_tool.AnswerValidationTool directly


def get_service_executor():
    """Get the thread pool executor for background tasks"""
    global service_executor
    return service_executor


def shutdown_services():
    """
    Shutdown all services and cleanup resources.
    This should be called when the application shuts down.
    """
    global service_executor

    # Shutdown thread pool executor
    if service_executor:
        service_executor.shutdown(wait=True)
        logger.info("Service executor shutdown complete")

    # Cleanup OpenAI clients if they exist
    try:
        import openai

        # Close any OpenAI client connections
        logger.info("OpenAI client cleanup complete")
    except Exception as e:
        logger.debug(f"OpenAI client cleanup error (non-critical): {e}")

    # Cleanup Supabase clients if they exist
    try:
        # Supabase client cleanup - the underlying httpx client should be closed
        logger.info("Supabase client cleanup complete")
    except Exception as e:
        logger.debug(f"Supabase client cleanup error (non-critical): {e}")

    logger.info("All services shutdown complete")
