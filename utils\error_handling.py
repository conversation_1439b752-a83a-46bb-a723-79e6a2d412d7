"""Error Handling Module - Provides common error handling functionality"""

import logging
import time
from typing import TypeVar, Callable, Optional

# Configure logging
logger = logging.getLogger("error_handling")

# Type variable for generic return type
T = TypeVar("T")


def with_exponential_backoff(
    func: Callable[..., T],
    max_retries: int = 3,
    retry_delay: int = 2,
    fallback_value: Optional[T] = None,
    error_prefix: str = "Operation",
    logger_instance: Optional[logging.Logger] = None,
) -> T:
    """
    Execute a function with exponential backoff retry logic.

    Args:
        func: The function to execute
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Initial delay between retries in seconds (default: 2)
        fallback_value: Value to return if all retries fail (default: None)
        error_prefix: Prefix for error messages (default: "Operation")
        logger_instance: Logger instance to use (default: error_handling logger)

    Returns:
        The result of the function or fallback_value if all retries fail
    """
    log = logger_instance or logger

    # Implement exponential backoff for retries
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as e:
            # Check if this is a rate limit or quota error
            error_str = str(e).lower()
            is_rate_limit = any(
                term in error_str
                for term in [
                    "rate limit",
                    "quota",
                    "capacity",
                    "too many requests",
                    "429",
                ]
            )

            # Log the error
            if is_rate_limit:
                log.warning(
                    f"OpenAI API rate limit error in {error_prefix} (attempt {attempt+1}/{max_retries+1}): {str(e)}"
                )
            else:
                log.error(
                    f"Error in {error_prefix} (attempt {attempt+1}/{max_retries+1}): {str(e)}"
                )

            # If this is the last attempt, return the fallback value
            if attempt >= max_retries:
                log.error(
                    f"{error_prefix} failed after {max_retries+1} attempts. Returning fallback value."
                )
                return fallback_value

            # Calculate exponential backoff delay (2^attempt * retry_delay)
            backoff_delay = retry_delay * (2**attempt)
            log.info(f"Retrying {error_prefix.lower()} in {backoff_delay} seconds...")

            # Wait before retrying
            time.sleep(backoff_delay)

    # This should never be reached, but just in case
    return fallback_value
