<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV Assessment Details</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f4f7f6;
            color: #333;
            margin: 0;
            padding: 2em;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background-color: #fff;
            padding: 2em;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 1em;
        }
        .details-grid {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 1em;
            margin-bottom: 2em;
        }
        .details-grid strong {
            font-weight: 600;
            color: #34495e;
        }
        .section-title {
            grid-column: 1 / -1;
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5em;
            margin-top: 1.5em;
        }
        pre {
            background-color: #ecf0f1;
            padding: 1em;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', Courier, monospace;
        }
        .back-link {
            display: inline-block;
            margin-top: 2em;
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CV Assessment Details</h1>
        
        <div class="details-grid">
            <strong>Candidate Name:</strong> <span>{{ assessment.name or 'N/A' }}</span>
            <strong>Email Address:</strong> <span>{{ assessment.email or 'N/A' }}</span>
            <strong>Date Submitted:</strong> <span id="submission-date">{{ assessment.created_at }}</span>
            <strong>Final Score:</strong> <span>{{ assessment.score if assessment.score is not none else 'Not Scored' }}</span>
        </div>

        <h2 class="section-title">Questions & Answers</h2>
        {% if assessment_qa and assessment_qa|length > 0 %}
            <div style="margin-bottom: 2em;">
                {% for qa in assessment_qa %}
                    <div style="margin-bottom: 1.2em; padding: 1em; background: #f8fafd; border-radius: 6px;">
                        <div style="font-weight: 600; color: #2c3e50;">Q{{ loop.index }}: {{ qa.question }}</div>
                        <div style="margin-top: 0.5em; color: #34495e;">A: {{ qa.answer }}</div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div style="margin-bottom: 2em; color: #888;">No questions and answers available for this assessment.</div>
        {% endif %}

        <h2 class="section-title">Assessment Summary</h2>
        <pre>{{ assessment.assessment_description if assessment.assessment_description else 'No description available.' }}</pre>

        <h2 class="section-title">Extracted CV Text</h2>
        <pre>{{ assessment.pdf_text if assessment.pdf_text else 'No text extracted from CV.' }}</pre>

        <a href="/cv-assessments/" class="back-link">&larr; Back to Assessments List</a>
    </div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dateElement = document.getElementById('submission-date');
        if (dateElement && dateElement.textContent.trim()) {
            const date = new Date(dateElement.textContent.trim());
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            dateElement.textContent = date.toLocaleDateString('en-US', options);
        }
    });
</script>
</body>
</html>
