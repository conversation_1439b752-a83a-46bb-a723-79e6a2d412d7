"""
WhatsApp Service Module

This module provides functionality for sending WhatsApp messages
to new leads using Twilio API.
"""

import os
import time
import logging
from dotenv import load_dotenv
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

# Import shared models to avoid circular imports
from database.models import LeadInfo
from utils.background_tasks import run_in_background

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("whatsapp_service")

# Load environment variables
load_dotenv()

# Get Twilio configuration from environment variables
TWILIO_ACCOUNT_SID = os.environ.get("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.environ.get("TWILIO_AUTH_TOKEN")
TWILIO_WHATSAPP_NUMBER = os.environ.get("TWILIO_WHATSAPP_NUMBER")
HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
# Get Arabic company name from environment variables or use a default
HOST_DOMAIN_ARABIC = os.environ.get("HOST_DOMAIN_ARABIC")


def format_whatsapp_message(lead_info: LeadInfo) -> str:
    """
    Format lead information as a WhatsApp message.

    Args:
        lead_info: The lead information to format

    Returns:
        Formatted WhatsApp message
    """
    # Create a personalized message
    message = f"Hello {lead_info.name}, thank you for your interest in our services at {HOST_DOMAIN}. "
    message += "We've received your information and a member of our team will contact you soon. "
    message += (
        "If you have any immediate questions, feel free to reply to this message."
    )

    return message


def format_whatsapp_message_arabic(lead_info: LeadInfo) -> str:
    """
    Format lead information as a WhatsApp message in Arabic.

    Args:
        lead_info: The lead information to format

    Returns:
        Formatted WhatsApp message in Arabic with RTL markers
    """
    # Create a personalized message in Arabic with RTL markers
    # The \u200F character is the Right-to-Left Mark (RLM) which helps ensure proper RTL rendering
    message = f"\u200f مرحبًا {lead_info.name}، شكرًا لاهتمامك بخدماتنا في {HOST_DOMAIN_ARABIC}. "
    message += "\u200f لقد تلقينا معلوماتك وسيتواصل معك أحد أعضاء فريقنا قريبًا. "
    message += "\u200f إذا كانت لديك أي أسئلة فورية، فلا تتردد في الرد على هذه الرسالة."

    return message


@run_in_background
def send_whatsapp_message(
    lead_info: LeadInfo,
    language: str = "English",
    max_retries: int = 3,
    retry_delay: int = 5,
) -> bool:
    """
    Send a WhatsApp message to a new lead using Twilio.
    This function runs in a background thread and includes retry logic for transient failures.

    Args:
        lead_info: The lead information to send
        language: The language to use for the message (default: English)
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        True if the message was sent successfully, False otherwise
    """
    # Check if Twilio configuration is complete
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_WHATSAPP_NUMBER]):
        logger.info(
            "Twilio configuration is not set. WhatsApp message will not be sent."
        )
        return True  # Return True to indicate "success" (no error) even though message wasn't sent

    # Check if the lead has a phone number
    if not lead_info.phone:
        logger.warning(
            f"Cannot send WhatsApp message to lead {lead_info.name}: No phone number provided"
        )
        return False

    # The phone number should already be properly formatted by the lead_capture module
    # But let's verify it has the + prefix required for WhatsApp
    to_phone = lead_info.phone

    # Log the phone number for debugging
    logger.info(f"Using phone number for WhatsApp: {to_phone}")

    # Format the message based on language
    if language.lower() == "arabic":
        message_body = format_whatsapp_message_arabic(lead_info)
    else:
        message_body = format_whatsapp_message(lead_info)

    # Initialize Twilio client
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

    # Implement retry logic
    retries = 0
    while retries <= max_retries:
        try:
            # Send the WhatsApp message
            message = client.messages.create(
                from_=f"whatsapp:{TWILIO_WHATSAPP_NUMBER}",
                body=message_body,
                to=f"whatsapp:{to_phone}",
            )

            # Log success
            logger.info(
                f"WhatsApp message sent successfully to {to_phone} for lead: {lead_info.name} (SID: {message.sid})"
            )
            return True

        except TwilioRestException as e:
            # Check if this is a rate limit or quota error
            error_str = str(e).lower()
            is_rate_limit = any(
                term in error_str
                for term in [
                    "rate limit",
                    "quota",
                    "capacity",
                    "too many requests",
                    "429",
                ]
            )

            # Log the error
            if is_rate_limit:
                logger.warning(
                    f"Twilio API rate limit error (attempt {retries+1}/{max_retries+1}): {str(e)}"
                )
            else:
                logger.warning(
                    f"Failed to send WhatsApp message to {to_phone} (attempt {retries+1}/{max_retries+1}). Error: {str(e)}"
                )

            # Increment retry counter
            retries += 1

            # If we've reached max retries, give up
            if retries > max_retries:
                logger.error(
                    f"Failed to send WhatsApp message to {to_phone} after {max_retries+1} attempts. Final error: {str(e)}"
                )
                return False

            # Calculate exponential backoff delay (2^attempt * retry_delay)
            backoff_delay = retry_delay * (2 ** (retries - 1))
            logger.info(f"Retrying WhatsApp message in {backoff_delay} seconds...")

            # Wait before retrying
            time.sleep(backoff_delay)

        except Exception as e:
            # Log unexpected errors
            logger.error(
                f"Unexpected error sending WhatsApp message to {to_phone}: {str(e)}"
            )
            return False

    # This should not be reached, but just in case
    return False
