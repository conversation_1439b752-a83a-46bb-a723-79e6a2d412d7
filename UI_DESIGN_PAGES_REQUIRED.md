# LEADERSSCOUT AI - PAGES TO DESIGN

## 1. CV Assessments List Page

Display all candidate assessments in a table format with candidate name, email, submission date, and score. Include sorting options and view details buttons.

## 2. CV Assessment Detail Page

Show detailed view of a single candidate assessment including candidate basic info, questions and answers section, assessment description, and extracted CV text. Should display score breakdown and tag analysis.

## 3. AI Chat Interface

Handle candidate interaction during assessment with chat interface, file upload for CV, and question/answer flow.

## 4. Login Page

User authentication page for recruiters with email/password form, login button, and forgot password link.

## 5. Main Dashboard

Overview page after login with summary cards showing total assessments and pending reviews, recent activity list, and quick action buttons to start new assessments.

## 6. Templates List Page

Manage scoring templates with list of available templates showing name, description, and weights. Include edit/create/delete actions.

## 7. Template Create/Edit Page

Configure scoring templates with template name and description, weight sliders for scoring components, AI/Human ratio settings, and category coverage thresholds.

## 8. Jobs List Page

Manage job positions with job listings showing title, department, and status. Include create new job button and edit/view actions.

## 9. Job Create/Edit Page

Create and configure job positions with job title, description, requirements, template assignment, and assessment settings.

## 10. Assessment Creation Page

Start new candidate assessment with job selection dropdown, template selection, CV upload area, and assessment configuration options.
