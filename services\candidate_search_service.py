"""
Candidate Search Service Module

Handles semantic candidate search functionality for recruiters.
Combines tag-based filtering with semantic vector search for optimal candidate matching.
"""

import logging
import json
import os
from typing import List, Dict, Any, Optional, Tuple, Union
from decimal import Decimal
from datetime import datetime
from uuid import UUID

from langchain_openai import OpenAIEmbeddings
from database.models import (
    CVAssessment,
    CandidateTag,
    CandidateTagCoverage,
    TagCategory,
    TagDefinition,
    JobLevel,
    CandidateScore,
    Job,
)
from database.database_service_wrapper import DatabaseService
from services.candidate_vector_service import get_candidate_vector_service
from services.candidate_profile_service import CandidateProfileService

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("candidate_search_service")


class CandidateSearchService:
    """Service for semantic candidate search with hybrid filtering"""

    def __init__(self, db_service):
        """Initialize the candidate search service"""
        self.db_service = db_service
        self.embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
        self.vector_service = get_candidate_vector_service()
        self.profile_service = CandidateProfileService(db_service)

    async def search_candidates(
        self,
        query_text: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 20,
        similarity_threshold: float = 0.6,
        hybrid_mode: bool = True,
        tag_weight: float = 0.4,
        semantic_weight: float = 0.6,
    ) -> Dict[str, Any]:
        """
        Search for candidates using hybrid approach (tags + semantic)

        Args:
            query_text: Job description or search query
            filters: Metadata filters for candidate selection
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score for semantic search
            hybrid_mode: Whether to use hybrid scoring (tag + semantic)
            tag_weight: Weight for tag-based matching (0-1)
            semantic_weight: Weight for semantic matching (0-1)

        Returns:
            Dictionary containing search results and metadata
        """
        try:
            logger.info(f"Searching candidates with query: {query_text[:100]}...")

            # Validate weights
            if hybrid_mode and abs(tag_weight + semantic_weight - 1.0) > 0.01:
                raise ValueError("Tag weight and semantic weight must sum to 1.0")

            # Step 1: Semantic search using vector similarity
            semantic_results = await self._semantic_search(
                query_text,
                filters,
                top_k * 2,
                similarity_threshold,  # Get more for filtering
            )

            if not hybrid_mode:
                # Return semantic results only
                return {
                    "success": True,
                    "total_results": len(semantic_results),
                    "search_type": "semantic_only",
                    "candidates": semantic_results[:top_k],
                    "query": query_text,
                    "filters": filters,
                    "search_metadata": {
                        "similarity_threshold": similarity_threshold,
                        "semantic_results_count": len(semantic_results),
                    },
                }

            # Step 2: Tag-based filtering and scoring
            tag_filtered_results = await self._apply_tag_based_filtering(
                semantic_results, filters
            )

            # Step 3: Hybrid scoring (combine semantic + tag scores)
            hybrid_results = await self._calculate_hybrid_scores(
                tag_filtered_results, tag_weight, semantic_weight
            )

            # Step 4: Sort by hybrid score and limit results
            final_results = sorted(
                hybrid_results, key=lambda x: x["hybrid_score"], reverse=True
            )[:top_k]

            # Step 5: Enhance results with additional candidate information
            enhanced_results = await self._enhance_search_results(final_results)

            return {
                "success": True,
                "total_results": len(enhanced_results),
                "search_type": "hybrid",
                "candidates": enhanced_results,
                "query": query_text,
                "filters": filters,
                "search_metadata": {
                    "similarity_threshold": similarity_threshold,
                    "semantic_results_count": len(semantic_results),
                    "tag_filtered_count": len(tag_filtered_results),
                    "hybrid_results_count": len(hybrid_results),
                    "tag_weight": tag_weight,
                    "semantic_weight": semantic_weight,
                },
            }

        except Exception as e:
            logger.error(f"Error in candidate search: {str(e)}")
            return {
                "success": False,
                "error_message": str(e),
                "total_results": 0,
                "candidates": [],
                "query": query_text,
                "filters": filters,
            }

    async def _semantic_search(
        self,
        query_text: str,
        filters: Optional[Dict[str, Any]],
        top_k: int,
        similarity_threshold: float,
    ) -> List[Dict[str, Any]]:
        """Perform semantic search using vector similarity"""
        try:
            # Use the vector service for semantic search
            vector_results = await self.vector_service.search_candidates(
                query_text, filters, top_k, "active", similarity_threshold
            )

            # Convert to standardized format
            semantic_results = []
            for result in vector_results:
                candidate_data = {
                    "candidate_id": result["candidate_id"],
                    "semantic_score": float(result["similarity_score"]),
                    "metadata": result["metadata"],
                    # Extract key information from metadata
                    "candidate_name": result["metadata"].get(
                        "candidate_name", "Unknown"
                    ),
                    "experience_level": result["metadata"].get(
                        "experience_level", "unknown"
                    ),
                    "current_role": result["metadata"].get("current_role"),
                    "programming_languages": result["metadata"].get(
                        "programming_languages", []
                    ),
                    "total_years_experience": result["metadata"].get(
                        "total_years_experience"
                    ),
                    "cv_assessment_id": result["metadata"].get("cv_assessment_id"),
                }
                semantic_results.append(candidate_data)

            logger.info(f"Semantic search returned {len(semantic_results)} candidates")
            return semantic_results

        except Exception as e:
            logger.error(f"Error in semantic search: {str(e)}")
            return []

    async def _apply_tag_based_filtering(
        self, candidates: List[Dict[str, Any]], filters: Optional[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Apply tag-based filtering and calculate tag match scores"""
        if not filters:
            # No tag filtering, just add default tag scores
            for candidate in candidates:
                candidate["tag_score"] = 0.5  # Neutral score when no tag filtering
            return candidates

        filtered_candidates = []

        for candidate in candidates:
            try:
                # Get candidate's CV assessment ID
                cv_assessment_id = candidate.get("cv_assessment_id")
                if not cv_assessment_id:
                    continue

                # Get candidate tags from database
                candidate_tags_data = self.db_service.get_candidate_tags(
                    str(cv_assessment_id)
                )
                candidate_tags = [
                    CandidateTag(**tag_data) for tag_data in candidate_tags_data
                ]

                # Calculate tag match score
                tag_score = self._calculate_tag_match_score(candidate_tags, filters)
                candidate["tag_score"] = tag_score
                candidate["candidate_tags"] = candidate_tags

                # Apply hard filters (if any)
                if self._passes_hard_filters(candidate, filters):
                    filtered_candidates.append(candidate)

            except Exception as e:
                logger.warning(
                    f"Error processing candidate {candidate.get('candidate_id', 'unknown')}: {str(e)}"
                )
                continue

        logger.info(f"Tag filtering returned {len(filtered_candidates)} candidates")
        return filtered_candidates

    def _calculate_tag_match_score(
        self, candidate_tags: List[CandidateTag], filters: Dict[str, Any]
    ) -> float:
        """Calculate how well candidate tags match the search filters"""
        if not filters or not candidate_tags:
            return 0.5  # Neutral score

        total_score = 0.0
        total_weight = 0.0

        # Create tag lookup for efficient matching
        tag_values = {}
        for tag in candidate_tags:
            if tag.tag_definition:
                category = (
                    tag.tag_definition.category.name
                    if tag.tag_definition.category
                    else "unknown"
                )
                tag_key = tag.tag_definition.tag_key
                tag_values[f"{category}.{tag_key}"] = tag.value.lower()

        # Check each filter criterion
        for filter_key, filter_value in filters.items():
            weight = 1.0  # Default weight for each filter

            if filter_key == "required_skills":
                score = self._match_skills(tag_values, filter_value)
                total_score += score * weight
                total_weight += weight

            elif filter_key == "experience_level":
                score = self._match_experience_level(tag_values, filter_value)
                total_score += score * weight
                total_weight += weight

            elif filter_key == "min_years_experience":
                score = self._match_min_experience(tag_values, filter_value)
                total_score += score * weight
                total_weight += weight

            elif filter_key == "education_level":
                score = self._match_education_level(tag_values, filter_value)
                total_score += score * weight
                total_weight += weight

            elif filter_key == "location":
                score = self._match_location(tag_values, filter_value)
                total_score += score * weight
                total_weight += weight

        return total_score / total_weight if total_weight > 0 else 0.5

    def _match_skills(
        self, tag_values: Dict[str, str], required_skills: List[str]
    ) -> float:
        """Match required skills against candidate's skills"""
        if not required_skills:
            return 1.0

        # Look for skills in various tag categories
        candidate_skills = []
        for key, value in tag_values.items():
            if (
                "skill" in key.lower()
                or "programming" in key.lower()
                or "technology" in key.lower()
            ):
                candidate_skills.extend(
                    [skill.strip().lower() for skill in value.split(",")]
                )

        if not candidate_skills:
            return 0.0

        # Calculate match percentage
        matched_skills = 0
        for required_skill in required_skills:
            if any(required_skill.lower() in skill for skill in candidate_skills):
                matched_skills += 1

        return matched_skills / len(required_skills)

    def _match_experience_level(
        self, tag_values: Dict[str, str], required_level: str
    ) -> float:
        """Match experience level"""
        experience_mapping = {
            "entry": 1,
            "junior": 2,
            "mid": 3,
            "senior": 4,
            "executive": 5,
        }

        required_level_num = experience_mapping.get(required_level.lower(), 3)

        # Look for experience level in tags
        for key, value in tag_values.items():
            if "experience" in key.lower() and "level" in key.lower():
                candidate_level_num = experience_mapping.get(value.lower(), 3)
                # Score based on how close the levels are
                diff = abs(candidate_level_num - required_level_num)
                return max(0.0, 1.0 - (diff * 0.25))  # 25% penalty per level difference

        return 0.5  # Neutral if no experience level found

    def _match_min_experience(
        self, tag_values: Dict[str, str], min_years: int
    ) -> float:
        """Match minimum years of experience"""
        # Look for years of experience in tags
        for key, value in tag_values.items():
            if "years" in key.lower() and "experience" in key.lower():
                try:
                    candidate_years = int(value)
                    if candidate_years >= min_years:
                        return 1.0
                    else:
                        # Partial score based on how close they are
                        return max(0.0, candidate_years / min_years)
                except ValueError:
                    continue

        return 0.5  # Neutral if no years found

    def _match_education_level(
        self, tag_values: Dict[str, str], required_education: str
    ) -> float:
        """Match education level"""
        education_mapping = {
            "high school": 1,
            "associate": 2,
            "bachelor": 3,
            "master": 4,
            "phd": 5,
            "doctorate": 5,
        }

        required_edu_num = education_mapping.get(required_education.lower(), 3)

        # Look for education level in tags
        for key, value in tag_values.items():
            if "degree" in key.lower() or "education" in key.lower():
                for edu_level, edu_num in education_mapping.items():
                    if edu_level in value.lower():
                        if edu_num >= required_edu_num:
                            return 1.0
                        else:
                            return max(0.0, edu_num / required_edu_num)

        return 0.5  # Neutral if no education found

    def _match_location(
        self, tag_values: Dict[str, str], required_location: str
    ) -> float:
        """Match location preferences"""
        # Look for location in tags
        for key, value in tag_values.items():
            if "location" in key.lower() or "city" in key.lower():
                if required_location.lower() in value.lower():
                    return 1.0

        return 0.3  # Lower score if location not found (might be flexible)

    def _passes_hard_filters(
        self, candidate: Dict[str, Any], filters: Dict[str, Any]
    ) -> bool:
        """Check if candidate passes hard filter requirements"""
        # Implement hard filters that completely exclude candidates
        # For now, we'll be inclusive and let scoring handle the filtering
        return True

    async def _calculate_hybrid_scores(
        self,
        candidates: List[Dict[str, Any]],
        tag_weight: float,
        semantic_weight: float,
    ) -> List[Dict[str, Any]]:
        """Calculate hybrid scores combining tag and semantic scores"""
        for candidate in candidates:
            semantic_score = candidate.get("semantic_score", 0.0)
            tag_score = candidate.get("tag_score", 0.0)

            # Calculate weighted hybrid score
            hybrid_score = (semantic_score * semantic_weight) + (tag_score * tag_weight)
            candidate["hybrid_score"] = hybrid_score

            # Add score breakdown for transparency
            candidate["score_breakdown"] = {
                "semantic_score": semantic_score,
                "tag_score": tag_score,
                "hybrid_score": hybrid_score,
                "semantic_weight": semantic_weight,
                "tag_weight": tag_weight,
            }

        return candidates

    async def _enhance_search_results(
        self, candidates: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Enhance search results with additional candidate information"""
        enhanced_candidates = []

        for candidate in candidates:
            try:
                # Get additional candidate information from database
                cv_assessment_id = candidate.get("cv_assessment_id")
                if cv_assessment_id:
                    # Get CV assessment details
                    cv_assessment_data = self.db_service.get_cv_assessment_by_id(
                        int(cv_assessment_id)
                    )
                    if cv_assessment_data:
                        cv_assessment = CVAssessment(**cv_assessment_data)

                        # Get candidate score if available
                        candidate_score_data = (
                            self.db_service.get_candidate_score_by_assessment_id(
                                str(cv_assessment_id)
                            )
                        )
                        candidate_score = (
                            CandidateScore(**candidate_score_data)
                            if candidate_score_data
                            else None
                        )

                        # Enhance candidate data
                        enhanced_candidate = {
                            **candidate,
                            "email": cv_assessment.email,
                            "assessment_completion": cv_assessment.is_complete,
                            "ai_total_score": (
                                float(cv_assessment.score)
                                if cv_assessment.score
                                else None
                            ),
                            "final_weighted_score": (
                                float(candidate_score.final_weighted_score)
                                if candidate_score
                                and candidate_score.final_weighted_score
                                else None
                            ),
                            "created_at": (
                                cv_assessment.created_at.isoformat()
                                if cv_assessment.created_at
                                else None
                            ),
                            "profile_completeness": candidate["metadata"].get(
                                "profile_completeness_score", 0
                            ),
                        }

                        enhanced_candidates.append(enhanced_candidate)
                    else:
                        enhanced_candidates.append(candidate)
                else:
                    enhanced_candidates.append(candidate)

            except Exception as e:
                logger.warning(
                    f"Error enhancing candidate {candidate.get('candidate_id', 'unknown')}: {str(e)}"
                )
                enhanced_candidates.append(candidate)

        return enhanced_candidates

    async def find_similar_candidates(
        self,
        reference_candidate_id: str,
        top_k: int = 10,
        exclude_reference: bool = True,
        similarity_threshold: float = 0.7,
    ) -> Dict[str, Any]:
        """
        Find candidates similar to a reference candidate

        Args:
            reference_candidate_id: ID of the reference candidate
            top_k: Number of similar candidates to return
            exclude_reference: Whether to exclude the reference candidate from results
            similarity_threshold: Minimum similarity score

        Returns:
            Dictionary containing similar candidates and metadata
        """
        try:
            logger.info(f"Finding candidates similar to: {reference_candidate_id}")

            # Use vector service to find similar candidates
            similar_results = await self.vector_service.find_similar_candidates(
                reference_candidate_id, top_k, "active", exclude_reference
            )

            # Filter by similarity threshold
            filtered_results = [
                result
                for result in similar_results
                if result["similarity_score"] >= similarity_threshold
            ]

            # Enhance results
            enhanced_results = []
            for result in filtered_results:
                enhanced_result = {
                    "candidate_id": result["candidate_id"],
                    "similarity_score": result["similarity_score"],
                    "candidate_name": result["metadata"].get(
                        "candidate_name", "Unknown"
                    ),
                    "experience_level": result["metadata"].get(
                        "experience_level", "unknown"
                    ),
                    "current_role": result["metadata"].get("current_role"),
                    "programming_languages": result["metadata"].get(
                        "programming_languages", []
                    ),
                    "total_years_experience": result["metadata"].get(
                        "total_years_experience"
                    ),
                    "profile_completeness_score": result["metadata"].get(
                        "profile_completeness_score", 0
                    ),
                    "cv_assessment_id": result["metadata"].get("cv_assessment_id"),
                    "metadata": result["metadata"],
                }
                enhanced_results.append(enhanced_result)

            return {
                "success": True,
                "reference_candidate_id": reference_candidate_id,
                "total_results": len(enhanced_results),
                "similar_candidates": enhanced_results,
                "search_metadata": {
                    "similarity_threshold": similarity_threshold,
                    "exclude_reference": exclude_reference,
                    "total_found": len(similar_results),
                    "after_threshold_filter": len(filtered_results),
                },
            }

        except Exception as e:
            logger.error(f"Error finding similar candidates: {str(e)}")
            return {
                "success": False,
                "error_message": str(e),
                "reference_candidate_id": reference_candidate_id,
                "total_results": 0,
                "similar_candidates": [],
            }

    async def advanced_search(
        self,
        job_description: str,
        required_skills: Optional[List[str]] = None,
        experience_level: Optional[str] = None,
        min_years_experience: Optional[int] = None,
        education_level: Optional[str] = None,
        location: Optional[str] = None,
        salary_range: Optional[Dict[str, int]] = None,
        top_k: int = 20,
        similarity_threshold: float = 0.6,
    ) -> Dict[str, Any]:
        """
        Advanced candidate search with structured filters

        Args:
            job_description: Job description for semantic matching
            required_skills: List of required skills
            experience_level: Required experience level
            min_years_experience: Minimum years of experience
            education_level: Required education level
            location: Required location
            salary_range: Salary range with 'min' and 'max' keys
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score

        Returns:
            Dictionary containing search results
        """
        try:
            # Build filters dictionary
            filters = {}
            if required_skills:
                filters["required_skills"] = required_skills
            if experience_level:
                filters["experience_level"] = experience_level
            if min_years_experience:
                filters["min_years_experience"] = min_years_experience
            if education_level:
                filters["education_level"] = education_level
            if location:
                filters["location"] = location
            if salary_range:
                filters["salary_range"] = salary_range

            # Perform hybrid search
            results = await self.search_candidates(
                query_text=job_description,
                filters=filters,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                hybrid_mode=True,
                tag_weight=0.4,
                semantic_weight=0.6,
            )

            # Add search criteria to results
            results["search_criteria"] = {
                "job_description": job_description,
                "required_skills": required_skills,
                "experience_level": experience_level,
                "min_years_experience": min_years_experience,
                "education_level": education_level,
                "location": location,
                "salary_range": salary_range,
            }

            return results

        except Exception as e:
            logger.error(f"Error in advanced search: {str(e)}")
            return {
                "success": False,
                "error_message": str(e),
                "total_results": 0,
                "candidates": [],
                "search_criteria": {
                    "job_description": job_description,
                    "required_skills": required_skills,
                    "experience_level": experience_level,
                    "min_years_experience": min_years_experience,
                    "education_level": education_level,
                    "location": location,
                    "salary_range": salary_range,
                },
            }

    def get_search_statistics(self) -> Dict[str, Any]:
        """Get statistics about the candidate search system"""
        try:
            # Get vector index statistics
            vector_stats = self.vector_service.get_index_stats("active")

            # Get database statistics
            total_assessments = len(self.db_service.get_all_cv_assessments())
            completed_assessments = len(
                [
                    assessment
                    for assessment in self.db_service.get_all_cv_assessments()
                    if assessment.get("is_complete", False)
                ]
            )

            return {
                "vector_index_stats": vector_stats,
                "total_cv_assessments": total_assessments,
                "completed_assessments": completed_assessments,
                "completion_rate": (
                    completed_assessments / total_assessments
                    if total_assessments > 0
                    else 0
                ),
                "searchable_candidates": vector_stats.get("namespace_vectors", 0),
            }

        except Exception as e:
            logger.error(f"Error getting search statistics: {str(e)}")
            return {
                "error": str(e),
                "vector_index_stats": {},
                "total_cv_assessments": 0,
                "completed_assessments": 0,
                "completion_rate": 0,
                "searchable_candidates": 0,
            }


# Initialize service function
def get_candidate_search_service(db_service) -> CandidateSearchService:
    """Get initialized candidate search service"""
    return CandidateSearchService(db_service)


# Test function for development
async def test_candidate_search_service():
    """Test function for the candidate search service"""
    from database.database_service_wrapper import DatabaseService

    db_service = DatabaseService()
    search_service = get_candidate_search_service(db_service)

    # Test basic search
    results = await search_service.search_candidates(
        query_text="Senior Python developer with machine learning experience",
        top_k=5,
        similarity_threshold=0.6,
    )

    print(f"✅ Basic search returned {results['total_results']} results")

    # Test advanced search
    advanced_results = await search_service.advanced_search(
        job_description="We need a senior software engineer with Python and React experience",
        required_skills=["python", "react"],
        experience_level="senior",
        min_years_experience=5,
        top_k=5,
    )

    print(f"✅ Advanced search returned {advanced_results['total_results']} results")

    # Get statistics
    stats = search_service.get_search_statistics()
    print(f"✅ Search statistics: {stats}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_candidate_search_service())
