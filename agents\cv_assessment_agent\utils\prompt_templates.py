"""
Prompt Templates <PERSON><PERSON><PERSON>

Contains all AI prompts used by the CV Assessment Agent.
Extracted from the original agent to provide clean separation and easier maintenance.
"""

from typing import Dict, Any, Optional


class PromptTemplates:
    """Container for all AI prompts used in CV assessment."""

    @staticmethod
    def get_cv_analysis_prompt(cv_text: str) -> str:
        """Get the prompt for CV analysis and contact information extraction."""
        return f"""
Extract the candidate's full name and primary email address from the following CV text. If the name contains obvious spelling or capitalization mistakes fix it. Return ONLY a JSON object in the form:
{{"name": "<full name or null>", "email": "<email or null>"}}
If you cannot find a value, return null for that field.
CV:
{cv_text}
"""

    @staticmethod
    def get_experience_level_prompt(cv_text: str) -> str:
        """Get the prompt for extracting experience level from CV."""
        return f"""
Analyze the following CV and determine the candidate's experience level based on years of experience, role titles, and responsibilities mentioned.

CV Text:
{cv_text}

Return only one of these experience levels:
- entry: 0-2 years of experience, fresh graduate, intern, junior roles
- mid: 3-5 years of experience, intermediate roles
- senior: 6-10 years of experience, senior roles, lead positions
- executive: 10+ years of experience, director/VP/C-level roles

Return only the experience level word (entry/mid/senior/executive).
"""

    @staticmethod
    def get_question_generation_prompt(
        cv_text: str,
        template_info: Dict[str, Any],
        assessment_length: int,
        language_instruction: str = "",
    ) -> str:
        """DEPRECATED: Use QuestionGenerationService instead."""
        # This method is deprecated - all question generation should use QuestionGenerationService
        raise NotImplementedError(
            "Use QuestionGenerationService._build_question_generation_prompt instead"
        )

    @staticmethod
    def get_followup_questions_prompt(
        cv_text: str,
        qa_str: str,
        template_name: str = "Default",
        language_instruction: str = "",
    ) -> str:
        """DEPRECATED: Use QuestionGenerationService instead."""
        # This method is deprecated - all question generation should use QuestionGenerationService
        raise NotImplementedError(
            "Use QuestionGenerationService for follow-up questions instead"
        )

    @staticmethod
    def get_final_assessment_prompt(
        cv_text: str,
        qa_str: str,
        template_info: Dict[str, Any],
        language_instruction: str = "",
    ) -> str:
        """Get the prompt for generating the final assessment."""
        template_name = template_info.get("template_name", "Default")
        weights = template_info.get("scoring_weights", {})
        ai_percentage = template_info.get("ai_percentage", 0.70)

        return f"""
You are an expert recruiter. Analyze the following CV and the candidate's detailed answers to create a comprehensive assessment.

## Assessment Template: {template_name}

This assessment uses the following scoring weights:
- CV Analysis: {weights.get('cv_analysis', 0.30) * 100:.0f}%
- Questionnaire (Q&A): {weights.get('questionnaire', 0.30) * 100:.0f}%
- Case Study: {weights.get('case_study', 0.20) * 100:.0f}%
- Psychometric: {weights.get('psychometric', 0.10) * 100:.0f}%
- Background Check: {weights.get('background_check', 0.10) * 100:.0f}%

AI Assessment Percentage: {ai_percentage * 100:.0f}%

{language_instruction}

CV:
{cv_text}

Previous Q&A:
{qa_str}

Based on the candidate's field, experience level, and responses, provide a detailed assessment focusing on the CV Analysis and Questionnaire components (since these are what we have data for).

Score: [0-100]

Calculate the score using ONLY the CV Analysis and Questionnaire weights, normalized to 100%. Since we don't have Case Study, Psychometric, or Background Check data yet, focus your scoring on:
- CV Analysis Score: Based on education, experience, skills, and achievements from the CV
- Questionnaire Score: Based on the quality and relevance of the candidate's answers

The final score should reflect the weighted average of these two components, normalized to account for the missing components.

IMPORTANT: You MUST extract the candidate's full name and email from the CV text. Return ONLY a JSON object with this exact structure:
{{
  "name": "<candidate's full name>",
  "email": "<candidate's email address>",
  "score": <integer 0-100 based on the rubric above>,
  "description": "<detailed assessment with all sections above, but DO NOT include any percentage weights in the description>",
  "user_message": "<friendly summary that offers feedback WITHOUT mentioning or hinting at the numeric score>"
}}

*** VERY IMPORTANT: DONT REPEAT QUESTIONS ALWAYS ASK NEW QUESTIONS TO GET NEW INFORMATION***

Keep descriptions concise and focused. Return ONLY the JSON object.
"""

    @staticmethod
    def get_tag_extraction_prompt(
        cv_text: str, job_description: Optional[str] = None
    ) -> str:
        """Get the prompt for tag extraction from CV."""
        job_context = f"\n\nJob Context: {job_description}" if job_description else ""

        return f"""
Extract relevant tags and information from the following CV text. Focus on:

1. Education (degrees, universities, graduation years, GPA, major fields)
2. Experience (years, industries, company sizes, roles, achievements)
3. Skills (technical skills, tools, frameworks, languages)
4. Certifications (professional certifications, licenses)
5. Personal attributes (leadership experience, languages spoken)

CV Text:
{cv_text}{job_context}

Extract specific, factual information that can be used for candidate assessment and matching.
"""
