<!-- Add viewport meta tag for mobile responsiveness -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

<!-- Chat Toggle Button -->
<div id="chat-toggle" style="position: fixed; bottom: 20px; right: 20px; z-index: 9999;">
  <button onclick="toggleChat()" style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); color: #fff; border: none; border-radius: 30px; padding: 14px 20px; font-size: 14px; font-weight: 600; cursor: pointer; box-shadow: 0 8px 25px rgba(65, 105, 225, 0.4); white-space: nowrap; transition: all 0.3s ease; letter-spacing: 0.5px; text-transform: uppercase; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;">hire me</button>
</div>

<!-- Chat Box -->
<div id="chat-box" style="display: none; position: fixed; bottom: 90px; right: 20px; width: 320px; height: 420px; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.2); z-index: 99999; overflow: hidden; font-family: sans-serif;">
  <div style="background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%); color: white; padding: 12px; font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
    <span>Customer Service</span>
    <button onclick="refreshSession()" style="background: none; border: none; color: white; cursor: pointer; font-size: 14px; display: flex; align-items: center; padding: 4px;">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M23 4v6h-6"></path>
        <path d="M1 20v-6h6"></path>
        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
      </svg>
    </button>
  </div>
  <div id="chat-messages" style="height: auto; max-height: calc(100% - 80px); padding: 10px 10px 20px 10px; overflow-y: auto; background: #f9f9f9; display: flex; flex-direction: column; gap: 8px; box-sizing: border-box;"></div>
  <div id="chat-input-container" style="display: flex; padding: 10px 10px 12px 10px; border-top: 1px solid #ddd; position: absolute; bottom: 0; left: 0; right: 0; background: white; z-index: 10; align-items: center; gap: 8px; min-height: 64px; box-sizing: border-box;">
    <label for="cv-upload" style="margin: 0; cursor: pointer; display: flex; align-items: center; background: none; border: none; padding: 0;">
      <input id="cv-upload" type="file" accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,.doc,.docx" style="display: none;" />
      <svg width="28" height="28" fill="none" stroke="#4169e1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="17 8 12 3 7 8"/>
        <line x1="12" y1="3" x2="12" y2="15"/>
      </svg>
    </label>
    <input id="user-input" type="text" placeholder="Type a message..." style="flex: 1; padding: 12px 16px; border-radius: 24px; border: 1px solid #ccc; font-size: 16px; box-shadow: 0 1px 4px rgba(65,105,225,0.08); outline: none; transition: border 0.2s; background: #f9fafd; margin: 0 4px; min-width: 0;" />
    <button onclick="sendMessage()" style="background: none; color: #4169e1; border: none; padding: 0; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; box-shadow: none; cursor: pointer; font-size: 18px; margin: 0; flex-shrink: 0;">
      <svg width="28" height="28" fill="none" stroke="#4169e1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
        <line x1="22" y1="2" x2="11" y2="13"/>
        <polygon points="22 2 15 22 11 13 2 9 22 2"/>
      </svg>
    </button>
  </div>
</div>

<script>
  // Make chat box responsive for mobile devices
  function adjustForMobile() {
    const chatBox = document.getElementById('chat-box');
    const chatToggle = document.getElementById('chat-toggle');
    const chatMessages = document.getElementById('chat-messages');
    const inputContainer = document.getElementById('chat-input-container');

    if (window.innerWidth < 768) {
      // Mobile view - make chat box take up more screen space but keep it above the chat bubble
      chatBox.style.width = '90%';
      chatBox.style.right = '5%';
      chatBox.style.left = 'auto';
      chatBox.style.bottom = '80px';
      chatBox.style.height = '60%';
      chatBox.style.borderRadius = '12px'; // Keep all corners rounded

      // Adjust the messages container height to fill available space
      if (chatMessages) {
        chatMessages.style.height = 'calc(100% - 110px)'; // Adjust based on header and input heights
        chatMessages.style.paddingBottom = '60px'; // Add padding to prevent messages from being hidden behind input
      }

      // Ensure input container is properly positioned
      if (inputContainer) {
        inputContainer.style.borderRadius = '0 0 12px 12px'; // Round bottom corners
        inputContainer.style.boxShadow = '0 -2px 5px rgba(0,0,0,0.1)'; // Add subtle shadow
      }

      // Keep toggle button position
      chatToggle.style.bottom = '15px';
      chatToggle.style.right = '15px';
    } else {
      // Desktop view - reset to default
      chatBox.style.width = '320px';
      chatBox.style.right = '20px';
      chatBox.style.left = 'auto';
      chatBox.style.bottom = '90px';
      chatBox.style.height = '420px';
      chatBox.style.borderRadius = '12px'; // Round all corners

      // Reset messages container height
      if (chatMessages) {
        chatMessages.style.height = '300px';
        chatMessages.style.paddingBottom = '10px'; // Reset padding
      }

      // Reset input container
      if (inputContainer) {
        inputContainer.style.borderRadius = '0 0 12px 12px'; // Round bottom corners
        inputContainer.style.boxShadow = 'none'; // Remove shadow
      }

      // Reset toggle button position
      chatToggle.style.bottom = '20px';
      chatToggle.style.right = '20px';
    }
  }

  // Call on page load and window resize
  window.addEventListener('load', adjustForMobile);
  window.addEventListener('resize', adjustForMobile);
</script>

<script>
  // Generate a new session ID for each page load
  let currentSessionId;
  let lastSentMessage = ""; // Store the last sent message for retry functionality

  // Initialize session when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    // Always generate a new UUID on page load
    currentSessionId = generateUUID();

    // Add event listener for Enter key
    const userInput = document.getElementById('user-input');
    userInput.addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        sendMessage();
        e.preventDefault();
      }
    });

    // Add event listener for input to handle phone numbers with + sign
    userInput.addEventListener('input', function() {
      // Force LTR for the input field to ensure + signs appear correctly
      this.setAttribute('dir', 'ltr');

      // If the input contains Arabic text but no + sign, use RTL
      if (containsArabic(this.value) && !this.value.includes('+')) {
        this.setAttribute('dir', 'rtl');
      }
    });
  });

  // Generate a proper UUID v4
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Function to refresh the session
  function refreshSession() {
    // Generate a new session ID
    currentSessionId = generateUUID();

    // Clear the chat messages
    const chatMessages = document.getElementById('chat-messages');
    chatMessages.innerHTML = '';

    // Add a system message
    appendMessage(`New conversation started. This is Amin from customer service. How can I help you?`, 'bot');

    // Focus on the input field
    document.getElementById('user-input').focus();
  }

  // Initialize chat with welcome message when chat is first opened
  let chatInitialized = false;

  // Welcome message to show when chat is first opened
  const welcomeMessage = 'Welcome! My name is Amin from customer service. I can answer your questions, help you upload your CV for review, or take your contact details if you\'re interested in our services. How can I help you today?';

  function toggleChat() {
    const chatBox = document.getElementById('chat-box');
    const chat = document.getElementById('chat-messages');
    const isHidden = chatBox.style.display === 'none';

    chatBox.style.display = isHidden ? 'block' : 'none';

    if (isHidden) {
      // Apply mobile layout adjustments
      adjustForMobile();

      // Show welcome message if this is the first time opening the chat
      if (!chatInitialized) {
        appendMessage(welcomeMessage, 'bot');
        chatInitialized = true;
      }

      // Focus on the input field when chat is opened
      setTimeout(() => {
        document.getElementById('user-input').focus();
        chat.scrollTop = chat.scrollHeight;
      }, 100);
    }
  }

  // Function to detect if text contains Arabic characters
  function containsArabic(text) {
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicPattern.test(text);
  }

  // Function to get localized timestamp
  function getLocalizedTimestamp(language) {
    const now = new Date();
    if (language === 'Arabic') {
      // Use Arabic numerals and format
      return now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      return now.getHours().toString().padStart(2, '0') + ':' +
             now.getMinutes().toString().padStart(2, '0');
    }
  }

  function appendMessage(content, type) {
    const chat = document.getElementById('chat-messages');
    const bubbleWrapper = document.createElement('div');
    bubbleWrapper.style.display = 'flex';
    bubbleWrapper.style.flexDirection = 'column';
    bubbleWrapper.style.alignItems = type === 'user' ? 'flex-end' : 'flex-start';

    const bubble = document.createElement('div');

    // Check if the content contains Arabic text
    const isArabic = containsArabic(content);

    // Fix phone numbers with + sign
    // Look for patterns like digits with + sign and wrap them in a span with explicit LTR direction
    const phoneRegex = /(\+\d+)/g;
    const contentWithFixedPhones = content.replace(phoneRegex, '<span style="unicode-bidi: embed; direction: ltr;">$1</span>');

    // Set text direction based on content
    if (isArabic) {
      bubble.style.direction = 'rtl';
      bubble.style.textAlign = 'right';
      bubble.style.fontFamily = 'Arial, sans-serif'; // Better font for Arabic
    } else {
      bubble.style.direction = 'ltr';
      bubble.style.textAlign = 'left';
    }

    // Use innerHTML to allow our span tags to work
    bubble.innerHTML = contentWithFixedPhones;
    bubble.style.maxWidth = '75%';
    bubble.style.padding = '8px 12px';
    bubble.style.borderRadius = '16px';
    bubble.style.fontSize = '14px';
    bubble.style.lineHeight = '1.4';

    const timestamp = document.createElement('div');
    // Use localized timestamp for Arabic
    timestamp.textContent = getLocalizedTimestamp(isArabic ? 'Arabic' : 'English');
    timestamp.style.fontSize = '10px';
    timestamp.style.marginTop = '2px';
    timestamp.style.marginBottom = '4px';
    timestamp.style.color = '#777';

    // Align timestamp with text direction
    timestamp.style.textAlign = isArabic ? 'right' : 'left';

    if (type === 'user') {
      bubble.style.background = '#d4f8d4';
      bubble.style.borderBottomRightRadius = '4px';
    } else {
      bubble.style.background = '#e4e6eb';
      bubble.style.borderBottomLeftRadius = '4px';
    }

    bubbleWrapper.appendChild(bubble);
    bubbleWrapper.appendChild(timestamp);
    chat.appendChild(bubbleWrapper);
    chat.scrollTop = chat.scrollHeight;

    // Add margin to bubble wrapper to prevent cutoff
    bubbleWrapper.style.marginBottom = '8px';
  }

  function showTypingAnimation() {
    const chat = document.getElementById('chat-messages');
    const typing = document.createElement('div');
    typing.id = 'typing-indicator';
    typing.style.alignSelf = 'flex-start';
    typing.style.background = '#e4e6eb';
    typing.style.maxWidth = '75%';
    typing.style.padding = '8px 12px';
    typing.style.borderRadius = '16px';
    typing.style.borderBottomLeftRadius = '4px';
    typing.style.fontSize = '14px';
    typing.style.marginBottom = '4px';

    // Create animated dots
    const dotsContainer = document.createElement('div');
    dotsContainer.style.display = 'flex';
    dotsContainer.style.gap = '4px';

    // Create three dots
    for (let i = 0; i < 3; i++) {
      const dot = document.createElement('div');
      dot.style.width = '6px';
      dot.style.height = '6px';
      dot.style.borderRadius = '50%';
      dot.style.background = '#666';
      dot.style.opacity = '0.7';

      // Add animation with delay based on dot index
      dot.style.animation = `dotPulse 1.4s infinite ${i * 0.2}s`;
      dotsContainer.appendChild(dot);
    }

    // Add animation keyframes and modern button styles
    if (!document.getElementById('dot-animation-style')) {
      const style = document.createElement('style');
      style.id = 'dot-animation-style';
      style.textContent = `
        @keyframes dotPulse {
          0%, 100% { transform: scale(0.7); opacity: 0.7; }
          50% { transform: scale(1.2); opacity: 1; }
        }
        
        @keyframes pulse {
          0% { box-shadow: 0 8px 25px rgba(65, 105, 225, 0.4); }
          50% { box-shadow: 0 8px 30px rgba(65, 105, 225, 0.6); }
          100% { box-shadow: 0 8px 25px rgba(65, 105, 225, 0.4); }
        }
        
        #chat-toggle button:hover {
          transform: translateY(-2px) scale(1.05);
          box-shadow: 0 12px 35px rgba(65, 105, 225, 0.6) !important;
          background: linear-gradient(135deg, #5a7bff 0%, #4169e1 100%) !important;
        }
        
        #chat-toggle button:active {
          transform: translateY(0) scale(1.02);
          box-shadow: 0 6px 20px rgba(65, 105, 225, 0.5) !important;
        }
        
        #chat-toggle button {
          animation: pulse 3s infinite;
        }
      `;
      document.head.appendChild(style);
    }

    typing.appendChild(dotsContainer);
    chat.appendChild(typing);
    chat.scrollTop = chat.scrollHeight;
  }

  function removeTypingAnimation() {
    const typing = document.getElementById('typing-indicator');
    if (typing) typing.remove();
  }

  // === API URL (initialize ONCE) ===
  const API_URL = 'http://127.0.0.1:8000/messages/';

  async function sendMessage(retryMessage = null) {
    const input = document.getElementById('user-input');
    let message = retryMessage || input.value.trim();
    if (!message) return;

    // Fix phone numbers with + sign before sending
    // This ensures the + sign is properly positioned before the numbers
    message = message.replace(/(\d+)\+/g, '+$1');

    // Store the message for potential retry
    lastSentMessage = message;

    // Only append user message and clear input if this is not a retry
    if (!retryMessage) {
      appendMessage(message, 'user');
      input.value = '';
    }

    showTypingAnimation();

    try {
      // Ensure we have a session ID
      if (!currentSessionId) {
        currentSessionId = generateUUID();
      }

      // Get the current hostname
      const hostName = window.location.hostname;

      const formData = new FormData();
      formData.append('content', message);
      formData.append('session_id', currentSessionId);
      formData.append('host_name', hostName);

      const res = await fetch(API_URL, {
        method: 'POST',
        body: formData
      });

      if (!res.ok) {
        throw new Error(`Server responded with status: ${res.status}`);
      }

      const data = await res.json();

      // Calculate typing delay based on message length (min 1s, max 3s)
      const typingDelay = Math.min(Math.max(data.response.length * 10, 1000), 3000);

      setTimeout(() => {
        removeTypingAnimation();
        appendMessage(data.response || 'No response', 'bot');
        // Re-focus on input after receiving response
        document.getElementById('user-input').focus();
      }, typingDelay);
    } catch (err) {
      removeTypingAnimation();
      showConnectionError();
    }
  }

  function showConnectionError() {
    const chat = document.getElementById('chat-messages');
    const errorWrapper = document.createElement('div');
    errorWrapper.style.display = 'flex';
    errorWrapper.style.flexDirection = 'column';
    errorWrapper.style.alignItems = 'flex-start';
    errorWrapper.style.maxWidth = '75%';
    errorWrapper.style.marginBottom = '8px';

    const errorBubble = document.createElement('div');
    errorBubble.textContent = "Sorry, I couldn't connect to the server. Please check your connection.";
    errorBubble.style.background = '#ffdddd';
    errorBubble.style.color = '#d32f2f';
    errorBubble.style.padding = '8px 12px';
    errorBubble.style.borderRadius = '16px';
    errorBubble.style.borderBottomLeftRadius = '4px';
    errorBubble.style.fontSize = '14px';
    errorBubble.style.marginBottom = '8px';

    const retryBtn = document.createElement('button');
    retryBtn.textContent = "Try Again";
    retryBtn.style.background = '#0a0a23';
    retryBtn.style.color = 'white';
    retryBtn.style.border = 'none';
    retryBtn.style.borderRadius = '4px';
    retryBtn.style.padding = '6px 12px';
    retryBtn.style.cursor = 'pointer';
    retryBtn.style.fontSize = '12px';

    retryBtn.onclick = function() {
      errorWrapper.remove();
      sendMessage(lastSentMessage); // Retry with the last message
    };

    errorWrapper.appendChild(errorBubble);
    errorWrapper.appendChild(retryBtn);
    chat.appendChild(errorWrapper);
    chat.scrollTop = chat.scrollHeight;

    // Re-focus on input
    document.getElementById('user-input').focus();
  }

  // CV upload logic (PDF, DOC, DOCX)
  document.getElementById('cv-upload').addEventListener('change', async function(event) {
    const file = event.target.files[0];
    if (!file) return;
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const fileName = file.name.toLowerCase();
    const isAllowedType = allowedTypes.includes(file.type) || allowedExtensions.some(ext => fileName.endsWith(ext));
    if (!isAllowedType) {
      appendMessage('Only PDF or Word files (.pdf, .doc, .docx) are allowed.', 'bot');
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      appendMessage('File size must be less than 5MB.', 'bot');
      return;
    }
    appendMessage('Uploading your CV, please wait...', 'bot');
    const formData = new FormData();
    formData.append('file', file);
    formData.append('session_id', currentSessionId);
    try {
      const res = await fetch(API_URL, {
        method: 'POST',
        body: formData
      });
      if (!res.ok) {
        throw new Error('Upload failed');
      }
      const data = await res.json();
      appendMessage(data.response || 'Assessment complete.', 'bot');
    } catch (err) {
      appendMessage('Failed to upload CV. Please try again.', 'bot');
    }
  });
</script>
