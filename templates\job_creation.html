<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Creation - Testing Interface</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #555;
      }
      input,
      textarea,
      select {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        box-sizing: border-box;
      }
      textarea {
        height: 120px;
        resize: vertical;
      }
      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }
      .form-row-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px;
      }
      button {
        background: #28a745;
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        margin-right: 10px;
      }
      button:hover {
        background: #218838;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .btn-secondary {
        background: #6c757d;
      }
      .btn-secondary:hover {
        background: #545b62;
      }
      .loading {
        display: none;
        text-align: center;
        padding: 20px;
        color: #666;
      }
      .success {
        background: #d4edda;
        color: #155724;
        padding: 15px;
        border-radius: 6px;
        margin-top: 20px;
        display: none;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 6px;
        margin-top: 20px;
        display: none;
      }
      .nav-links {
        text-align: center;
        margin-bottom: 20px;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        font-weight: 500;
      }
      .nav-links a:hover {
        text-decoration: underline;
      }
      .scoring-weights {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 6px;
        margin-top: 20px;
      }
      .weight-group {
        display: grid;
        grid-template-columns: 1fr 100px;
        gap: 10px;
        align-items: center;
        margin-bottom: 10px;
      }
      .weight-label {
        font-weight: 500;
      }
      .weight-input {
        text-align: center;
      }
      .total-weight {
        font-weight: 600;
        color: #007bff;
        text-align: right;
        margin-top: 10px;
      }
      .jobs-list {
        margin-top: 30px;
      }
      .job-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        background: #fafafa;
      }
      .job-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      .job-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      .job-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
      }
      .status-active {
        background: #28a745;
        color: white;
      }
      .status-inactive {
        background: #6c757d;
        color: white;
      }
      .job-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        font-size: 14px;
      }
      .detail-label {
        font-weight: 600;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="nav-links">
      <a href="/candidate-search">Candidate Search</a>
      <a href="/job-creation">Job Creation</a>
      <a href="/cv-assessments/">CV Assessments</a>
    </div>

    <div class="container">
      <h1>💼 Job Creation & Management</h1>

      <form id="jobForm">
        <div class="form-group">
          <label for="title">Job Title *:</label>
          <input
            type="text"
            id="title"
            required
            placeholder="e.g., Senior Software Engineer"
          />
        </div>

        <div class="form-group">
          <label for="description">Job Description *:</label>
          <textarea
            id="description"
            required
            placeholder="Enter detailed job description, requirements, responsibilities..."
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="company">Company:</label>
            <input type="text" id="company" placeholder="Company name" />
          </div>
          <div class="form-group">
            <label for="location">Location:</label>
            <input
              type="text"
              id="location"
              placeholder="e.g., New York, Remote"
            />
          </div>
        </div>

        <div class="form-row-3">
          <div class="form-group">
            <label for="minSalary">Min Salary:</label>
            <input
              type="number"
              id="minSalary"
              min="0"
              step="1000"
              placeholder="50000"
            />
          </div>
          <div class="form-group">
            <label for="maxSalary">Max Salary:</label>
            <input
              type="number"
              id="maxSalary"
              min="0"
              step="1000"
              placeholder="100000"
            />
          </div>
          <div class="form-group">
            <label for="experienceLevel">Experience Level:</label>
            <select id="experienceLevel">
              <option value="">Any</option>
              <option value="fresh">Fresh Graduate</option>
              <option value="junior">Junior (1-3 years)</option>
              <option value="mid">Mid-level (3-7 years)</option>
              <option value="senior">Senior (7+ years)</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="requiredSkills"
              >Required Skills (comma-separated):</label
            >
            <input
              type="text"
              id="requiredSkills"
              placeholder="Python, React, AWS, etc."
            />
          </div>
          <div class="form-group">
            <label for="department">Department:</label>
            <input
              type="text"
              id="department"
              placeholder="Engineering, Marketing, etc."
            />
          </div>
        </div>

        <div class="scoring-weights">
          <h3>Custom Scoring Weights (Optional)</h3>
          <p style="font-size: 14px; color: #666; margin-bottom: 15px">
            Adjust the importance of different criteria. Leave blank to use
            default weights.
          </p>

          <div class="weight-group">
            <div class="weight-label">Education</div>
            <input
              type="number"
              class="weight-input"
              id="educationWeight"
              min="0"
              max="100"
              step="5"
              placeholder="20"
            />
          </div>
          <div class="weight-group">
            <div class="weight-label">Experience</div>
            <input
              type="number"
              class="weight-input"
              id="experienceWeight"
              min="0"
              max="100"
              step="5"
              placeholder="25"
            />
          </div>
          <div class="weight-group">
            <div class="weight-label">Skills</div>
            <input
              type="number"
              class="weight-input"
              id="skillsWeight"
              min="0"
              max="100"
              step="5"
              placeholder="30"
            />
          </div>
          <div class="weight-group">
            <div class="weight-label">Achievements</div>
            <input
              type="number"
              class="weight-input"
              id="achievementsWeight"
              min="0"
              max="100"
              step="5"
              placeholder="15"
            />
          </div>
          <div class="weight-group">
            <div class="weight-label">Personal</div>
            <input
              type="number"
              class="weight-input"
              id="personalWeight"
              min="0"
              max="100"
              step="5"
              placeholder="5"
            />
          </div>
          <div class="weight-group">
            <div class="weight-label">Cultural</div>
            <input
              type="number"
              class="weight-input"
              id="culturalWeight"
              min="0"
              max="100"
              step="5"
              placeholder="5"
            />
          </div>
          <div class="total-weight" id="totalWeight">Total: 0%</div>
        </div>

        <div style="margin-top: 30px">
          <button type="submit">Create Job</button>
          <button type="button" class="btn-secondary" onclick="loadJobs()">
            Refresh Jobs List
          </button>
        </div>
      </form>

      <div class="loading" id="loading">
        <p>💼 Creating job...</p>
      </div>

      <div class="success" id="success"></div>
      <div class="error" id="error"></div>
    </div>

    <div class="container">
      <div class="jobs-list">
        <h2>Existing Jobs</h2>
        <div id="jobsList">
          <p>Loading jobs...</p>
        </div>
      </div>
    </div>

    <script>
      // Calculate total weight
      function updateTotalWeight() {
        const weights = [
          "educationWeight",
          "experienceWeight",
          "skillsWeight",
          "achievementsWeight",
          "personalWeight",
          "culturalWeight",
        ];

        let total = 0;
        weights.forEach((id) => {
          const value = parseInt(document.getElementById(id).value) || 0;
          total += value;
        });

        const totalElement = document.getElementById("totalWeight");
        totalElement.textContent = `Total: ${total}%`;
        totalElement.style.color =
          total === 100 ? "#28a745" : total > 100 ? "#dc3545" : "#007bff";
      }

      // Add event listeners for weight inputs
      [
        "educationWeight",
        "experienceWeight",
        "skillsWeight",
        "achievementsWeight",
        "personalWeight",
        "culturalWeight",
      ].forEach((id) => {
        document
          .getElementById(id)
          .addEventListener("input", updateTotalWeight);
      });

      // Form submission
      document
        .getElementById("jobForm")
        .addEventListener("submit", async function (e) {
          e.preventDefault();

          const loading = document.getElementById("loading");
          const success = document.getElementById("success");
          const error = document.getElementById("error");

          // Show loading, hide messages
          loading.style.display = "block";
          success.style.display = "none";
          error.style.display = "none";

          // Collect form data
          const jobData = {
            title: document.getElementById("title").value,
            description: document.getElementById("description").value,
            company: document.getElementById("company").value || null,
            location: document.getElementById("location").value || null,
            department: document.getElementById("department").value || null,
            experience_level:
              document.getElementById("experienceLevel").value || null,
            required_skills: document.getElementById("requiredSkills").value
              ? document
                  .getElementById("requiredSkills")
                  .value.split(",")
                  .map((s) => s.trim())
              : [],
            salary_range: {},
          };

          // Add salary range if provided
          const minSalary = document.getElementById("minSalary").value;
          const maxSalary = document.getElementById("maxSalary").value;
          if (minSalary) jobData.salary_range.min = parseInt(minSalary);
          if (maxSalary) jobData.salary_range.max = parseInt(maxSalary);

          // Add custom weights if provided
          const weights = {};
          const weightFields = [
            "educationWeight",
            "experienceWeight",
            "skillsWeight",
            "achievementsWeight",
            "personalWeight",
            "culturalWeight",
          ];
          const weightKeys = [
            "education",
            "experience",
            "skills",
            "achievements",
            "personal",
            "cultural",
          ];

          let hasCustomWeights = false;
          weightFields.forEach((field, index) => {
            const value = document.getElementById(field).value;
            if (value) {
              weights[weightKeys[index]] = parseInt(value) / 100;
              hasCustomWeights = true;
            }
          });

          if (hasCustomWeights) {
            jobData.custom_weights = weights;
          }

          try {
            const response = await fetch("/jobs", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(jobData),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(
                errorData.detail || `HTTP error! status: ${response.status}`
              );
            }

            const result = await response.json();
            const job = result.job || result; // Handle both response formats
            success.textContent = `Job "${job.title}" created successfully! ID: ${job.id}`;
            success.style.display = "block";

            // Reset form
            document.getElementById("jobForm").reset();
            updateTotalWeight();

            // Refresh jobs list
            loadJobs();
          } catch (err) {
            console.error("Job creation error:", err);
            error.textContent = `Job creation failed: ${err.message}`;
            error.style.display = "block";
          } finally {
            loading.style.display = "none";
          }
        });

      // Load existing jobs
      async function loadJobs() {
        const jobsList = document.getElementById("jobsList");
        jobsList.innerHTML = "<p>Loading jobs...</p>";

        try {
          const response = await fetch("/jobs?active_only=false");
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (!data.jobs || data.jobs.length === 0) {
            jobsList.innerHTML = "<p>No jobs found.</p>";
            return;
          }

          let html = "";
          data.jobs.forEach((job) => {
            html += `
                        <div class="job-card">
                            <div class="job-header">
                                <div class="job-title">${job.title}</div>
                                <div class="job-status ${
                                  job.is_active
                                    ? "status-active"
                                    : "status-inactive"
                                }">
                                    ${job.is_active ? "ACTIVE" : "INACTIVE"}
                                </div>
                            </div>
                            
                            <div class="job-details">
                                <div>
                                    <div class="detail-label">Company:</div>
                                    <div>${job.company || "N/A"}</div>
                                </div>
                                <div>
                                    <div class="detail-label">Location:</div>
                                    <div>${job.location || "N/A"}</div>
                                </div>
                                <div>
                                    <div class="detail-label">Department:</div>
                                    <div>${job.department || "N/A"}</div>
                                </div>
                                <div>
                                    <div class="detail-label">Experience Level:</div>
                                    <div>${job.experience_level || "Any"}</div>
                                </div>
                                <div>
                                    <div class="detail-label">Salary Range:</div>
                                    <div>${
                                      job.salary_range
                                        ? `$${
                                            job.salary_range.min?.toLocaleString() ||
                                            "0"
                                          } - $${
                                            job.salary_range.max?.toLocaleString() ||
                                            "∞"
                                          }`
                                        : "N/A"
                                    }</div>
                                </div>
                                <div>
                                    <div class="detail-label">Created:</div>
                                    <div>${new Date(
                                      job.created_at
                                    ).toLocaleDateString()}</div>
                                </div>
                            </div>
                            
                            ${
                              job.required_skills &&
                              job.required_skills.length > 0
                                ? `
                                <div style="margin-top: 15px;">
                                    <div class="detail-label">Required Skills:</div>
                                    <div>${job.required_skills.join(", ")}</div>
                                </div>
                            `
                                : ""
                            }
                            
                            <div style="margin-top: 15px;">
                                <button onclick="searchJobCandidates('${
                                  job.id
                                }')" style="font-size: 14px; padding: 8px 16px;">
                                    Find Candidates
                                </button>
                            </div>
                        </div>
                    `;
          });

          jobsList.innerHTML = html;
        } catch (err) {
          console.error("Load jobs error:", err);
          jobsList.innerHTML = `<p style="color: #dc3545;">Failed to load jobs: ${err.message}</p>`;
        }
      }

      // Search candidates for a specific job
      function searchJobCandidates(jobId) {
        window.open(`/candidate-search?job_id=${jobId}`, "_blank");
      }

      // Load jobs on page load
      loadJobs();
    </script>
  </body>
</html>
