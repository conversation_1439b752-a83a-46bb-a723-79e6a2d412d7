"""
Lead Service Module

This module provides functionality for lead-related operations, including
detecting leads, extracting lead information, and processing lead messages.
"""

import logging

from database.models import LeadInfo

# Configure logging
logger = logging.getLogger("lead_service")


def save_lead(
    supabase_client,
    lead_info,
    session_id: str,
    user_language: str = "English",
    email_service=None,
    google_sheets_service=None,
    whatsapp_service=None,
    service_executor=None,
) -> bool:
    """
    Save lead information to the database or update if it already exists.
    For new leads, also send email notification, WhatsApp message, and update Google Sheets.

    Args:
        supabase_client: Supabase client
        lead_info: Lead information to save (LeadInfo object or dict)
        session_id: Session ID for the conversation
        user_language: Language of the user (default: English)
        email_service: Email service module (optional)
        google_sheets_service: Google Sheets service module (optional)
        whatsapp_service: WhatsApp service module (optional)
        service_executor: Thread pool executor for background tasks (optional)

    Returns:
        True if the lead was saved successfully, False otherwise
    """
    # Convert dict to LeadInfo if needed
    if isinstance(lead_info, dict):
        from database.models import LeadInfo

        lead_info = LeadInfo(
            name=lead_info.get("name", "Unknown"),
            email=lead_info.get("email"),
            phone=lead_info.get("phone"),
            lead_type=lead_info.get("lead_type"),
            company=lead_info.get("company"),
            industry=lead_info.get("industry"),
            num_locations=lead_info.get("num_locations"),
            num_employees=lead_info.get("num_employees"),
            additional_message=lead_info.get("additional_message"),
            consent_given=lead_info.get("consent_given", False),
        )
    # Check if the name is a placeholder - we require a real name before saving to the database
    is_placeholder_name = any(
        placeholder in lead_info.name.lower()
        for placeholder in ["unnamed", "unknown", "customer"]
    )

    if is_placeholder_name:
        logger.warning(
            f"Cannot save lead: A real name is required, not a placeholder: '{lead_info.name}'"
        )
        return False

    try:
        # Convert LeadInfo to dict for database operations
        lead_data = lead_info.model_dump()
        lead_data["session_id"] = session_id

        # Check if a lead with this session_id, email, or phone already exists
        existing_lead = None

        # First try to find by session_id (most reliable)
        session_result = (
            supabase_client.table("leads")
            .select("*")
            .eq("session_id", session_id)
            .execute()
        )
        if session_result.data and len(session_result.data) > 0:
            existing_lead = session_result.data[0]
            logger.info(f"Found existing lead with session_id: {session_id}")

        # If not found by session_id, try email
        if not existing_lead and lead_info.email:
            email_result = (
                supabase_client.table("leads")
                .select("*")
                .eq("email", lead_info.email)
                .execute()
            )
            if email_result.data and len(email_result.data) > 0:
                existing_lead = email_result.data[0]
                logger.info(f"Found existing lead with email: {lead_info.email}")

        # If still not found, try phone
        if not existing_lead and lead_info.phone:
            phone_result = (
                supabase_client.table("leads")
                .select("*")
                .eq("phone", lead_info.phone)
                .execute()
            )
            if phone_result.data and len(phone_result.data) > 0:
                existing_lead = phone_result.data[0]
                logger.info(f"Found existing lead with phone: {lead_info.phone}")

        if existing_lead:
            # Update existing lead with new information
            # Only update fields that are provided in the new lead_info
            update_data = {}
            for key, value in lead_data.items():
                # Skip session_id as it's a reference field
                if key == "session_id":
                    continue

                # Only update if the new value is not None and either:
                # 1. The field doesn't exist in the existing lead, or
                # 2. The existing value is None and the new value is not None, or
                # 3. The new value is different from the existing one
                if value is not None and (
                    key not in existing_lead
                    or existing_lead[key] is None
                    or existing_lead[key] != value
                ):
                    update_data[key] = value

            # If there's nothing to update, return success
            if not update_data:
                # Still update Google Sheets to ensure it's in sync
                if google_sheets_service and service_executor:
                    service_executor.submit(
                        google_sheets_service.add_or_update_lead_in_sheet, lead_info
                    )
                return True

            # Update the lead
            try:
                result = (
                    supabase_client.table("leads")
                    .update(update_data)
                    .eq("id", existing_lead["id"])
                    .execute()
                )
                success = len(result.data) > 0

                if success:
                    # Create a complete LeadInfo object with all the existing data plus the updates
                    # This ensures all fields are properly passed to Google Sheets
                    from database.models import LeadInfo

                    complete_lead_info = LeadInfo(
                        name=existing_lead.get("name"),
                        email=existing_lead.get("email"),
                        phone=update_data.get("phone", existing_lead.get("phone")),
                        lead_type=update_data.get(
                            "lead_type", existing_lead.get("lead_type")
                        ),
                        company=update_data.get(
                            "company", existing_lead.get("company")
                        ),
                        industry=update_data.get(
                            "industry", existing_lead.get("industry")
                        ),
                        num_locations=update_data.get(
                            "num_locations", existing_lead.get("num_locations")
                        ),
                        num_employees=update_data.get(
                            "num_employees", existing_lead.get("num_employees")
                        ),
                        additional_message=update_data.get(
                            "additional_message",
                            existing_lead.get("additional_message"),
                        ),
                        consent_given=existing_lead.get("consent_given", False),
                    )

                    # Update with the new values
                    for field, value in update_data.items():
                        setattr(complete_lead_info, field, value)

                    # Check if phone number was just added or updated
                    phone_updated = (
                        "phone" in update_data
                        and update_data["phone"]
                        and (
                            "phone" not in existing_lead
                            or not existing_lead["phone"]
                            or existing_lead["phone"] != update_data["phone"]
                        )
                    )

                    # Use a thread pool to coordinate background tasks
                    futures = []

                    # Send WhatsApp message if phone number was just added or updated
                    if phone_updated and whatsapp_service and service_executor:
                        logger.info(
                            f"Phone number updated for lead {complete_lead_info.name}. Sending WhatsApp message."
                        )
                        futures.append(
                            service_executor.submit(
                                whatsapp_service.send_whatsapp_message,
                                complete_lead_info,
                                user_language,
                            )
                        )

                    # Update Google Sheets
                    if google_sheets_service:
                        logger.info(
                            f"Submitting lead update to Google Sheets: {complete_lead_info.name}"
                        )
                        try:
                            # Import directly
                            from services import google_sheets_service as gss

                            # Call directly
                            result = gss.add_or_update_lead_in_sheet(complete_lead_info)
                            logger.info(f"Google Sheets update result: {result}")

                        except Exception as e:
                            logger.error(f"Error updating Google Sheets: {e}")

                return success
            except Exception as e:
                logger.error(f"Error updating lead: {e}")
                return False
        else:
            # Insert new lead
            # Make sure we're not violating unique constraints
            try:
                # If email is None, make sure it's explicitly set to None
                if "email" not in lead_data or lead_data["email"] is None:
                    lead_data["email"] = None

                # If phone is None, make sure it's explicitly set to None
                if "phone" not in lead_data or lead_data["phone"] is None:
                    lead_data["phone"] = None

                # Use a transaction to ensure data consistency
                # Note: Supabase client doesn't support explicit transactions,
                # but the server-side PostgreSQL will handle this as a single transaction
                result = supabase_client.table("leads").insert(lead_data).execute()
                success = len(result.data) > 0

                if success and service_executor:
                    # Use a thread pool to coordinate background tasks
                    futures = []

                    # This is a new lead, send email notification to sales team (in background)
                    if email_service:
                        futures.append(
                            service_executor.submit(
                                email_service.send_lead_notification, lead_info
                            )
                        )

                    # Send follow-up email to lead if email is provided (in background)
                    if email_service and lead_info.email:
                        futures.append(
                            service_executor.submit(
                                email_service.send_lead_followup_email,
                                lead_info,
                                user_language,
                            )
                        )

                    # Send WhatsApp message if phone number is provided (in background)
                    if whatsapp_service and lead_info.phone:
                        futures.append(
                            service_executor.submit(
                                whatsapp_service.send_whatsapp_message,
                                lead_info,
                                user_language,
                            )
                        )

                    # Update Google Sheets
                    if google_sheets_service:
                        logger.info(
                            f"Submitting new lead to Google Sheets: {lead_info.name}"
                        )
                        try:
                            # Import directly
                            from services import google_sheets_service as gss

                            # Call directly
                            result = gss.add_or_update_lead_in_sheet(lead_info)
                            logger.info(f"Google Sheets update result: {result}")

                        except Exception as e:
                            logger.error(f"Error updating Google Sheets: {e}")

                return success
            except Exception as e:
                logger.error(f"Error inserting lead: {e}")
                # If insertion fails due to unique constraint, try to find the lead again
                # This can happen if the lead was created between our check and insert
                try:
                    # Try to find by session_id first
                    retry_result = (
                        supabase_client.table("leads")
                        .select("*")
                        .eq("session_id", session_id)
                        .execute()
                    )
                    if retry_result.data and len(retry_result.data) > 0:
                        existing_lead = retry_result.data[0]

                        # Update the lead with new information
                        update_data = {
                            k: v
                            for k, v in lead_data.items()
                            if k != "session_id" and v is not None
                        }
                        if update_data:
                            update_result = (
                                supabase_client.table("leads")
                                .update(update_data)
                                .eq("id", existing_lead["id"])
                                .execute()
                            )
                            success = len(update_result.data) > 0

                            # Update Google Sheets
                            if success and google_sheets_service:
                                logger.info(
                                    f"Submitting lead update to Google Sheets (retry): {lead_info.name}"
                                )
                                try:
                                    # Import directly
                                    from services import google_sheets_service as gss

                                    # Call directly
                                    result = gss.add_or_update_lead_in_sheet(lead_info)
                                    logger.info(
                                        f"Google Sheets update result (retry): {result}"
                                    )

                                except Exception as e:
                                    logger.error(
                                        f"Error updating Google Sheets (retry): {e}"
                                    )

                            return success

                        # Update Google Sheets
                        if google_sheets_service:
                            logger.info(
                                f"Submitting lead to Google Sheets (retry fallback): {lead_info.name}"
                            )
                            try:
                                # Import directly
                                from services import google_sheets_service as gss

                                # Call directly
                                result = gss.add_or_update_lead_in_sheet(lead_info)
                                logger.info(
                                    f"Google Sheets update result (retry fallback): {result}"
                                )

                            except Exception as e:
                                logger.error(
                                    f"Error updating Google Sheets (retry fallback): {e}"
                                )

                        return True
                except Exception as retry_error:
                    logger.error(f"Error during retry: {retry_error}")
                    pass

                return False
    except Exception as e:
        logger.error(f"Unexpected error in save_lead: {e}")
        return False
