"""
Language Service Module

This module provides functionality for language detection and database management.
It handles determining the user's language preference and storing it in the database.
Translation is now handled by the agent internally.
"""

import re
import logging

# Configure logging
logger = logging.getLogger("language_service")


def detect_user_language(
    message_content: str, previous_language: str = "English"
) -> str:
    """
    Detect the language of a user message and determine the language preference.

    Args:
        message_content: The content of the user's message
        previous_language: The previously detected language (default: English)

    Returns:
        The detected language preference (English or Arabic)
    """
    if not message_content:
        return previous_language

    # Check if the message contains ANY Arabic characters
    contains_arabic = bool(re.search(r"[\u0600-\u06FF]", message_content))

    # If there's ANY Arabic content, set language to Arabic
    if contains_arabic:
        arabic_char_count = len(re.findall(r"[\u0600-\u06FF]", message_content))
        logger.info(f"Detected {arabic_char_count} Arabic characters in message")
        return "Arabic"

    # Check if the message is just an email, phone number, or short code
    is_just_email = re.match(
        r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        message_content.strip(),
    )
    is_just_phone = re.match(r"^[+]?[\d\s()-]{5,20}$", message_content.strip())

    if is_just_email or is_just_phone:
        # If it's just an email or phone, keep the previous language
        return previous_language

    # Check if the message contains any English words
    if re.search(r"[a-zA-Z]{2,}", message_content):
        # If there are any English words (even in short messages), use English
        return "English"

    # For other short messages, keep the previous language
    return previous_language


def update_language_preference(
    supabase_client, session_id: str, new_language: str
) -> bool:
    """
    Update the language preference for a session in the database.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID
        new_language: The new language preference

    Returns:
        True if the update was successful, False otherwise
    """
    try:
        supabase_client.table("conversations").update({"language": new_language}).eq(
            "session_id", session_id
        ).execute()
        logger.info(
            f"Updated language preference to {new_language} for session {session_id}"
        )
        return True
    except Exception as e:
        logger.error(f"Error updating language preference: {e}")
        return False


def get_language_preference(supabase_client, session_id: str) -> str:
    """
    Get the current language preference for a session from the database.

    Args:
        supabase_client: Initialized Supabase client
        session_id: The session ID

    Returns:
        The current language preference (defaults to English if not found)
    """
    try:
        conversation_result = (
            supabase_client.table("conversations")
            .select("language")
            .eq("session_id", session_id)
            .execute()
        )

        if conversation_result.data and len(conversation_result.data) > 0:
            return conversation_result.data[0].get("language", "English")
        else:
            return "English"  # Default if not found
    except Exception as e:
        logger.error(f"Error retrieving language preference: {e}")
        return "English"  # Default to English on error
