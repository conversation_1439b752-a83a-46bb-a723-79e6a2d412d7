"""
Google Sheets Service Module

This module provides functionality for adding and updating lead information
in a Google Sheet for tracking purposes.
"""

import os
import json
import time
import logging
import socket
import warnings
from typing import List, Optional, Dict
from dotenv import load_dotenv
import googleapiclient.discovery
import googleapiclient.errors
from google.oauth2 import service_account
from google.oauth2.service_account import Credentials

# Suppress the googleapiclient.discovery_cache warning
warnings.filterwarnings(
    "ignore", message="file_cache is only supported with oauth2client<4.0.0"
)

# Import shared models to avoid circular imports
from database.models import LeadInfo

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("google_sheets_service")

# Load environment variables
load_dotenv()

# Get Google Sheets configuration from environment variables
GOOGLE_SHEETS_CREDENTIALS = os.environ.get("GOOGLE_SHEETS_CREDENTIALS")
GOOGLE_SHEETS_ID = os.environ.get("GOOGLE_SHEETS_ID")
SALES_TEAM_EMAIL = os.environ.get("SALES_TEAM_EMAIL", "")

# Define the column headers for the leads sheet
SHEET_HEADERS = [
    "Name",
    "Email",
    "Phone",
    "Lead Type",
    "Company",
    "Industry",
    "Number of Locations",
    "Number of Employees",
    "Additional Message",
    "Consent Given",
    "Date Added",
]

# Define the header for the sales team emails sheet
SALES_EMAILS_HEADER = ["email"]


def get_credentials() -> Optional[Credentials]:
    """
    Get Google API credentials from the environment variable.

    Returns:
        Google API credentials or None if not configured
    """
    if not GOOGLE_SHEETS_CREDENTIALS:
        logger.error("Google Sheets credentials not found in environment variables")
        return None

    try:
        # Check if the credentials are a file path or JSON string
        if os.path.isfile(GOOGLE_SHEETS_CREDENTIALS):
            return service_account.Credentials.from_service_account_file(
                GOOGLE_SHEETS_CREDENTIALS,
                scopes=["https://www.googleapis.com/auth/spreadsheets"],
            )
        else:
            # Assume it's a JSON string
            credentials_info = json.loads(GOOGLE_SHEETS_CREDENTIALS)
            return service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=["https://www.googleapis.com/auth/spreadsheets"],
            )
    except Exception as e:
        logger.error(f"Error loading Google credentials: {str(e)}")
        return None


def get_sheets_service():
    """
    Get the Google Sheets API service.

    Returns:
        Google Sheets API service or None if not configured
    """
    credentials = get_credentials()
    if not credentials:
        return None

    try:
        return googleapiclient.discovery.build("sheets", "v4", credentials=credentials)
    except Exception as e:
        logger.error(f"Error building Google Sheets service: {str(e)}")
        return None


def initialize_sheet() -> bool:
    """
    Initialize the Google Sheet with headers if it doesn't exist.

    Returns:
        True if successful, False otherwise

    Raises:
        ConnectionError: If there's a network-related error
        Exception: For other errors
    """
    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return False

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return False

    try:
        # Check if the sheet exists and has headers
        result = (
            service.spreadsheets()
            .values()
            .get(spreadsheetId=GOOGLE_SHEETS_ID, range="Leads!A1:K1")
            .execute()
        )

        values = result.get("values", [])

        # If the sheet is empty or doesn't have headers, add them
        if not values or len(values[0]) < len(SHEET_HEADERS):
            service.spreadsheets().values().update(
                spreadsheetId=GOOGLE_SHEETS_ID,
                range="Leads!A1",
                valueInputOption="RAW",
                body={"values": [SHEET_HEADERS]},
            ).execute()
            logger.info("Initialized Google Sheet with headers")
        else:
            logger.info("Google Sheet already has headers")

        return True
    except (socket.timeout, socket.error, ConnectionError) as e:
        # Re-raise connection errors for retry logic
        logger.warning(f"Connection error while initializing sheet: {str(e)}")
        raise ConnectionError(f"Connection error while initializing sheet: {str(e)}")
    except Exception as e:
        logger.error(f"Error initializing Google Sheet: {str(e)}")
        raise


def initialize_sales_emails_sheet() -> bool:
    """
    Initialize the sales team emails sheet if it doesn't exist.

    Returns:
        True if successful, False otherwise
    """
    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return False

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return False

    try:
        # Check if SalesTeamEmails sheet exists
        result = service.spreadsheets().get(spreadsheetId=GOOGLE_SHEETS_ID).execute()

        # Check if SalesTeamEmails sheet exists
        sheet_exists = False
        for sheet in result.get("sheets", []):
            if sheet.get("properties", {}).get("title") == "SalesTeamEmails":
                sheet_exists = True
                break

        # If SalesTeamEmails sheet doesn't exist, create it
        if not sheet_exists:
            request = {"addSheet": {"properties": {"title": "SalesTeamEmails"}}}

            service.spreadsheets().batchUpdate(
                spreadsheetId=GOOGLE_SHEETS_ID, body={"requests": [request]}
            ).execute()

            # Add header and default email if available
            values = [SALES_EMAILS_HEADER]

            # Add default email from environment variable if available
            if SALES_TEAM_EMAIL:
                values.append([SALES_TEAM_EMAIL])

            service.spreadsheets().values().update(
                spreadsheetId=GOOGLE_SHEETS_ID,
                range="SalesTeamEmails!A1:A" + str(len(values)),
                valueInputOption="RAW",
                body={"values": values},
            ).execute()

            logger.info("Created SalesTeamEmails sheet with default email")
        else:
            # Check if the sheet has headers
            try:
                header_result = (
                    service.spreadsheets()
                    .values()
                    .get(spreadsheetId=GOOGLE_SHEETS_ID, range="SalesTeamEmails!A1")
                    .execute()
                )

                header_values = header_result.get("values", [])

                # If the sheet is empty or doesn't have the correct header, add it
                if not header_values or header_values[0][0].lower() != "email":
                    service.spreadsheets().values().update(
                        spreadsheetId=GOOGLE_SHEETS_ID,
                        range="SalesTeamEmails!A1",
                        valueInputOption="RAW",
                        body={"values": [SALES_EMAILS_HEADER]},
                    ).execute()
                    logger.info("Added header to existing SalesTeamEmails sheet")
            except Exception as e:
                logger.error(f"Error checking SalesTeamEmails sheet header: {str(e)}")

        return True
    except Exception as e:
        logger.error(f"Error initializing SalesTeamEmails sheet: {str(e)}")
        return False


def find_lead_row(email: Optional[str], phone: Optional[str]) -> int:
    """
    Find a lead in the Google Sheet by email or phone.

    Args:
        email: The lead's email address
        phone: The lead's phone number

    Returns:
        The row number (1-based) if found, or -1 if not found

    Raises:
        ConnectionError: If there's a network-related error
        Exception: For other errors
    """
    if not email and not phone:
        return -1

    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return -1

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return -1

    try:
        # Get all data from the sheet
        result = (
            service.spreadsheets()
            .values()
            .get(spreadsheetId=GOOGLE_SHEETS_ID, range="Leads!A:K")
            .execute()
        )

        values = result.get("values", [])
        if not values:
            logger.info("No data found in Google Sheet")
            return -1

        # Skip the header row
        for i, row in enumerate(values[1:], start=2):
            # Check if row has enough columns
            if len(row) < 3:
                continue

            # Check if email or phone matches
            row_email = row[1] if len(row) > 1 else ""
            row_phone = row[2] if len(row) > 2 else ""

            if (email and row_email == email) or (phone and row_phone == phone):
                logger.info(
                    f"Found lead at row {i} with {'email' if email and row_email == email else 'phone'} match"
                )
                return i

        logger.info(f"No matching lead found for email={email}, phone={phone}")
        return -1
    except (socket.timeout, socket.error, ConnectionError) as e:
        # Re-raise connection errors for retry logic
        logger.warning(f"Connection error while finding lead: {str(e)}")
        raise ConnectionError(f"Connection error while finding lead: {str(e)}")
    except Exception as e:
        logger.error(f"Error finding lead in Google Sheet: {str(e)}")
        raise


def lead_to_row(lead_info: LeadInfo) -> List[str]:
    """
    Convert lead information to a row for the Google Sheet.

    Args:
        lead_info: The lead information to convert

    Returns:
        A list of values for the sheet row
    """
    from datetime import datetime

    return [
        lead_info.name or "",
        lead_info.email or "",
        lead_info.phone or "",
        lead_info.lead_type or "",
        lead_info.company or "",
        lead_info.industry or "",
        str(lead_info.num_locations) if lead_info.num_locations is not None else "",
        str(lead_info.num_employees) if lead_info.num_employees is not None else "",
        lead_info.additional_message or "",
        "Yes" if lead_info.consent_given else "No",
        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    ]


def get_leads_from_sheet(
    max_retries: int = 3, retry_delay: int = 5
) -> List[Dict[str, str]]:
    """
    Get all leads from the Google Sheet.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        List of dictionaries containing lead information
    """
    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return []

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return []

    # Implement retry logic
    for attempt in range(max_retries + 1):
        try:
            # Get all data from the sheet
            result = (
                service.spreadsheets()
                .values()
                .get(spreadsheetId=GOOGLE_SHEETS_ID, range="Leads!A:K")
                .execute()
            )

            values = result.get("values", [])
            if not values:
                logger.info("No data found in Google Sheet")
                return []

            # Skip the header row
            leads = []
            for row in values[1:]:  # Skip header row
                # Ensure the row has enough columns
                if len(row) < 3:
                    continue

                # Create a dictionary for each lead
                lead = {}
                for i, header in enumerate(SHEET_HEADERS):
                    if i < len(row):
                        lead[header.lower().replace(" ", "_")] = row[i]
                    else:
                        lead[header.lower().replace(" ", "_")] = ""

                leads.append(lead)

            logger.info(f"Found {len(leads)} leads in Google Sheet")
            return leads

        except (socket.timeout, socket.error, ConnectionError) as e:
            if attempt < max_retries:
                logger.warning(f"Connection error reading leads: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to read leads after {max_retries} attempts: {str(e)}"
                )
                return []
        except Exception as e:
            logger.error(f"Error reading leads from Google Sheet: {str(e)}")
            return []

    return []


def get_sales_team_emails(max_retries: int = 3, retry_delay: int = 5) -> List[str]:
    """
    Get all sales team email addresses from the Google Sheet.
    Falls back to environment variable if configuration is not available.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        List of email addresses
    """
    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        # Fall back to environment variable
        return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        # Fall back to environment variable
        return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []

    # Make sure the sheet exists
    try:
        initialize_sales_emails_sheet()
    except Exception as e:
        logger.error(f"Error initializing sales emails sheet: {str(e)}")
        # Fall back to environment variable
        return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []

    # Implement retry logic
    for attempt in range(max_retries + 1):
        try:
            # Get all emails from the sheet
            result = (
                service.spreadsheets()
                .values()
                .get(spreadsheetId=GOOGLE_SHEETS_ID, range="SalesTeamEmails!A:A")
                .execute()
            )

            values = result.get("values", [])
            if not values:
                logger.warning("No sales team emails found in Google Sheet")
                # Fall back to environment variable
                return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []

            # Skip the header row and collect all email addresses
            emails = []
            for row in values[1:]:  # Skip header row
                if row and row[0].strip():  # Ensure the cell is not empty
                    emails.append(row[0].strip())

            if not emails:
                logger.warning(
                    "No valid email addresses found in SalesTeamEmails sheet"
                )
                # Fall back to environment variable
                return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []

            logger.info(f"Found {len(emails)} sales team email(s) in Google Sheet")
            return emails

        except (socket.timeout, socket.error, ConnectionError) as e:
            if attempt < max_retries:
                logger.warning(f"Connection error reading sales team emails: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to read sales team emails after {max_retries} attempts: {str(e)}"
                )
                # Fall back to environment variable
                return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []
        except Exception as e:
            logger.error(f"Error reading sales team emails: {str(e)}")
            # Fall back to environment variable
            return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []

    # This should not be reached, but just in case
    return [SALES_TEAM_EMAIL] if SALES_TEAM_EMAIL else []


def add_sales_team_email(
    email: str, max_retries: int = 3, retry_delay: int = 5
) -> bool:
    """
    Add a new sales team email to the configuration.

    Args:
        email: The email address to add
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        True if successful, False otherwise
    """
    if not email or not email.strip():
        logger.error("Cannot add empty email address")
        return False

    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return False

    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return False

    # Make sure the sheet exists
    try:
        if not initialize_sales_emails_sheet():
            logger.error("Failed to initialize sales emails sheet")
            return False
    except Exception as e:
        logger.error(f"Error initializing sales emails sheet: {str(e)}")
        return False

    # Clean the email address
    email = email.strip()

    # Implement retry logic
    for attempt in range(max_retries + 1):
        try:
            # First check if this email already exists
            existing_emails = get_sales_team_emails()
            if email in existing_emails:
                logger.info(f"Email {email} already exists in configuration")
                return True

            # Get the next available row
            result = (
                service.spreadsheets()
                .values()
                .get(spreadsheetId=GOOGLE_SHEETS_ID, range="SalesTeamEmails!A:A")
                .execute()
            )

            values = result.get("values", [])
            next_row = len(values) + 1

            # Add the new email
            service.spreadsheets().values().update(
                spreadsheetId=GOOGLE_SHEETS_ID,
                range=f"SalesTeamEmails!A{next_row}",
                valueInputOption="RAW",
                body={"values": [[email]]},
            ).execute()

            logger.info(f"Added new sales team email: {email}")
            return True
        except (socket.timeout, socket.error, ConnectionError) as e:
            if attempt < max_retries:
                logger.warning(f"Connection error adding sales team email: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to add sales team email after {max_retries} attempts: {str(e)}"
                )
                return False
        except Exception as e:
            logger.error(f"Error adding sales team email: {str(e)}")
            return False

    # This should not be reached, but just in case
    return False


def add_or_update_lead_in_sheet(
    lead_info: LeadInfo, max_retries: int = 3, retry_delay: int = 5
) -> bool:
    """
    Add a new lead to the Google Sheet or update an existing one.

    This function includes retry logic for transient failures.

    Args:
        lead_info: The lead information to add or update
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        True if successful, False otherwise
    """
    logger.info(
        f"GOOGLE SHEETS: Adding/updating lead in Google Sheet: {lead_info.name} with email {lead_info.email}"
    )
    if not GOOGLE_SHEETS_ID:
        logger.error("Google Sheets ID not found in environment variables")
        return False

    # Get the Google Sheets service
    service = get_sheets_service()
    if not service:
        logger.error("Failed to initialize Google Sheets service")
        return False

    # Initialize the sheet with headers if needed
    sheet_initialized = False
    for attempt in range(max_retries + 1):
        try:
            if initialize_sheet():
                sheet_initialized = True
                break
            else:
                if attempt < max_retries:
                    logger.warning(
                        f"Failed to initialize sheet, retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                    )
                    time.sleep(retry_delay)
                else:
                    logger.error(
                        f"Failed to initialize sheet after {max_retries} attempts"
                    )
                    return False
        except (socket.timeout, socket.error, ConnectionError) as e:
            if attempt < max_retries:
                logger.warning(f"Connection error initializing sheet: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to initialize sheet after {max_retries} attempts due to connection error: {str(e)}"
                )
                return False
        except Exception as e:
            # Check if the error message indicates a network/DNS issue
            error_str = str(e).lower()
            if any(
                term in error_str
                for term in [
                    "unable to find",
                    "server",
                    "network",
                    "dns",
                    "connection",
                    "timeout",
                    "connect",
                ]
            ):
                if attempt < max_retries:
                    logger.warning(
                        f"Network-related error initializing sheet: {str(e)}"
                    )
                    logger.info(
                        f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                    )
                    time.sleep(retry_delay)
                else:
                    logger.error(
                        f"Failed to initialize sheet after {max_retries} attempts due to network error: {str(e)}"
                    )
                    return False
            else:
                # For non-network related errors, don't retry
                logger.error(f"Unexpected error initializing sheet: {str(e)}")
                return False

    if not sheet_initialized:
        return False

    # Convert lead info to row data
    row_data = lead_to_row(lead_info)

    # Find the lead row with retry logic
    row_num = -1
    for attempt in range(max_retries + 1):
        try:
            row_num = find_lead_row(lead_info.email, lead_info.phone)
            break
        except (socket.timeout, socket.error, ConnectionError) as e:
            if attempt < max_retries:
                logger.warning(f"Connection error finding lead row: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to find lead row after {max_retries} attempts due to connection error: {str(e)}"
                )
                return False
        except Exception as e:
            # Check if the error message indicates a network/DNS issue
            error_str = str(e).lower()
            if any(
                term in error_str
                for term in [
                    "unable to find",
                    "server",
                    "network",
                    "dns",
                    "connection",
                    "timeout",
                    "connect",
                ]
            ):
                if attempt < max_retries:
                    logger.warning(f"Network-related error finding lead row: {str(e)}")
                    logger.info(
                        f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                    )
                    time.sleep(retry_delay)
                else:
                    logger.error(
                        f"Failed to find lead row after {max_retries} attempts due to network error: {str(e)}"
                    )
                    return False
            else:
                # For non-network related errors, don't retry
                logger.error(f"Error finding lead row: {str(e)}")
                return False

    # Update or add the lead with retry logic
    for attempt in range(max_retries + 1):
        try:
            if row_num > 0:
                # Update existing lead
                range_name = f"Leads!A{row_num}:K{row_num}"
                service.spreadsheets().values().update(
                    spreadsheetId=GOOGLE_SHEETS_ID,
                    range=range_name,
                    valueInputOption="RAW",
                    body={"values": [row_data]},
                ).execute()
                logger.info(
                    f"GOOGLE SHEETS: Successfully updated lead in Google Sheet: {lead_info.name} with email {lead_info.email}"
                )
            else:
                # Add new lead
                service.spreadsheets().values().append(
                    spreadsheetId=GOOGLE_SHEETS_ID,
                    range="Leads!A:K",
                    valueInputOption="RAW",
                    insertDataOption="INSERT_ROWS",
                    body={"values": [row_data]},
                ).execute()
                logger.info(
                    f"GOOGLE SHEETS: Successfully added new lead to Google Sheet: {lead_info.name} with email {lead_info.email}"
                )

            return True
        except (
            socket.timeout,
            socket.error,
            ConnectionError,
            googleapiclient.errors.HttpError,
        ) as e:
            if attempt < max_retries:
                logger.warning(f"Connection error updating/adding lead: {str(e)}")
                logger.info(
                    f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                )
                time.sleep(retry_delay)
            else:
                logger.error(
                    f"Failed to update/add lead after {max_retries} attempts due to connection error: {str(e)}"
                )
                return False
        except Exception as e:
            # Check if the error message indicates a network/DNS issue
            error_str = str(e).lower()
            if any(
                term in error_str
                for term in [
                    "unable to find",
                    "server",
                    "network",
                    "dns",
                    "connection",
                    "timeout",
                    "connect",
                ]
            ):
                if attempt < max_retries:
                    logger.warning(
                        f"Network-related error updating/adding lead: {str(e)}"
                    )
                    logger.info(
                        f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{max_retries})..."
                    )
                    time.sleep(retry_delay)
                else:
                    logger.error(
                        f"Failed to update/add lead after {max_retries} attempts due to network error: {str(e)}"
                    )
                    return False
            else:
                # For non-network related errors, don't retry
                logger.error(
                    f"Unexpected error adding/updating lead in Google Sheet: {str(e)}"
                )
                return False

    # This should not be reached, but just in case
    return False
