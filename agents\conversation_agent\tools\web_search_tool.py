import logging
from langchain.tools import tool
from services import perplexity_service

logger = logging.getLogger("web_search_tool")


def create_tool(agent):  # agent param kept for future extensibility
    """Return a web-search tool."""

    @tool
    def web_search(query: str) -> str:
        """Search the web for current information when internal knowledge is insufficient.

        Use this tool when:
        1. rag_search returns information indicating lack of data.
        2. Additional context such as competitor info, trends is required.
        """
        try:
            # Check service availability
            if (
                not getattr(perplexity_service, "perplexity_service", None)
                or not perplexity_service.perplexity_service.is_available()
            ):
                return "Web search is not available at the moment. Please try asking about our specific services instead."

            # Perform search
            result = perplexity_service.search_web(query)
            if result:
                return result
            return "I couldn't find current information about that topic. Please try rephrasing your question or ask about our specific services."
        except Exception as exc:
            logger.error("Error in web_search: %s", exc)
            return "I'm having trouble accessing web information. Please try again or ask about our specific services."

    return web_search
