"""Main API Module - Provides FastAPI endpoints for the chatbot"""

from typing import Optional, Dict, Any
from fastapi import (
    FastAPI,
    HTTPException,
    Request,
    BackgroundTasks,
    UploadFile,
    File,
    Form,
    Body,
    status,
)
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, Field
from supabase import create_client, Client
from openai import OpenAI
import os
import logging
import io
import json
import base64
import PyPDF2
from contextlib import asynccontextmanager
from dotenv import load_dotenv

# Local imports
from database.database_service import *
from database.models import *
from database import DatabaseService
from services import custom_answers_service
from utils.message_processor import *
from utils.utils import sanitize_text
from services.rag_service import *
from services.service_init import initialize_services
from services.candidate_profile_service import CandidateProfileService
from services.candidate_search_service import get_candidate_search_service
from agents import CoordinatorAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Suppress asyncio cleanup warnings (they're not harmful)
logging.getLogger("asyncio").setLevel(logging.CRITICAL)

# Load environment variables
load_dotenv()


# --- Configuration ---
class Config:
    """Application configuration"""

    HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
    if not HOST_DOMAIN:
        raise ValueError("HOST_DOMAIN environment variable must be set")
    HOST_URL = f"https://{HOST_DOMAIN}"

    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
    SUPABASE_URL = os.environ.get("SUPABASE_URL")
    SUPABASE_KEY = os.environ.get("SUPABASE_KEY")
    PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
    PINECONE_ENVIRONMENT = os.environ.get("PINECONE_ENVIRONMENT")
    PINECONE_INDEX_NAME = os.environ.get("PINECONE_INDEX_NAME")
    PERPLEXITY_API_KEY = os.environ.get("PERPLEXITY_API_KEY")

    ALLOWED_ORIGINS = [
        HOST_URL,
        "http://localhost",
        "http://localhost:3000",
        "http://localhost:5000",
        "http://localhost:8000",
        "http://127.0.0.1",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5000",
        "http://127.0.0.1:8000",
        "null",  # Browser sends "null" for file:// protocol
    ]

    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
    ALLOWED_FILE_TYPES = {
        "application/pdf": "pdf",
        "application/msword": "doc",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    }


# --- Initialize Clients ---
config = Config()

# Initialize OpenAI client
openai_client = OpenAI(api_key=config.OPENAI_API_KEY)

# Initialize Supabase client
supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)

# --- Global Variables ---
background_task_status = {"pinecone_refresh": "idle"}


# --- Background Tasks ---
async def generate_candidate_profile_background(cv_assessment_id: int):
    """
    Background task to generate candidate profile after assessment completion.
    This runs asynchronously without blocking the assessment response.
    """
    try:
        logger.info(
            f"Starting background candidate profile generation for assessment {cv_assessment_id}"
        )

        # Initialize the candidate profile service
        db_service = DatabaseService(supabase)
        profile_service = CandidateProfileService(db_service)

        # Generate the candidate profile
        result = await profile_service.generate_candidate_profile(cv_assessment_id)

        if result.get("success"):
            logger.info(
                f"Successfully generated candidate profile for assessment {cv_assessment_id}"
            )
            logger.info(
                f"Profile stored with candidate_id: {result.get('candidate_id')}"
            )
        else:
            logger.error(
                f"Failed to generate candidate profile for assessment {cv_assessment_id}: {result.get('error_message')}"
            )

    except Exception as e:
        logger.error(
            f"Background profile generation failed for assessment {cv_assessment_id}: {str(e)}"
        )


# --- Models ---
class Message(BaseModel):
    """Message model for chat endpoints"""

    content: str
    session_id: str
    host_name: Optional[str] = None


# --- File Processing Functions ---
def process_pdf(contents: bytes) -> str:
    """Extract text from PDF file"""
    pdf_reader = PyPDF2.PdfReader(io.BytesIO(contents))
    return "\n".join(page.extract_text() or "" for page in pdf_reader.pages)


def process_docx(contents: bytes) -> str:
    """Extract text from DOCX file"""
    try:
        from docx import Document as DocxDocument
    except ImportError:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="DOCX support is not installed. Please install python-docx.",
        )

    doc = DocxDocument(io.BytesIO(contents))
    paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
    tables = []
    for table in doc.tables:
        for row in table.rows:
            row_text = [cell.text.strip() for cell in row.cells if cell.text.strip()]
            if row_text:
                tables.append(" | ".join(row_text))

    raw_text = "\n".join(paragraphs + tables)
    segments = [seg.strip() for line in raw_text.split("\n") for seg in line.split("|")]
    seen = set()
    deduped_segments = []
    for seg in segments:
        if seg and seg not in seen:
            deduped_segments.append(seg)
            seen.add(seg)
    return "\n".join(deduped_segments)


def validate_file(file: UploadFile) -> str:
    """Validate uploaded file and return file type"""
    if file.content_type not in config.ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only PDF or Word files (.pdf, .doc, .docx) are allowed.",
        )
    return config.ALLOWED_FILE_TYPES[file.content_type]


# --- Background Tasks ---
async def run_pinecone_refresh_and_update_status(crawl: bool, max_pages: Optional[int]):
    """Background task to refresh Pinecone index"""
    global background_task_status
    from services.pinecone_service import (
        refresh_pinecone_index as local_refresh_pinecone_service,
    )

    try:
        background_task_status["pinecone_refresh"] = "processing"
        logger.info("Background Pinecone refresh task started.")

        success = await local_refresh_pinecone_service(crawl=crawl, max_pages=max_pages)
        background_task_status["pinecone_refresh"] = (
            "completed" if success else "failed"
        )
        logger.info(
            f"Background Pinecone refresh task {'completed' if success else 'failed'}."
        )
    except Exception as e:
        logger.error(f"Error in background Pinecone refresh: {str(e)}", exc_info=True)
        background_task_status["pinecone_refresh"] = "failed"


# --- FastAPI App Setup ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for FastAPI app"""
    logger.info("Application starting up")
    try:
        # Initialize services
        initialize_services()

        # Initialize custom answers
        custom_answers_service.load_custom_answers()
        logger.info("Custom answers service initialized")

        # Reset background task status
        background_task_status["pinecone_refresh"] = "idle"

        yield
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise
    finally:
        # Cleanup
        logger.info("Application shutting down")
        from services.service_init import shutdown_services

        shutdown_services()


app = FastAPI(lifespan=lifespan)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=600,
)

# Mount templates and static files
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")


# --- Routes ---
@app.get("/")
async def root():
    """Root endpoint - redirect to testing dashboard"""
    from fastapi.responses import RedirectResponse

    return RedirectResponse(url="/testing-dashboard")


@app.get("/testing-dashboard", response_class=HTMLResponse)
async def testing_dashboard(request: Request):
    """Main testing dashboard for Phase 2 interfaces"""
    return templates.TemplateResponse("testing_dashboard.html", {"request": request})


@app.get("/cv-assessments/", response_class=HTMLResponse)
async def list_cv_assessments(request: Request):
    """List all CV assessments"""
    assessments = get_all_cv_assessments(supabase)
    return templates.TemplateResponse(
        "cv_assessments_list.html", {"request": request, "assessments": assessments}
    )


@app.get("/cv-assessments/{assessment_id}", response_class=HTMLResponse)
async def get_cv_assessment(request: Request, assessment_id: int):
    """Retrieve a single CV assessment by ID (HTML view)"""
    assessment = get_cv_assessment_by_id(supabase, assessment_id)
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Assessment not found"
        )

    assessment_state = assessment.get("assessment_state")
    try:
        if assessment_state:
            parsed_state = json.loads(assessment_state)
            if isinstance(parsed_state, dict):
                # New format - extract qa_history from the structured data
                assessment_qa = parsed_state.get("qa_history", [])
            else:
                # Old format - assessment_state is directly the qa_history array
                assessment_qa = parsed_state
        else:
            assessment_qa = []
    except Exception:
        assessment_qa = []

    return templates.TemplateResponse(
        "cv_assessment_detail.html",
        {"request": request, "assessment": assessment, "assessment_qa": assessment_qa},
    )


@app.get("/api/cv-assessments/{assessment_id}")
async def get_cv_assessment_json(assessment_id: int):
    """Retrieve a single CV assessment by ID (JSON API)"""
    try:
        assessment = get_cv_assessment_by_id(supabase, assessment_id)
        if not assessment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Assessment not found"
            )

        # Parse assessment state
        assessment_state = assessment.get("assessment_state")
        qa_pairs = []
        try:
            if assessment_state:
                parsed_state = json.loads(assessment_state)
                if isinstance(parsed_state, dict):
                    qa_pairs = parsed_state.get("qa_history", [])
                else:
                    qa_pairs = parsed_state
        except Exception as e:
            logger.warning(
                f"Error parsing assessment state for {assessment_id}: {str(e)}"
            )

        # Return structured JSON response
        return {
            "id": assessment["id"],
            "candidate_name": assessment.get("candidate_name"),
            "email": assessment.get("email"),
            "phone": assessment.get("phone"),
            "location": assessment.get("location"),
            "experience_years": assessment.get("experience_years"),
            "current_salary": assessment.get("current_salary"),
            "expected_salary": assessment.get("expected_salary"),
            "summary": assessment.get("summary"),
            "score": assessment.get("score"),
            "is_complete": assessment.get("is_complete", False),
            "created_at": assessment.get("created_at"),
            "updated_at": assessment.get("updated_at"),
            "tags": assessment.get("tags"),
            "scores": assessment.get("scores"),
            "qa_pairs": qa_pairs,
            "status": "completed" if assessment.get("is_complete") else "in_progress",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CV assessment {assessment_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving assessment: {str(e)}",
        )


# --- Testing Interface Routes ---
@app.get("/candidate-search", response_class=HTMLResponse)
async def candidate_search_interface(request: Request):
    """Candidate search testing interface"""
    return templates.TemplateResponse("candidate_search.html", {"request": request})


@app.get("/job-creation", response_class=HTMLResponse)
async def job_creation_interface(request: Request):
    """Job creation testing interface"""
    return templates.TemplateResponse("job_creation.html", {"request": request})


@app.get("/candidate-profile-viewer", response_class=HTMLResponse)
async def candidate_profile_viewer_interface(request: Request):
    """Candidate profile viewer testing interface"""
    return templates.TemplateResponse("candidate_profile.html", {"request": request})


@app.get("/candidate-profile/{candidate_id}", response_class=HTMLResponse)
async def candidate_profile_interface(request: Request, candidate_id: str):
    """Candidate profile viewer testing interface with ID"""
    return templates.TemplateResponse(
        "candidate_profile.html", {"request": request, "candidate_id": candidate_id}
    )


@app.get("/generate-candidate-profiles/", response_class=HTMLResponse)
async def generate_candidate_profiles_interface(request: Request):
    """Generate candidate profiles interface"""
    return templates.TemplateResponse("generate_profiles.html", {"request": request})


@app.get("/api/generate-candidate-profiles/")
async def generate_candidate_profiles(force_regenerate: bool = False):
    """Generate candidate profiles for existing CV assessments with AI extraction (API)"""
    try:
        from services.candidate_profile_service import CandidateProfileService

        # Get all CV assessments (not just completed ones for testing)
        assessments = get_all_cv_assessments(supabase)

        if not assessments:
            return {
                "status": "info",
                "message": "No assessments found",
                "processed": 0,
            }

        db_service = DatabaseService(supabase)
        profile_service = CandidateProfileService(db_service)
        processed = 0
        skipped = 0
        errors = []

        for assessment in assessments:
            try:
                # Generate and store candidate profile with AI extraction
                profile_result = await profile_service.generate_candidate_profile(
                    cv_assessment_id=assessment["id"], force_regenerate=force_regenerate
                )
                if profile_result:
                    if profile_result.get("status") == "skipped":
                        skipped += 1
                        logger.info(
                            f"Profile already exists for assessment {assessment['id']}, skipped"
                        )
                    elif profile_result.get("success"):
                        processed += 1
                        logger.info(
                            f"Generated profile with AI extraction for assessment {assessment['id']}"
                        )
                    else:
                        errors.append(
                            f"Assessment {assessment['id']}: {profile_result.get('error_message', 'Unknown error')}"
                        )
            except Exception as e:
                error_msg = f"Assessment {assessment['id']}: {str(e)}"
                errors.append(error_msg)
                logger.error(
                    f"Error generating profile for assessment {assessment['id']}: {str(e)}"
                )

        return {
            "status": "success",
            "message": f"Processed {processed} assessments with AI extraction",
            "processed": processed,
            "skipped": skipped,
            "total_assessments": len(assessments),
            "ai_extraction_enabled": True,
            "errors": errors[:5],  # Show first 5 errors only
        }

    except Exception as e:
        logger.error(f"Error generating candidate profiles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating candidate profiles: {str(e)}",
        )


@app.get("/generate-profile/{assessment_id}")
async def generate_single_profile(assessment_id: int, force_regenerate: bool = False):
    """Generate candidate profile for a specific assessment with AI-powered extraction"""
    try:
        from services.candidate_profile_service import CandidateProfileService

        # Check if assessment exists
        assessment = get_cv_assessment_by_id(supabase, assessment_id)
        if not assessment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Assessment {assessment_id} not found",
            )

        db_service = DatabaseService(supabase)
        profile_service = CandidateProfileService(db_service)

        # Generate profile with AI extraction (don't require completion for testing)
        profile_result = await profile_service.generate_candidate_profile(
            cv_assessment_id=assessment_id, force_regenerate=force_regenerate
        )

        if profile_result.get("success"):
            return {
                "status": "success",
                "message": f"Profile generated with AI extraction for assessment {assessment_id}",
                "assessment_id": assessment_id,
                "profile_data": {
                    "candidate_id": profile_result.get("candidate_id"),
                    "profile_length": profile_result.get("profile_length", 0),
                    "completeness_score": profile_result.get("completeness_score", 0),
                    "vector_stored": profile_result.get("vector_stored", False),
                    "metadata": profile_result.get("metadata", {}),
                    "ai_extraction_used": True,
                },
            }
        else:
            return {
                "status": "error",
                "message": profile_result.get(
                    "error_message", "Profile generation failed"
                ),
                "assessment_id": assessment_id,
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error generating profile for assessment {assessment_id}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating profile: {str(e)}",
        )


@app.get("/test-google-sheets/")
async def test_google_sheets():
    """Test the Google Sheets integration"""
    try:
        from database.models import LeadInfo
        from services import google_sheets_service

        test_lead = LeadInfo(
            name="Test Lead API",
            email="<EMAIL>",
            phone="+1234567890",
            company="Test Company",
            industry="Testing",
            consent_given=True,
        )

        if google_sheets_service.add_or_update_lead_in_sheet(test_lead):
            return {
                "status": "success",
                "message": "Test lead added to Google Sheets successfully",
            }
        return {
            "status": "error",
            "message": "Failed to add test lead to Google Sheets",
        }
    except Exception as e:
        logger.error(f"Error testing Google Sheets integration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error testing Google Sheets integration: {str(e)}",
        )


@app.get("/refresh-custom-answers/")
async def refresh_custom_answers():
    """Refresh the custom answers cache"""
    try:
        if custom_answers_service.refresh_custom_answers():
            return {
                "status": "success",
                "message": "Custom answers refreshed successfully",
            }
        return {"status": "error", "message": "Failed to refresh custom answers"}
    except Exception as e:
        logger.error(f"Error refreshing custom answers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error refreshing custom answers: {str(e)}",
        )


@app.get("/refresh-pinecone-index/")
async def refresh_pinecone_index_endpoint(background_tasks: BackgroundTasks):
    """Start Pinecone index refresh"""
    if background_task_status["pinecone_refresh"] == "processing":
        logger.info("Pinecone refresh already in progress.")

    background_task_status["pinecone_refresh"] = "processing"
    background_tasks.add_task(
        run_pinecone_refresh_and_update_status, crawl=True, max_pages=None
    )

    try:
        with open("static/loading.html", "r") as f:
            return HTMLResponse(content=f.read(), status_code=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error serving loading page: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "message": f"Error serving loading page: {str(e)}. Refresh initiated.",
            },
        )


@app.get("/pinecone-refresh-status/")
async def get_pinecone_refresh_status():
    """Get Pinecone refresh status"""
    return JSONResponse(
        content={"status": background_task_status.get("pinecone_refresh", "unknown")}
    )


@app.options("/messages/")
async def options_messages():
    """Handle CORS preflight requests"""
    response = JSONResponse(content={"detail": "OK"})
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type"
    return response


@app.post("/messages/")
async def receive_message(
    background_tasks: BackgroundTasks,
    content: Optional[str] = Form(None),
    session_id: str = Form(...),
    host_name: Optional[str] = Form(None),
    file: Optional[UploadFile] = File(None),
):
    """Handle incoming messages and file uploads"""
    # Create or update conversation
    create_or_update_conversation(
        supabase, session_id, host_name=host_name, language="English"
    )

    # Check for ongoing assessment
    assessment_row = (
        supabase.table("cv_assessments")
        .select("*")
        .eq("session_id", session_id)
        .order("created_at", desc=True)
        .limit(1)
        .execute()
    )
    assessment = assessment_row.data[0] if assessment_row.data else None

    # Handle file upload
    if file:
        contents = await file.read()
        if len(contents) > config.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File size must be less than 5MB.",
            )

        file_type = validate_file(file)

        # Process file based on type
        if file_type == "pdf":
            cv_text = process_pdf(contents)
        elif file_type == "docx":
            cv_text = process_docx(contents)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported file type."
            )

        # Sanitize CV text to remove null bytes and other problematic characters
        sanitized_cv_text = sanitize_text(cv_text)

        # Create CV assessment record first to get ID for tag-based processing
        pdf_base64 = base64.b64encode(contents).decode("utf-8")

        # Create initial assessment record
        initial_assessment = (
            supabase.table("cv_assessments")
            .insert(
                {
                    "session_id": session_id,
                    "pdf_blob": pdf_base64,
                    "pdf_text": sanitized_cv_text,
                    "assessment_state": json.dumps([]),
                    "is_complete": False,
                }
            )
            .execute()
        )

        cv_assessment_id = (
            initial_assessment.data[0]["id"] if initial_assessment.data else None
        )

        # Run CV assessment with the new assessment ID
        db_service = DatabaseService(supabase)
        agent = CoordinatorAgent(
            session_id=session_id, supabase_client=supabase, db_service=db_service
        )

        job_id = None  # This would come from the job application context if available

        assessment_result = agent.run_cv_assessment(
            cv_text=sanitized_cv_text,
            qa_history=[],
            cv_assessment_id=cv_assessment_id,
        )

        # Update assessment result with complete information
        updated_qa_history = assessment_result.get("qa_history", [])

        # Convert any non-serializable objects to dictionaries
        def make_serializable(obj):
            """Recursively convert objects to JSON-serializable format"""
            if obj is None:
                return None
            elif isinstance(obj, (str, int, float, bool)):
                return obj
            elif isinstance(obj, list):
                return [make_serializable(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: make_serializable(value) for key, value in obj.items()}
            else:
                # For any other object type (like CandidateTag), convert to string or skip
                try:
                    # Try to convert to string representation
                    return str(obj)
                except:
                    # If that fails, skip the object
                    return None

        # Preserve remaining_questions and other important metadata
        assessment_state = {
            "qa_history": make_serializable(updated_qa_history),
            "remaining_questions": make_serializable(
                assessment_result.get("remaining_questions", [])
            ),
            "tag_based": assessment_result.get("tag_based", False),
            "template_info": make_serializable(assessment_result.get("template_info")),
            "question_metadata": make_serializable(
                assessment_result.get("question_metadata", [])
            ),
        }

        # Check if assessment is being completed
        is_complete = bool("score" in assessment_result)

        supabase.table("cv_assessments").update(
            {
                "name": assessment_result.get("name"),
                "email": assessment_result.get("email"),
                "assessment_state": json.dumps(assessment_state),
                "is_complete": is_complete,
                "score": assessment_result.get("score"),
                "assessment_description": assessment_result.get("description"),
                "template_id": assessment_result.get("template_id"),
                "experience_level": assessment_result.get("experience_level"),
            }
        ).eq("id", cv_assessment_id).execute()

        # If assessment is complete, trigger background candidate profile generation
        if is_complete and cv_assessment_id:
            logger.info(
                f"Assessment {cv_assessment_id} completed, scheduling candidate profile generation"
            )
            background_tasks.add_task(
                generate_candidate_profile_background, cv_assessment_id
            )

        return {
            "response": assessment_result.get("user_message", "Assessment complete."),
            "question": assessment_result.get("question"),
            "is_complete": is_complete,
        }

    # Handle ongoing assessment
    if assessment and not assessment.get("is_complete"):
        pdf_text = assessment["pdf_text"]
        assessment_state_raw = assessment["assessment_state"] or "{}"

        # Parse assessment state - handle both old format (list) and new format (dict)
        try:
            assessment_state_data = json.loads(assessment_state_raw)
            if isinstance(assessment_state_data, list):
                # Old format - just qa_history
                qa_history = assessment_state_data
                remaining_questions = []
                tag_based = False
                template_info = None
                question_metadata = []
            else:
                # New format - structured data
                qa_history = assessment_state_data.get("qa_history", [])
                remaining_questions = assessment_state_data.get(
                    "remaining_questions", []
                )
                tag_based = assessment_state_data.get("tag_based", False)
                template_info = assessment_state_data.get("template_info")
                question_metadata = assessment_state_data.get("question_metadata", [])
        except:
            qa_history = []
            remaining_questions = []
            tag_based = False
            template_info = None
            question_metadata = []

        # Update answer in QA history
        # Find the most recent question without an answer and add the user's response
        answer_added = False
        for qa in reversed(qa_history):
            if not qa.get("answer"):
                qa["answer"] = content
                answer_added = True
                break

        # If no unanswered question found, this indicates a follow-up question scenario
        # In this case, we should add the answer to the last question that was asked
        if not answer_added and qa_history:
            # This is likely a follow-up question response
            # Add the answer to the most recent question entry
            qa_history[-1]["answer"] = content
            answer_added = True

        # Only add "Additional info" as a last resort if something went wrong
        if not answer_added:
            logger.warning(
                "No question found to attach answer to, this may indicate a flow issue"
            )
            qa_history.append({"question": "Additional info", "answer": content})

        # Add remaining_questions to the last QA entry so the orchestrator can access it
        if qa_history and remaining_questions:
            qa_history[-1]["remaining_questions"] = remaining_questions
            qa_history[-1]["tag_based"] = tag_based
            qa_history[-1]["template_info"] = template_info

        # Continue assessment - agent handles everything automatically
        db_service = DatabaseService(supabase)
        agent = CoordinatorAgent(
            session_id=session_id, supabase_client=supabase, db_service=db_service
        )

        # Get CV assessment ID for tag-based processing
        cv_assessment_id = assessment.get("id")
        template_id = assessment.get("template_id")
        job_id = assessment.get("job_id")

        assessment_result = agent.run_cv_assessment(
            cv_text=pdf_text,
            qa_history=qa_history,
            cv_assessment_id=cv_assessment_id,
            template_id=template_id,
            job_id=job_id,
        )
        updated_qa_history = assessment_result.get("qa_history", qa_history)

        # Convert any non-serializable objects to dictionaries
        def make_serializable(obj):
            """Recursively convert objects to JSON-serializable format"""
            if obj is None:
                return None
            elif isinstance(obj, (str, int, float, bool)):
                return obj
            elif isinstance(obj, list):
                return [make_serializable(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: make_serializable(value) for key, value in obj.items()}
            else:
                # For any other object type (like CandidateTag), convert to string or skip
                try:
                    # Try to convert to string representation
                    return str(obj)
                except:
                    # If that fails, skip the object
                    return None

        # Preserve remaining_questions and other important metadata for continuation
        assessment_state = {
            "qa_history": make_serializable(updated_qa_history),
            "remaining_questions": make_serializable(
                assessment_result.get("remaining_questions", remaining_questions)
            ),
            "tag_based": assessment_result.get("tag_based", tag_based),
            "template_info": make_serializable(
                assessment_result.get("template_info", template_info)
            ),
            "question_metadata": make_serializable(
                assessment_result.get("question_metadata", question_metadata)
            ),
        }

        # Update assessment
        is_complete = bool("score" in assessment_result)
        update_data = {
            "assessment_state": json.dumps(assessment_state),
            "is_complete": is_complete,
            "score": assessment_result.get("score"),
            "assessment_description": assessment_result.get("description"),
        }
        if assessment_result.get("name"):
            update_data["name"] = assessment_result["name"]
        if assessment_result.get("email"):
            update_data["email"] = assessment_result["email"]
        if assessment_result.get("experience_level"):
            update_data["experience_level"] = assessment_result["experience_level"]

        supabase.table("cv_assessments").update(update_data).eq(
            "id", assessment["id"]
        ).execute()

        # If assessment is complete, trigger background candidate profile generation
        if is_complete and assessment.get("id"):
            logger.info(
                f"Assessment {assessment['id']} completed, scheduling candidate profile generation"
            )
            background_tasks.add_task(
                generate_candidate_profile_background, assessment["id"]
            )

        return {
            "response": assessment_result.get("user_message", "Assessment complete."),
            "question": assessment_result.get("question"),
            "is_complete": is_complete,
            "is_rephrased": assessment_result.get("is_rephrased", False),
        }

    # Handle normal chat flow
    return {
        "response": process_message(
            message_content=content,
            session_id=session_id,
            supabase=supabase,
            host_name=host_name,
            host_domain=config.HOST_DOMAIN,
        )
    }


# =====================================================
# PHASE 2 API ENDPOINTS - CANDIDATE SEARCH SYSTEM
# =====================================================


# --- Candidate Search Models ---
class CandidateSearchRequest(BaseModel):
    """Model for candidate search requests"""

    job_description: str = Field(
        ..., description="Job description for semantic matching"
    )
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    top_k: int = Field(20, ge=1, le=100, description="Number of results to return")
    similarity_threshold: float = Field(
        0.6, ge=0.0, le=1.0, description="Minimum similarity score"
    )
    hybrid_mode: bool = Field(True, description="Use hybrid scoring (tag + semantic)")
    tag_weight: float = Field(
        0.4, ge=0.0, le=1.0, description="Weight for tag-based matching"
    )
    semantic_weight: float = Field(
        0.6, ge=0.0, le=1.0, description="Weight for semantic matching"
    )


class AdvancedSearchRequest(BaseModel):
    """Model for advanced candidate search requests"""

    job_description: str = Field(
        ..., description="Job description for semantic matching"
    )
    required_skills: Optional[List[str]] = Field(
        None, description="List of required skills"
    )
    experience_level: Optional[str] = Field(
        None, description="Required experience level"
    )
    min_years_experience: Optional[int] = Field(
        None, ge=0, description="Minimum years of experience"
    )
    education_level: Optional[str] = Field(None, description="Required education level")
    location: Optional[str] = Field(None, description="Required location")
    salary_range: Optional[Dict[str, int]] = Field(
        None, description="Salary range with min/max"
    )
    top_k: int = Field(20, ge=1, le=100, description="Number of results to return")
    similarity_threshold: float = Field(
        0.6, ge=0.0, le=1.0, description="Minimum similarity score"
    )


class SimilarCandidatesRequest(BaseModel):
    """Model for finding similar candidates"""

    reference_candidate_id: str = Field(
        ..., description="ID of the reference candidate"
    )
    top_k: int = Field(
        10, ge=1, le=50, description="Number of similar candidates to return"
    )
    exclude_reference: bool = Field(
        True, description="Exclude reference candidate from results"
    )
    similarity_threshold: float = Field(
        0.7, ge=0.0, le=1.0, description="Minimum similarity score"
    )


# --- Job Management Models ---
class JobCreateRequest(BaseModel):
    """Model for creating a new job"""

    title: str = Field(..., description="Job title")
    description: str = Field(..., description="Job description")
    requirements: Optional[str] = Field(None, description="Job requirements")
    company: Optional[str] = Field(None, description="Company name")
    location: Optional[str] = Field(None, description="Job location")
    department: Optional[str] = Field(None, description="Department")
    job_type: Optional[str] = Field(
        None, description="Job type (full-time, part-time, contract, internship)"
    )
    experience_level: Optional[str] = Field(
        None, description="Experience level (entry, junior, mid, senior, executive)"
    )
    salary_range: Optional[Dict[str, Any]] = Field(
        None, description="Salary range object with min/max"
    )
    required_skills: Optional[List[str]] = Field(
        None, description="List of required skills"
    )
    template_id: Optional[str] = Field(None, description="Scoring template ID")
    custom_weights: Optional[Dict[str, Any]] = Field(
        None, description="Custom scoring weights"
    )
    recruiter_id: Optional[str] = Field(None, description="Recruiter ID")
    assessment_length: Optional[int] = Field(
        None, ge=1, le=50, description="Number of assessment questions"
    )


class JobUpdateRequest(BaseModel):
    """Model for updating a job"""

    title: Optional[str] = Field(None, description="Job title")
    description: Optional[str] = Field(None, description="Job description")
    requirements: Optional[str] = Field(None, description="Job requirements")
    company: Optional[str] = Field(None, description="Company name")
    location: Optional[str] = Field(None, description="Job location")
    department: Optional[str] = Field(None, description="Department")
    job_type: Optional[str] = Field(None, description="Job type")
    experience_level: Optional[str] = Field(None, description="Experience level")
    salary_range: Optional[Dict[str, Any]] = Field(
        None, description="Salary range object with min/max"
    )
    required_skills: Optional[List[str]] = Field(
        None, description="List of required skills"
    )
    template_id: Optional[str] = Field(None, description="Scoring template ID")
    custom_weights: Optional[Dict[str, Any]] = Field(
        None, description="Custom scoring weights"
    )
    assessment_length: Optional[int] = Field(
        None, ge=1, le=50, description="Number of assessment questions"
    )
    is_active: Optional[bool] = Field(None, description="Whether job is active")


class JobCandidateSearchRequest(BaseModel):
    """Model for searching candidates for a specific job"""

    matching_strategy: Optional[str] = Field(
        "hybrid_balanced", description="Matching strategy"
    )
    tag_weight: Optional[float] = Field(
        0.4, ge=0.0, le=1.0, description="Weight for tag-based matching"
    )
    semantic_weight: Optional[float] = Field(
        0.6, ge=0.0, le=1.0, description="Weight for semantic matching"
    )
    similarity_threshold: Optional[float] = Field(
        0.6, ge=0.0, le=1.0, description="Minimum similarity threshold"
    )
    top_k: Optional[int] = Field(
        20, ge=1, le=100, description="Number of candidates to return"
    )
    required_skills: Optional[List[str]] = Field(None, description="Required skills")
    min_years_experience: Optional[int] = Field(
        None, ge=0, description="Minimum years of experience"
    )
    max_years_experience: Optional[int] = Field(
        None, ge=0, description="Maximum years of experience"
    )
    experience_levels: Optional[List[str]] = Field(
        None, description="Accepted experience levels"
    )
    education_levels: Optional[List[str]] = Field(
        None, description="Required education levels"
    )
    locations: Optional[List[str]] = Field(None, description="Accepted locations")
    must_have_tags: Optional[List[str]] = Field(
        None, description="Tags that must be present"
    )
    exclude_tags: Optional[List[str]] = Field(
        None, description="Tags that disqualify candidates"
    )


# --- Candidate Search Endpoints ---
@app.post("/candidates/search")
async def search_candidates(request: CandidateSearchRequest):
    """
    Search for candidates using hybrid approach (semantic + tag-based)

    This endpoint combines semantic vector search with tag-based filtering
    to find the most relevant candidates for a given job description.
    """
    try:
        logger.info(f"Candidate search request: {request.job_description[:100]}...")

        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Perform search
        results = await search_service.search_candidates(
            query_text=request.job_description,
            filters=request.filters,
            top_k=request.top_k,
            similarity_threshold=request.similarity_threshold,
            hybrid_mode=request.hybrid_mode,
            tag_weight=request.tag_weight,
            semantic_weight=request.semantic_weight,
        )

        if results["success"]:
            logger.info(
                f"Search completed successfully: {results['total_results']} candidates found"
            )
            return JSONResponse(status_code=status.HTTP_200_OK, content=results)
        else:
            logger.error(
                f"Search failed: {results.get('error_message', 'Unknown error')}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=results.get("error_message", "Search failed"),
            )

    except Exception as e:
        logger.error(f"Error in candidate search endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@app.post("/candidates/advanced-search")
async def advanced_search_candidates(request: AdvancedSearchRequest):
    """
    Advanced candidate search with structured filters

    This endpoint provides a more structured approach to candidate search
    with specific filters for skills, experience, education, etc.
    """
    try:
        logger.info(f"Advanced search request: {request.job_description[:100]}...")

        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Perform advanced search
        results = await search_service.advanced_search(
            job_description=request.job_description,
            required_skills=request.required_skills,
            experience_level=request.experience_level,
            min_years_experience=request.min_years_experience,
            education_level=request.education_level,
            location=request.location,
            salary_range=request.salary_range,
            top_k=request.top_k,
            similarity_threshold=request.similarity_threshold,
        )

        if results["success"]:
            logger.info(
                f"Advanced search completed: {results['total_results']} candidates found"
            )
            return JSONResponse(status_code=status.HTTP_200_OK, content=results)
        else:
            logger.error(
                f"Advanced search failed: {results.get('error_message', 'Unknown error')}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=results.get("error_message", "Advanced search failed"),
            )

    except Exception as e:
        logger.error(f"Error in advanced search endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@app.get("/candidates/{candidate_id}/similar")
async def find_similar_candidates(
    candidate_id: str,
    top_k: int = 10,
    exclude_reference: bool = True,
    similarity_threshold: float = 0.7,
):
    """
    Find candidates similar to a reference candidate

    This endpoint finds candidates with similar profiles to a given reference candidate
    using semantic similarity in the vector space.
    """
    try:
        logger.info(f"Finding similar candidates to: {candidate_id}")

        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Find similar candidates
        results = await search_service.find_similar_candidates(
            reference_candidate_id=candidate_id,
            top_k=top_k,
            exclude_reference=exclude_reference,
            similarity_threshold=similarity_threshold,
        )

        if results["success"]:
            logger.info(
                f"Similar candidates search completed: {results['total_results']} candidates found"
            )
            return JSONResponse(status_code=status.HTTP_200_OK, content=results)
        else:
            logger.error(
                f"Similar candidates search failed: {results.get('error_message', 'Unknown error')}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=results.get("error_message", "Similar candidates search failed"),
            )

    except Exception as e:
        logger.error(f"Error in similar candidates endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@app.post("/candidates/{candidate_id}/similar")
async def find_similar_candidates_post(
    candidate_id: str, request: SimilarCandidatesRequest
):
    """
    Find candidates similar to a reference candidate (POST version with request body)

    Alternative endpoint that accepts more detailed parameters via request body.
    """
    try:
        logger.info(f"Finding similar candidates to: {candidate_id} (POST)")

        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Find similar candidates
        results = await search_service.find_similar_candidates(
            reference_candidate_id=candidate_id,
            top_k=request.top_k,
            exclude_reference=request.exclude_reference,
            similarity_threshold=request.similarity_threshold,
        )

        if results["success"]:
            logger.info(
                f"Similar candidates search completed: {results['total_results']} candidates found"
            )
            return JSONResponse(status_code=status.HTTP_200_OK, content=results)
        else:
            logger.error(
                f"Similar candidates search failed: {results.get('error_message', 'Unknown error')}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=results.get("error_message", "Similar candidates search failed"),
            )

    except Exception as e:
        logger.error(f"Error in similar candidates POST endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@app.get("/candidates/{candidate_id}")
async def get_candidate_by_id(candidate_id: str):
    """
    Get detailed information about a specific candidate

    This endpoint retrieves comprehensive candidate information including
    profile data, assessment results, and metadata.
    """
    try:
        logger.info(f"Getting candidate details: {candidate_id}")

        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Try to get candidate by ID (could be candidate_id or cv_assessment_id)
        candidate = await search_service.profile_service.get_candidate_by_id(
            candidate_id
        )

        # If not found by candidate_id, try by cv_assessment_id
        if not candidate:
            candidate = (
                await search_service.profile_service.get_candidate_by_assessment_id(
                    candidate_id
                )
            )

        if candidate:
            logger.info(
                f"Candidate found: {candidate.get('candidate_name', 'Unknown')}"
            )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"success": True, "candidate": candidate},
            )
        else:
            logger.warning(f"Candidate not found: {candidate_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Candidate {candidate_id} not found",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get candidate endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@app.get("/candidates/search/statistics")
async def get_search_statistics():
    """
    Get statistics about the candidate search system

    This endpoint provides insights into the search system performance,
    index health, and candidate database statistics.
    """
    try:
        logger.info("Getting search statistics")

        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Get statistics
        stats = search_service.get_search_statistics()

        logger.info("Search statistics retrieved successfully")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"success": True, "statistics": stats},
        )

    except Exception as e:
        logger.error(f"Error getting search statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


# --- Job Management Endpoints ---
@app.get("/jobs")
async def list_jobs(
    active_only: bool = True,
    recruiter_id: Optional[str] = None,
    limit: Optional[int] = None,
):
    """
    List jobs with optional filters

    Args:
        active_only: Whether to return only active jobs
        recruiter_id: Filter by recruiter ID
        limit: Maximum number of jobs to return

    Returns:
        List of jobs with basic information
    """
    try:
        logger.info(
            f"Listing jobs - active_only: {active_only}, recruiter_id: {recruiter_id}, limit: {limit}"
        )

        # Import job service
        from services.job_service import JobService
        from uuid import UUID

        job_service = JobService(supabase)

        # Convert recruiter_id to UUID if provided
        recruiter_uuid = UUID(recruiter_id) if recruiter_id else None

        # Get jobs
        jobs = job_service.get_jobs(
            active_only=active_only, recruiter_id=recruiter_uuid, limit=limit
        )

        # Convert to dict format for JSON response
        jobs_data = []
        for job in jobs:
            job_dict = {
                "id": str(job.id) if job.id else None,
                "title": job.title,
                "description": job.description,
                "requirements": job.requirements,
                "company": job.company,
                "location": job.location,
                "job_type": job.job_type.value if job.job_type else None,
                "experience_level": (
                    job.experience_level.value if job.experience_level else None
                ),
                "salary_range": job.salary_range,
                "template_id": str(job.template_id) if job.template_id else None,
                "custom_weights": job.custom_weights,
                "recruiter_id": str(job.recruiter_id) if job.recruiter_id else None,
                "assessment_length": job.assessment_length,
                "is_active": job.is_active,
                "applications_count": job.applications_count,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "updated_at": job.updated_at.isoformat() if job.updated_at else None,
                "closed_at": job.closed_at.isoformat() if job.closed_at else None,
            }
            jobs_data.append(job_dict)

        return {
            "success": True,
            "total_jobs": len(jobs_data),
            "jobs": jobs_data,
            "filters_applied": {
                "active_only": active_only,
                "recruiter_id": recruiter_id,
                "limit": limit,
            },
        }

    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}",
        )


@app.post("/jobs")
async def create_job(request: JobCreateRequest):
    """
    Create a new job posting

    Args:
        request: Job creation request with job details

    Returns:
        Created job information
    """
    try:
        logger.info(f"Creating job: {request.title}")

        # Import job service
        from services.job_service import JobService
        from uuid import UUID

        job_service = JobService(supabase)

        # Prepare job data (only include fields that exist in database)
        job_data = {
            "title": request.title,
            "description": request.description,
            "requirements": request.requirements,
            "company": request.company,
            "location": request.location,
            "job_type": request.job_type,
            "experience_level": request.experience_level,
            "salary_range": request.salary_range,
            "template_id": (
                str(UUID(request.template_id)) if request.template_id else None
            ),
            "custom_weights": request.custom_weights,
            "recruiter_id": (
                str(UUID(request.recruiter_id)) if request.recruiter_id else None
            ),
            "assessment_length": request.assessment_length,
        }

        # Handle department and required_skills in requirements field
        if request.department or request.required_skills:
            additional_info = []
            if request.department:
                additional_info.append(f"Department: {request.department}")
            if request.required_skills:
                additional_info.append(
                    f"Required Skills: {', '.join(request.required_skills)}"
                )

            if job_data["requirements"]:
                job_data["requirements"] += "\n\n" + "\n".join(additional_info)
            else:
                job_data["requirements"] = "\n".join(additional_info)

        # Remove None values
        job_data = {k: v for k, v in job_data.items() if v is not None}

        # Create job
        created_job = job_service.create_job(job_data)

        if not created_job:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create job"
            )

        # Convert to response format
        job_response = {
            "id": str(created_job.id) if created_job.id else None,
            "title": created_job.title,
            "description": created_job.description,
            "requirements": created_job.requirements,
            "company": created_job.company,
            "location": created_job.location,
            "job_type": created_job.job_type.value if created_job.job_type else None,
            "experience_level": (
                created_job.experience_level.value
                if created_job.experience_level
                else None
            ),
            "salary_range": created_job.salary_range,
            "template_id": (
                str(created_job.template_id) if created_job.template_id else None
            ),
            "custom_weights": created_job.custom_weights,
            "recruiter_id": (
                str(created_job.recruiter_id) if created_job.recruiter_id else None
            ),
            "assessment_length": created_job.assessment_length,
            "is_active": created_job.is_active,
            "applications_count": created_job.applications_count,
            "created_at": (
                created_job.created_at.isoformat() if created_job.created_at else None
            ),
        }

        return {
            "success": True,
            "message": "Job created successfully",
            "job": job_response,
        }

    except ValueError as e:
        logger.error(f"Invalid UUID in job creation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid UUID format: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Error creating job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create job: {str(e)}",
        )


@app.put("/jobs/{job_id}")
async def update_job(job_id: str, request: JobUpdateRequest):
    """
    Update an existing job posting

    Args:
        job_id: Job UUID
        request: Job update request with fields to update

    Returns:
        Success status and updated job information
    """
    try:
        logger.info(f"Updating job: {job_id}")

        # Import job service
        from services.job_service import JobService
        from uuid import UUID

        job_service = JobService(supabase)

        # Validate job_id
        try:
            job_uuid = UUID(job_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid job ID format"
            )

        # Prepare update data (only include non-None values)
        update_data = {}
        if request.title is not None:
            update_data["title"] = request.title
        if request.description is not None:
            update_data["description"] = request.description
        if request.requirements is not None:
            update_data["requirements"] = request.requirements
        if request.company is not None:
            update_data["company"] = request.company
        if request.location is not None:
            update_data["location"] = request.location
        if request.job_type is not None:
            update_data["job_type"] = request.job_type
        if request.experience_level is not None:
            update_data["experience_level"] = request.experience_level
        if request.salary_range is not None:
            update_data["salary_range"] = request.salary_range
        if request.template_id is not None:
            update_data["template_id"] = str(UUID(request.template_id))
        if request.custom_weights is not None:
            update_data["custom_weights"] = request.custom_weights
        if request.assessment_length is not None:
            update_data["assessment_length"] = request.assessment_length
        if request.is_active is not None:
            update_data["is_active"] = request.is_active

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields provided for update",
            )

        # Update job
        success = job_service.update_job(job_uuid, update_data)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found or update failed",
            )

        # Get updated job
        updated_job = job_service.get_job(job_uuid)

        if updated_job:
            job_response = {
                "id": str(updated_job.id) if updated_job.id else None,
                "title": updated_job.title,
                "description": updated_job.description,
                "requirements": updated_job.requirements,
                "company": updated_job.company,
                "location": updated_job.location,
                "job_type": (
                    updated_job.job_type.value if updated_job.job_type else None
                ),
                "experience_level": (
                    updated_job.experience_level.value
                    if updated_job.experience_level
                    else None
                ),
                "salary_range": updated_job.salary_range,
                "template_id": (
                    str(updated_job.template_id) if updated_job.template_id else None
                ),
                "custom_weights": updated_job.custom_weights,
                "recruiter_id": (
                    str(updated_job.recruiter_id) if updated_job.recruiter_id else None
                ),
                "assessment_length": updated_job.assessment_length,
                "is_active": updated_job.is_active,
                "applications_count": updated_job.applications_count,
                "created_at": (
                    updated_job.created_at.isoformat()
                    if updated_job.created_at
                    else None
                ),
                "updated_at": (
                    updated_job.updated_at.isoformat()
                    if updated_job.updated_at
                    else None
                ),
                "closed_at": (
                    updated_job.closed_at.isoformat() if updated_job.closed_at else None
                ),
            }
        else:
            job_response = None

        return {
            "success": True,
            "message": "Job updated successfully",
            "job": job_response,
        }

    except ValueError as e:
        logger.error(f"Invalid UUID in job update: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid UUID format: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Error updating job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update job: {str(e)}",
        )


@app.post("/jobs/{job_id}/candidates")
async def get_job_candidates(job_id: str, request: JobCandidateSearchRequest):
    """
    Get matching candidates for a specific job using dual matching system

    Args:
        job_id: Job UUID
        request: Search configuration and filters

    Returns:
        List of matching candidates with scores and insights
    """
    try:
        logger.info(f"Finding candidates for job: {job_id}")

        # Import services
        from services.dual_matching_service import (
            get_dual_matching_service,
            MatchingStrategy,
            SearchConfiguration,
            MatchingWeights,
            HardRequirements,
        )
        from database.database_service_wrapper import DatabaseService
        from uuid import UUID
        from database.models import JobLevel

        # Validate job_id
        try:
            job_uuid = UUID(job_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid job ID format"
            )

        # Initialize services
        db_service = DatabaseService()
        dual_matching_service = get_dual_matching_service(db_service)

        # Build search configuration
        try:
            strategy = MatchingStrategy(request.matching_strategy)
        except ValueError:
            strategy = MatchingStrategy.HYBRID_BALANCED

        # Create matching weights
        weights = MatchingWeights(
            tag_weight=request.tag_weight, semantic_weight=request.semantic_weight
        )

        # Create hard requirements
        hard_requirements = None
        if any(
            [
                request.required_skills,
                request.min_years_experience,
                request.max_years_experience,
                request.experience_levels,
                request.education_levels,
                request.locations,
                request.must_have_tags,
                request.exclude_tags,
            ]
        ):
            # Convert experience levels to JobLevel enums
            experience_levels = None
            if request.experience_levels:
                try:
                    experience_levels = [
                        JobLevel(level) for level in request.experience_levels
                    ]
                except ValueError as e:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid experience level: {str(e)}",
                    )

            hard_requirements = HardRequirements(
                required_skills=request.required_skills,
                min_years_experience=request.min_years_experience,
                max_years_experience=request.max_years_experience,
                experience_levels=experience_levels,
                education_levels=request.education_levels,
                locations=request.locations,
                must_have_tags=request.must_have_tags,
                exclude_tags=request.exclude_tags,
            )

        # Create search configuration
        config = SearchConfiguration(
            strategy=strategy,
            weights=weights,
            hard_requirements=hard_requirements,
            similarity_threshold=request.similarity_threshold,
            top_k=request.top_k,
            enable_fuzzy_matching=True,
            boost_recent_candidates=False,
            boost_complete_profiles=True,
        )

        # Find matching candidates
        results = await dual_matching_service.find_candidates_for_job(job_uuid, config)

        if not results["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=results.get(
                    "error_message", "Failed to find matching candidates"
                ),
            )

        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error finding candidates for job {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to find candidates: {str(e)}",
        )


# --- Health Check Endpoints ---
@app.get("/health/search")
async def health_check_search():
    """
    Health check for the candidate search system

    This endpoint verifies that all search system components are operational.
    """
    try:
        # Initialize search service
        db_service = DatabaseService(supabase)
        search_service = get_candidate_search_service(db_service)

        # Get basic statistics to verify system health
        stats = search_service.get_search_statistics()

        # Check if vector index is accessible
        vector_stats = stats.get("vector_index_stats", {})
        is_healthy = vector_stats.get("namespace_vectors", 0) >= 0

        return JSONResponse(
            status_code=(
                status.HTTP_200_OK
                if is_healthy
                else status.HTTP_503_SERVICE_UNAVAILABLE
            ),
            content={
                "status": "healthy" if is_healthy else "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "components": {
                    "vector_index": "operational" if vector_stats else "unavailable",
                    "database": "operational",
                    "search_service": "operational",
                },
                "statistics": stats,
            },
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
            },
        )
