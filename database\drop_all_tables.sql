-- Drop All Tables Script
-- WARNING: This will delete ALL data and tables!
-- Run this only if you want to completely reset the database

-- =====================================================
-- DROP TRIGGERS FIRST
-- =====================================================
-- Drop all triggers on tables before dropping the tables
DROP TRIGGER IF EXISTS update_tag_categories_updated_at ON tag_categories;
DROP TRIGGER IF EXISTS update_tag_definitions_updated_at ON tag_definitions;
DROP TRIGGER IF EXISTS update_candidate_tags_updated_at ON candidate_tags;
DROP TRIGGER IF EXISTS update_scoring_templates_updated_at ON scoring_templates;
DROP TRIGGER IF EXISTS update_recruiters_updated_at ON recruiters;
DROP TRIGGER IF EXISTS update_jobs_updated_at ON jobs;
DROP TRIGGER IF EXISTS update_candidate_scores_updated_at ON candidate_scores;
DROP TRIGGER IF EXISTS update_human_assessments_updated_at ON human_assessments;

-- =====================================================
-- DROP INDEXES
-- =====================================================
-- Phase 2 indexes
DROP INDEX IF EXISTS idx_candidate_tags_assessment;
DROP INDEX IF EXISTS idx_candidate_tags_definition;
DROP INDEX IF EXISTS idx_tag_definitions_category;
DROP INDEX IF EXISTS idx_candidate_coverage_assessment;
DROP INDEX IF EXISTS idx_coverage_thresholds_unique;
DROP INDEX IF EXISTS idx_jobs_recruiter;
DROP INDEX IF EXISTS idx_jobs_template;
DROP INDEX IF EXISTS idx_candidate_scores_assessment;
DROP INDEX IF EXISTS idx_human_assessments_assessment;
DROP INDEX IF EXISTS idx_human_assessments_recruiter;

-- Phase 1 indexes
DROP INDEX IF EXISTS idx_messages_session_id;
DROP INDEX IF EXISTS idx_messages_session_created_at;
DROP INDEX IF EXISTS idx_leads_session_id;
DROP INDEX IF EXISTS idx_unique_email;
DROP INDEX IF EXISTS idx_unique_phone;

-- =====================================================
-- DROP TABLES
-- =====================================================
-- Drop Phase 2 tables first (due to foreign key dependencies)
DROP TABLE IF EXISTS human_assessments CASCADE;
DROP TABLE IF EXISTS candidate_scores CASCADE;
DROP TABLE IF EXISTS jobs CASCADE;
DROP TABLE IF EXISTS recruiters CASCADE;
DROP TABLE IF EXISTS scoring_templates CASCADE;
DROP TABLE IF EXISTS coverage_thresholds CASCADE;
DROP TABLE IF EXISTS candidate_tag_coverage CASCADE;
DROP TABLE IF EXISTS candidate_tags CASCADE;
DROP TABLE IF EXISTS tag_definitions CASCADE;
DROP TABLE IF EXISTS tag_categories CASCADE;

-- Drop Phase 1 tables
DROP TABLE IF EXISTS cv_assessments CASCADE;
DROP TABLE IF EXISTS leads CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;

-- =====================================================
-- DROP FUNCTIONS
-- =====================================================
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- =====================================================
-- DROP TYPES/DOMAINS (if any)
-- =====================================================
-- Add any custom types or domains here if created

-- =====================================================
-- VERIFICATION
-- =====================================================
-- After running this script, you can verify everything is dropped by running:
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public';
-- This should return no tables (or only system tables) 