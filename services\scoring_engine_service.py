"""
Scoring Engine Service

Implements the comprehensive scoring system per Business Requirements Document:
1. Weighted component scoring (Education, Experience, Skills, etc.)
2. Template-based score calculation with customizable weights
3. AI + Human score combination with configurable ratios
4. Detailed score breakdown generation and explanations
5. Tag-based scoring integration with hierarchical tag system
"""

import logging
from typing import Dict, Optional, List, Any, Tuple
from uuid import UUID
from decimal import Decimal
from datetime import datetime

from database import database_service
from database.models import (
    CandidateScore,
    ScoringTemplate,
    HumanAssessment,
    CVAssessment,
    CandidateTag,
    TagCategory,
    BackgroundCheckStatus,
    JobLevel,
)
from services.scoring_service import get_scoring_weights
from services.tag_extraction_service import TagExtractionService

logger = logging.getLogger("scoring_engine_service")


class ScoringEngineService:
    """Comprehensive scoring engine implementing BRD scoring requirements"""

    def __init__(self, db_service):
        """Initialize the scoring engine service"""
        self.db_service = db_service
        self.tag_service = TagExtractionService(db_service)

        # Default component weights per BRD
        self.default_ai_weights = {
            "education": 0.20,
            "experience": 0.25,
            "skills": 0.20,
            "achievements": 0.10,
            "certifications": 0.10,
            "formatting": 0.05,
            "recruiter_questions": 0.10,
        }

        # Default human assessment weights
        self.default_human_weights = {
            "case_study": 0.50,
            "psychometric": 0.30,
            "background_check": 0.20,
        }

    async def calculate_comprehensive_score(
        self,
        cv_assessment_id: UUID,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
        human_assessment_id: Optional[UUID] = None,
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive candidate score combining AI and human components.

        Args:
            cv_assessment_id: The CV assessment to score
            template_id: Optional scoring template to use
            job_id: Optional job context for custom weights
            human_assessment_id: Optional human assessment data

        Returns:
            Complete scoring breakdown with final weighted score
        """
        try:
            # Get assessment data
            assessment = await self._get_assessment_data(cv_assessment_id)
            if not assessment:
                raise ValueError(f"CV Assessment {cv_assessment_id} not found")

            # Get scoring template/weights
            scoring_template = await self._get_scoring_template(template_id, job_id)

            # Calculate AI component scores
            ai_scores = await self._calculate_ai_scores(
                cv_assessment_id, scoring_template, assessment
            )

            # Calculate human component scores
            human_scores = await self._calculate_human_scores(
                human_assessment_id if human_assessment_id else None
            )

            # Calculate weighted totals
            ai_total = self._calculate_weighted_total(ai_scores, "ai")
            human_total = self._calculate_weighted_total(human_scores, "human")

            # Calculate final combined score
            final_score = self._calculate_final_score(
                ai_total, human_total, scoring_template
            )

            # Generate detailed breakdown
            score_breakdown = self._generate_score_breakdown(
                ai_scores,
                human_scores,
                ai_total,
                human_total,
                final_score,
                scoring_template,
            )

            # Store results in database
            candidate_score = await self._store_candidate_score(
                cv_assessment_id,
                job_id,
                template_id,
                ai_scores,
                human_scores,
                ai_total,
                human_total,
                final_score,
                score_breakdown,
            )

            return {
                "success": True,
                "candidate_score_id": candidate_score.id if candidate_score else None,
                "ai_total_score": float(ai_total),
                "human_total_score": float(human_total),
                "final_weighted_score": float(final_score),
                "score_breakdown": score_breakdown,
                "ai_component_scores": ai_scores,
                "human_component_scores": human_scores,
                "template_used": (
                    scoring_template.name if scoring_template else "default"
                ),
            }

        except Exception as e:
            logger.error(f"Error calculating comprehensive score: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "ai_total_score": 0.0,
                "human_total_score": 0.0,
                "final_weighted_score": 0.0,
            }

    async def _get_assessment_data(
        self, cv_assessment_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Retrieve assessment data from database"""
        try:
            # Get basic assessment info
            assessments = self.db_service.get_cv_assessments(
                self.db_service.supabase_client
            )

            assessment = None
            for a in assessments:
                if str(a.get("id")) == str(cv_assessment_id):
                    assessment = a
                    break

            if not assessment:
                return None

            # Get candidate tags
            tags = self.db_service.get_candidate_tags_by_assessment(
                self.db_service.supabase_client, cv_assessment_id
            )

            assessment["candidate_tags"] = tags
            return assessment

        except Exception as e:
            logger.error(f"Error getting assessment data: {str(e)}")
            return None

    async def _get_scoring_template(
        self, template_id: Optional[UUID] = None, job_id: Optional[UUID] = None
    ) -> Optional[ScoringTemplate]:
        """Get scoring template with fallback to default"""
        try:
            if template_id:
                template_data = self.db_service.get_scoring_template(
                    self.db_service.supabase_client, template_id
                )
                if template_data:
                    return ScoringTemplate(**template_data)

            # Fallback to default template
            return self._create_default_template()

        except Exception as e:
            logger.error(f"Error getting scoring template: {str(e)}")
            return self._create_default_template()

    def _create_default_template(self) -> ScoringTemplate:
        """Create default scoring template per BRD"""
        return ScoringTemplate(
            name="Default Platform Template",
            description="Default scoring weights per BRD",
            cv_analysis_weight=Decimal("0.60"),
            questionnaire_weight=Decimal("0.30"),
            case_study_weight=Decimal("0.50"),
            psychometric_weight=Decimal("0.30"),
            background_check_weight=Decimal("0.20"),
            ai_percentage=Decimal("0.60"),
            human_percentage=Decimal("0.40"),
            category_weights={
                "education": 0.20,
                "experience": 0.25,
                "skills": 0.20,
                "achievements": 0.10,
                "certifications": 0.10,
                "formatting": 0.05,
                "recruiter_questions": 0.10,
            },
        )

    async def _calculate_ai_scores(
        self,
        cv_assessment_id: UUID,
        template: ScoringTemplate,
        assessment: Dict[str, Any],
    ) -> Dict[str, float]:
        """Calculate AI component scores based on tags and CV analysis"""
        ai_scores = {}

        try:
            # Get candidate tags grouped by category
            candidate_tags = assessment.get("candidate_tags", [])
            tags_by_category = {}

            for tag in candidate_tags:
                category_name = tag.get("category_name", "unknown")
                if category_name not in tags_by_category:
                    tags_by_category[category_name] = []
                tags_by_category[category_name].append(tag)

            # Score each category based on tag coverage and quality
            category_weights = template.category_weights

            for category, weight in category_weights.items():
                category_tags = tags_by_category.get(category, [])
                category_score = await self._score_category_tags(
                    category, category_tags, cv_assessment_id
                )
                ai_scores[category] = category_score

            # Add formatting score (basic CV quality assessment)
            ai_scores["formatting"] = self._score_cv_formatting(assessment)

            # Add recruiter questions score
            ai_scores["recruiter_questions"] = self._score_recruiter_questions(
                assessment
            )

            return ai_scores

        except Exception as e:
            logger.error(f"Error calculating AI scores: {str(e)}")
            return {category: 0.0 for category in self.default_ai_weights.keys()}

    async def _score_category_tags(
        self, category: str, tags: List[Dict[str, Any]], cv_assessment_id: UUID
    ) -> float:
        """Score a category based on tag coverage and quality"""
        try:
            if not tags:
                return 0.0

            # Get coverage analysis for this category
            coverage_data = await self.tag_service.calculate_coverage(cv_assessment_id)
            category_coverage = coverage_data.get("category_coverage", {})

            # Find coverage for this category
            coverage_percentage = 0.0
            for cat_data in category_coverage.values():
                if cat_data.get("category_name", "").lower() == category.lower():
                    coverage_percentage = cat_data.get("coverage_percentage", 0.0)
                    break

            # Base score from coverage (0-80 points)
            base_score = min(80.0, coverage_percentage * 0.8)

            # Quality bonus based on confidence scores (0-20 points)
            total_confidence = sum(tag.get("confidence_score", 0.0) for tag in tags)
            avg_confidence = total_confidence / len(tags) if tags else 0.0
            quality_bonus = avg_confidence * 20.0

            final_score = min(100.0, base_score + quality_bonus)
            return final_score

        except Exception as e:
            logger.error(f"Error scoring category {category}: {str(e)}")
            return 0.0

    def _score_cv_formatting(self, assessment: Dict[str, Any]) -> float:
        """Score CV formatting and presentation quality"""
        try:
            pdf_text = assessment.get("pdf_text", "")
            if not pdf_text:
                return 0.0

            score = 50.0  # Base score

            # Length check (appropriate amount of content)
            word_count = len(pdf_text.split())
            if 200 <= word_count <= 2000:
                score += 20.0
            elif word_count < 100:
                score -= 20.0

            # Structure indicators
            structure_keywords = [
                "experience",
                "education",
                "skills",
                "summary",
                "objective",
                "achievements",
                "certifications",
            ]
            found_keywords = sum(
                1
                for keyword in structure_keywords
                if keyword.lower() in pdf_text.lower()
            )
            score += min(20.0, found_keywords * 3.0)

            # Professional language indicators
            professional_indicators = [
                "responsible for",
                "managed",
                "developed",
                "implemented",
                "achieved",
                "collaborated",
                "led",
                "coordinated",
            ]
            found_professional = sum(
                1
                for phrase in professional_indicators
                if phrase.lower() in pdf_text.lower()
            )
            score += min(10.0, found_professional * 2.0)

            return min(100.0, max(0.0, score))

        except Exception as e:
            logger.error(f"Error scoring CV formatting: {str(e)}")
            return 50.0

    def _score_recruiter_questions(self, assessment: Dict[str, Any]) -> float:
        """Score responses to recruiter bot questions"""
        try:
            assessment_state = assessment.get("assessment_state")
            if not assessment_state:
                return 0.0

            # Parse Q&A history from assessment state
            import json

            qa_history = (
                json.loads(assessment_state)
                if isinstance(assessment_state, str)
                else assessment_state
            )

            if not qa_history or not isinstance(qa_history, list):
                return 0.0

            # Count answered questions and evaluate quality
            answered_questions = 0
            total_length = 0

            for qa in qa_history:
                if isinstance(qa, dict) and qa.get("answer"):
                    answered_questions += 1
                    answer_length = len(qa["answer"].split())
                    total_length += answer_length

            if answered_questions == 0:
                return 0.0

            # Base score from completion
            completion_score = min(60.0, (answered_questions / 10.0) * 60.0)

            # Quality score from answer length and detail
            avg_length = total_length / answered_questions
            quality_score = min(40.0, (avg_length / 10.0) * 40.0)

            return min(100.0, completion_score + quality_score)

        except Exception as e:
            logger.error(f"Error scoring recruiter questions: {str(e)}")
            return 0.0

    async def _calculate_human_scores(
        self, human_assessment_id: Optional[UUID] = None
    ) -> Dict[str, float]:
        """Calculate human assessment component scores"""
        human_scores = {"case_study": 0.0, "psychometric": 0.0, "background_check": 0.0}

        if not human_assessment_id:
            return human_scores

        try:
            # Get human assessment data
            human_assessment = self.db_service.get_human_assessment(
                self.db_service.supabase_client, human_assessment_id
            )

            if not human_assessment:
                return human_scores

            # Case study score
            if human_assessment.get("case_study_score") is not None:
                human_scores["case_study"] = float(human_assessment["case_study_score"])

            # Psychometric score (average of all psychometric components)
            psych_scores = []
            for field in ["technical_score", "soft_skills_score", "cultural_fit_score"]:
                if human_assessment.get(field) is not None:
                    psych_scores.append(float(human_assessment[field]))

            if psych_scores:
                human_scores["psychometric"] = sum(psych_scores) / len(psych_scores)

            # Background check score
            bg_status = human_assessment.get("background_check_status")
            if bg_status == BackgroundCheckStatus.PASSED:
                human_scores["background_check"] = 100.0
            elif bg_status == BackgroundCheckStatus.FLAGGED:
                human_scores["background_check"] = 50.0
            elif bg_status == BackgroundCheckStatus.FAILED:
                human_scores["background_check"] = 0.0
            # PENDING remains 0.0

            return human_scores

        except Exception as e:
            logger.error(f"Error calculating human scores: {str(e)}")
            return human_scores

    def _calculate_weighted_total(
        self, component_scores: Dict[str, float], score_type: str
    ) -> Decimal:
        """Calculate weighted total for AI or human components"""
        try:
            if score_type == "ai":
                weights = self.default_ai_weights
            else:
                weights = self.default_human_weights

            total_weighted = 0.0
            total_weight = 0.0

            for component, score in component_scores.items():
                weight = weights.get(component, 0.0)
                total_weighted += score * weight
                total_weight += weight

            # Normalize if weights don't sum to 1.0
            if total_weight > 0:
                final_score = total_weighted / total_weight
            else:
                final_score = 0.0

            return Decimal(str(round(final_score, 2)))

        except Exception as e:
            logger.error(f"Error calculating weighted total: {str(e)}")
            return Decimal("0.0")

    def _calculate_final_score(
        self, ai_total: Decimal, human_total: Decimal, template: ScoringTemplate
    ) -> Decimal:
        """Calculate final weighted score combining AI and human components"""
        try:
            ai_weight = template.ai_percentage
            human_weight = template.human_percentage

            final_score = (float(ai_total) * float(ai_weight)) + (
                float(human_total) * float(human_weight)
            )

            return Decimal(str(round(final_score, 2)))

        except Exception as e:
            logger.error(f"Error calculating final score: {str(e)}")
            return Decimal("0.0")

    def _generate_score_breakdown(
        self,
        ai_scores: Dict[str, float],
        human_scores: Dict[str, float],
        ai_total: Decimal,
        human_total: Decimal,
        final_score: Decimal,
        template: ScoringTemplate,
    ) -> Dict[str, Any]:
        """Generate detailed score breakdown and explanation"""
        try:
            breakdown = {
                "ai_components": {},
                "human_components": {},
                "summary": {},
                "explanation": [],
            }

            # AI components breakdown
            for component, score in ai_scores.items():
                weight = self.default_ai_weights.get(component, 0.0)
                weighted_contribution = score * weight

                breakdown["ai_components"][component] = {
                    "raw_score": round(score, 2),
                    "weight": round(weight * 100, 1),
                    "weighted_score": round(weighted_contribution, 2),
                    "max_possible": round(100 * weight, 2),
                }

            # Human components breakdown
            for component, score in human_scores.items():
                weight = self.default_human_weights.get(component, 0.0)
                weighted_contribution = score * weight

                breakdown["human_components"][component] = {
                    "raw_score": round(score, 2),
                    "weight": round(weight * 100, 1),
                    "weighted_score": round(weighted_contribution, 2),
                    "max_possible": round(100 * weight, 2),
                }

            # Summary
            breakdown["summary"] = {
                "ai_total_score": float(ai_total),
                "ai_weight_in_final": float(template.ai_percentage) * 100,
                "ai_contribution_to_final": float(ai_total)
                * float(template.ai_percentage),
                "human_total_score": float(human_total),
                "human_weight_in_final": float(template.human_percentage) * 100,
                "human_contribution_to_final": float(human_total)
                * float(template.human_percentage),
                "final_weighted_score": float(final_score),
            }

            # Generate explanation
            breakdown["explanation"] = self._generate_explanation(
                ai_scores, human_scores, final_score
            )

            return breakdown

        except Exception as e:
            logger.error(f"Error generating score breakdown: {str(e)}")
            return {"error": str(e)}

    def _generate_explanation(
        self,
        ai_scores: Dict[str, float],
        human_scores: Dict[str, float],
        final_score: Decimal,
    ) -> List[str]:
        """Generate human-readable explanation of the scoring"""
        explanations = []

        try:
            # Overall performance
            score_value = float(final_score)
            if score_value >= 85:
                explanations.append(
                    "Excellent overall performance across all assessment areas."
                )
            elif score_value >= 70:
                explanations.append(
                    "Good overall performance with some areas for improvement."
                )
            elif score_value >= 55:
                explanations.append(
                    "Average performance with several areas needing improvement."
                )
            else:
                explanations.append(
                    "Below average performance requiring significant improvement."
                )

            # AI component highlights
            ai_strengths = [comp for comp, score in ai_scores.items() if score >= 80]
            ai_weaknesses = [comp for comp, score in ai_scores.items() if score < 60]

            if ai_strengths:
                explanations.append(f"Strong performance in: {', '.join(ai_strengths)}")
            if ai_weaknesses:
                explanations.append(
                    f"Areas for improvement: {', '.join(ai_weaknesses)}"
                )

            # Human assessment insights
            if any(score > 0 for score in human_scores.values()):
                human_strengths = [
                    comp for comp, score in human_scores.items() if score >= 80
                ]
                if human_strengths:
                    explanations.append(
                        f"Excellent human assessment results in: {', '.join(human_strengths)}"
                    )
            else:
                explanations.append(
                    "Human assessment pending - score based on AI analysis only."
                )

            return explanations

        except Exception as e:
            logger.error(f"Error generating explanation: {str(e)}")
            return ["Score calculated successfully."]

    async def _store_candidate_score(
        self,
        cv_assessment_id: UUID,
        job_id: Optional[UUID],
        template_id: Optional[UUID],
        ai_scores: Dict[str, float],
        human_scores: Dict[str, float],
        ai_total: Decimal,
        human_total: Decimal,
        final_score: Decimal,
        score_breakdown: Dict[str, Any],
    ) -> Optional[CandidateScore]:
        """Store calculated scores in database"""
        try:
            # Prepare score data
            score_data = {
                "cv_assessment_id": str(cv_assessment_id),
                "job_id": str(job_id) if job_id else None,
                "template_id": str(template_id) if template_id else None,
                "cv_analysis_score": ai_scores.get(
                    "experience", 0.0
                ),  # Map to closest component
                "questionnaire_score": ai_scores.get("recruiter_questions", 0.0),
                "case_study_score": human_scores.get("case_study"),
                "psychometric_score": human_scores.get("psychometric"),
                "background_check_score": human_scores.get("background_check"),
                "tag_scores": ai_scores,
                "ai_total_score": float(ai_total),
                "human_total_score": float(human_total),
                "final_weighted_score": float(final_score),
                "score_breakdown": score_breakdown,
                "calculated_at": datetime.utcnow().isoformat(),
            }

            # Store in database
            result = self.db_service.create_candidate_score(
                self.db_service.supabase_client, score_data
            )

            if result:
                return CandidateScore(**result)
            return None

        except Exception as e:
            logger.error(f"Error storing candidate score: {str(e)}")
            return None

    async def recalculate_score(
        self, cv_assessment_id: UUID, force_refresh: bool = False
    ) -> Dict[str, Any]:
        """Recalculate scores for an existing assessment"""
        try:
            # Get existing score record
            existing_score = self.db_service.get_candidate_score_by_assessment(
                self.db_service.supabase_client, cv_assessment_id
            )

            template_id = None
            job_id = None
            human_assessment_id = None

            if existing_score:
                template_id = existing_score.get("template_id")
                job_id = existing_score.get("job_id")
                # Try to find human assessment
                human_assessments = self.db_service.get_human_assessments_by_assessment(
                    self.db_service.supabase_client, cv_assessment_id
                )
                if human_assessments:
                    human_assessment_id = human_assessments[0].get("id")

            # Recalculate comprehensive score
            result = await self.calculate_comprehensive_score(
                cv_assessment_id,
                UUID(template_id) if template_id else None,
                UUID(job_id) if job_id else None,
                UUID(human_assessment_id) if human_assessment_id else None,
            )

            result["recalculated"] = True
            result["previous_score"] = (
                existing_score.get("final_weighted_score") if existing_score else None
            )

            return result

        except Exception as e:
            logger.error(f"Error recalculating score: {str(e)}")
            return {"success": False, "error": str(e), "recalculated": False}

    async def get_score_summary(self, cv_assessment_id: UUID) -> Dict[str, Any]:
        """Get score summary for a candidate assessment"""
        try:
            # Get stored score
            candidate_score = self.db_service.get_candidate_score_by_assessment(
                self.db_service.supabase_client, cv_assessment_id
            )

            if not candidate_score:
                return {"success": False, "error": "Score not found"}

            # Get assessment details
            assessment = await self._get_assessment_data(cv_assessment_id)

            return {
                "success": True,
                "candidate_score": candidate_score,
                "candidate_name": assessment.get("name") if assessment else "Unknown",
                "calculation_date": candidate_score.get("calculated_at"),
                "score_breakdown": candidate_score.get("score_breakdown", {}),
                "final_score": candidate_score.get("final_weighted_score", 0.0),
            }

        except Exception as e:
            logger.error(f"Error getting score summary: {str(e)}")
            return {"success": False, "error": str(e)}
