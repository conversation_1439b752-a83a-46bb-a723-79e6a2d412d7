"""
Email Service Module

This module provides functionality for sending email notifications
about new leads to the sales team using SendGrid, as well as
follow-up emails to leads who provide their email address.
"""

import os
import time
import logging
from typing import List
from dotenv import load_dotenv
import sendgrid
from sendgrid.helpers.mail import Mail, Email, To, HtmlContent
import requests.exceptions

# Import shared models
from database.models import LeadInfo
from utils.background_tasks import run_in_background

# Will be imported when needed
google_sheets_service = None

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("email_service")

# Load environment variables
load_dotenv()

# Get SendGrid configuration from environment variables
SENDGRID_API_KEY = os.environ.get("SENDGRID_API_KEY")
SALES_TEAM_EMAIL = os.environ.get("SALES_TEAM_EMAIL")
EMAIL_FROM_ADDRESS = os.environ.get("EMAIL_FROM_ADDRESS")
HOST_DOMAIN = os.environ.get("HOST_DOMAIN")
# Get Arabic company name from environment variables or use a default
HOST_DOMAIN_ARABIC = os.environ.get("HOST_DOMAIN_ARABIC")


def format_lead_email_html(lead_info: LeadInfo) -> str:
    """
    Format lead information as HTML for email content.

    Args:
        lead_info: The lead information to format

    Returns:
        HTML formatted string with lead details
    """
    # Start with a basic HTML structure
    html = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            h1 {{ color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 10px; }}
            .lead-info {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; }}
            .field {{ margin-bottom: 10px; }}
            .label {{ font-weight: bold; }}
            .value {{ margin-left: 10px; }}
            .footer {{ margin-top: 20px; font-size: 12px; color: #777; text-align: center; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>New Lead Notification</h1>
            <p>A new lead has been captured on {HOST_DOMAIN}:</p>

            <div class="lead-info">
    """

    # Add each field if it exists
    fields = [
        ("Name", lead_info.name),
        ("Email", lead_info.email),
        ("Phone", lead_info.phone),
        ("Company", lead_info.company),
        ("Industry", lead_info.industry),
        ("Number of Locations", lead_info.num_locations),
        ("Number of Employees", lead_info.num_employees),
        ("Additional Message", lead_info.additional_message),
        ("Consent Given", "Yes" if lead_info.consent_given else "No"),
    ]

    for label, value in fields:
        if value is not None and value != "":
            html += f"""
                <div class="field">
                    <span class="label">{label}:</span>
                    <span class="value">{value}</span>
                </div>
            """

    # Close the HTML structure
    html += """
            </div>

            <p>Please follow up with this lead as soon as possible.</p>

            <div class="footer">
                <p>This is an automated notification from your website's chatbot.</p>
            </div>
        </div>
    </body>
    </html>
    """

    return html


def get_recipient_emails(max_retries: int = 3, retry_delay: int = 5) -> List[str]:
    """
    Get the list of sales team email addresses from Google Sheets configuration.
    Implements multiple retry attempts before falling back to environment variable.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        List of email addresses
    """
    global google_sheets_service

    # Lazy import to avoid circular imports
    if google_sheets_service is None:
        try:
            from services import google_sheets_service as gss

            google_sheets_service = gss
        except ImportError:
            logger.error("Google Sheets service module not available")
            # Fall back to environment variable
            env_email = os.environ.get("SALES_TEAM_EMAIL", "")
            return [env_email] if env_email else []

    # Implement retry logic for getting emails from Google Sheets
    for attempt in range(max_retries + 1):
        try:
            # Try to get emails from Google Sheets
            emails = google_sheets_service.get_sales_team_emails()
            if emails:
                logger.info(
                    f"Successfully retrieved {len(emails)} email(s) from Google Sheets"
                )
                return emails
            else:
                logger.warning("No emails found in Google Sheets")

                # If this is the last attempt, fall back to environment variable
                if attempt >= max_retries:
                    logger.warning(
                        f"No emails found after {max_retries + 1} attempts, falling back to environment variable"
                    )
                    break

                # Otherwise, retry after initializing the sheet again
                logger.info(
                    f"Attempting to initialize sales emails sheet (attempt {attempt + 1}/{max_retries + 1})"
                )
                try:
                    google_sheets_service.initialize_sales_emails_sheet()
                except Exception as init_error:
                    logger.warning(
                        f"Error initializing sales emails sheet: {str(init_error)}"
                    )

                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)

        except Exception as e:
            # Check if the error message indicates a network/DNS issue
            error_str = str(e).lower()
            error_type = (
                "Network-related"
                if any(
                    term in error_str
                    for term in [
                        "unable to find",
                        "server",
                        "network",
                        "dns",
                        "connection",
                        "timeout",
                        "connect",
                        "unreachable",
                        "refused",
                    ]
                )
                else "General"
            )

            # Log the error
            logger.warning(
                f"{error_type} error getting sales team emails from Google Sheets (attempt {attempt + 1}/{max_retries + 1}): {str(e)}"
            )

            # If this is the last attempt, fall back to environment variable
            if attempt >= max_retries:
                logger.error(
                    f"Failed to get emails after {max_retries + 1} attempts, falling back to environment variable"
                )
                break

            # Otherwise, retry
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)

    # Fall back to environment variable after all retries have failed
    env_email = os.environ.get("SALES_TEAM_EMAIL", "")
    if env_email:
        logger.info(f"Using fallback email from environment variable: {env_email}")
        return [env_email]
    else:
        logger.warning("No fallback email found in environment variable")
        return []


@run_in_background
def send_lead_notification(
    lead_info: LeadInfo, max_retries: int = 3, retry_delay: int = 5
) -> bool:
    """
    Send an email notification about a new lead to the sales team.
    This function runs in a background thread and includes retry logic for transient failures.

    Args:
        lead_info: The lead information to send
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        True if the email was sent successfully, False otherwise
    """
    if not SENDGRID_API_KEY or not EMAIL_FROM_ADDRESS:
        logger.error(
            "SendGrid configuration is incomplete. Check environment variables."
        )
        return False

    # Get recipient emails from Google Sheets or environment variable
    recipients = get_recipient_emails()

    if not recipients:
        logger.error("No sales team email addresses configured")
        return False

    # Create the email content
    html_content = format_lead_email_html(lead_info)

    # Initialize SendGrid client
    sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)

    # Track if at least one email was sent successfully
    any_success = False

    # Send to each recipient
    for recipient in recipients:
        # Create the email message for this recipient
        message = Mail(
            from_email=Email(EMAIL_FROM_ADDRESS),
            to_emails=To(recipient),
            subject=f"New Lead from {HOST_DOMAIN}: {lead_info.name}",
            html_content=HtmlContent(html_content),
        )

        # Implement retry logic for this recipient
        retries = 0
        while retries <= max_retries:
            try:
                # Send the email
                response = sg.send(message)

                # Check response status
                if response.status_code >= 200 and response.status_code < 300:
                    logger.info(
                        f"Email notification sent successfully to {recipient} for lead: {lead_info.name}"
                    )
                    any_success = True
                    break  # Success for this recipient, move to next
                else:
                    # Log the error
                    logger.warning(
                        f"Failed to send email notification to {recipient}. Status code: {response.status_code}"
                    )

                    # If we've reached max retries, give up on this recipient
                    if retries >= max_retries:
                        logger.error(
                            f"Failed to send email to {recipient} after {max_retries} attempts. Final status code: {response.status_code}"
                        )
                        break  # Move to next recipient

                    # Otherwise, retry
                    retries += 1
                    logger.info(
                        f"Retrying email send to {recipient} (attempt {retries}/{max_retries}) in {retry_delay} seconds..."
                    )
                    time.sleep(retry_delay)

            except requests.exceptions.ConnectionError as e:
                # Handle connection errors (network issues)
                if retries >= max_retries:
                    logger.error(
                        f"Failed to send email to {recipient} after {max_retries} attempts due to connection error: {str(e)}"
                    )
                    break  # Move to next recipient

                retries += 1
                logger.warning(
                    f"Connection error when sending email to {recipient}: {str(e)}"
                )
                logger.info(
                    f"Retrying email send (attempt {retries}/{max_retries}) in {retry_delay} seconds..."
                )
                time.sleep(retry_delay)

            except Exception as e:
                # Check if the error message indicates a network/DNS issue
                error_str = str(e).lower()
                if any(
                    term in error_str
                    for term in [
                        "unable to find",
                        "server",
                        "network",
                        "dns",
                        "connection",
                        "timeout",
                        "connect",
                        "unreachable",
                        "refused",
                    ]
                ):
                    # This is likely a network-related error, so retry
                    if retries >= max_retries:
                        logger.error(
                            f"Failed to send email to {recipient} after {max_retries} attempts due to network error: {str(e)}"
                        )
                        break  # Move to next recipient

                    retries += 1
                    logger.warning(
                        f"Network-related error when sending email to {recipient}: {str(e)}"
                    )
                    logger.info(
                        f"Retrying email send (attempt {retries}/{max_retries}) in {retry_delay} seconds..."
                    )
                    time.sleep(retry_delay)
                else:
                    # For non-network related exceptions, log and move to next recipient
                    logger.error(
                        f"Error sending email notification to {recipient}: {str(e)}"
                    )
                    break  # Move to next recipient

    # Return True if at least one email was sent successfully
    return any_success


def format_lead_followup_email_html(
    lead_info: LeadInfo, language: str = "English"
) -> str:
    """
    Format a follow-up email to send to a lead.

    Args:
        lead_info: The lead information
        language: The language to use (default: English)

    Returns:
        HTML formatted string with the follow-up message
    """
    # Determine the greeting based on language
    is_arabic = language.lower() == "arabic"

    if is_arabic:
        greeting = f"مرحبًا {lead_info.name}،"
        company_name = HOST_DOMAIN_ARABIC
        signature = "أمين"
        message = f"""
        <p>{greeting}</p>
        <p>شكرًا لاهتمامك بخدمات {company_name}. لقد تلقينا معلوماتك وسيتواصل معك أحد أعضاء فريقنا قريبًا لمناقشة كيف يمكننا مساعدتك.</p>
        <p>إذا كانت لديك أي أسئلة فورية، فلا تتردد في الرد على هذا البريد الإلكتروني.</p>
        <p>نتطلع إلى التحدث معك قريبًا!</p>
        <p>مع أطيب التحيات،<br>{signature}<br>فريق خدمة العملاء<br>{company_name}</p>
        """
        footer_text = f"هذه رسالة آلية من {company_name}."
    else:
        greeting = f"Hello {lead_info.name},"
        company_name = HOST_DOMAIN
        signature = "Amin"
        message = f"""
        <p>{greeting}</p>
        <p>Thank you for your interest in {company_name} services. We have received your information and a member of our team will contact you soon to discuss how we can assist you.</p>
        <p>If you have any immediate questions, feel free to reply to this email.</p>
        <p>We look forward to speaking with you soon!</p>
        <p>Best regards,<br>{signature}<br>Customer Service Team<br>{company_name}</p>
        """
        footer_text = f"This is an automated message from {company_name}."

    # Set direction attributes based on language
    html_dir = "rtl" if is_arabic else "ltr"
    text_align = "right" if is_arabic else "left"

    # Create the full HTML email with appropriate RTL/LTR settings
    html = f"""
    <html dir="{html_dir}" lang="{language.lower()}">
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                direction: {html_dir};
                text-align: {text_align};
            }}
            .container {{
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                direction: {html_dir};
            }}
            h1 {{
                color: #2c3e50;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
                text-align: {text_align};
            }}
            .content {{
                background-color: #f9f9f9;
                padding: 15px;
                border-radius: 5px;
                text-align: {text_align};
            }}
            .footer {{
                margin-top: 20px;
                font-size: 12px;
                color: #777;
                text-align: center;
            }}
            p {{
                text-align: {text_align};
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="content" dir="{html_dir}">
                {message}
            </div>
            <div class="footer" dir="{html_dir}">
                <p>{footer_text}</p>
            </div>
        </div>
    </body>
    </html>
    """

    return html


@run_in_background
def send_lead_followup_email(
    lead_info: LeadInfo,
    language: str = "English",
    max_retries: int = 3,
    retry_delay: int = 5,
) -> bool:
    """
    Send a follow-up email to a new lead.
    This function runs in a background thread and includes retry logic for transient failures.

    Args:
        lead_info: The lead information
        language: The language to use (default: English)
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        True if the email was sent successfully, False otherwise
    """
    # Check if we have the required configuration
    if not SENDGRID_API_KEY or not EMAIL_FROM_ADDRESS:
        logger.error(
            "SendGrid configuration is incomplete. Check environment variables."
        )
        return False

    # Check if the lead has an email address
    if not lead_info.email:
        logger.warning(
            f"Cannot send follow-up email to lead {lead_info.name}: No email address provided"
        )
        return False

    # Create the email content
    html_content = format_lead_followup_email_html(lead_info, language)

    # Initialize SendGrid client
    sg = sendgrid.SendGridAPIClient(api_key=SENDGRID_API_KEY)

    # Create the email message
    message = Mail(
        from_email=Email(EMAIL_FROM_ADDRESS),
        to_emails=To(lead_info.email),
        subject=(
            f"Thank you for your interest in {HOST_DOMAIN}"
            if language.lower() != "arabic"
            else f"شكرًا لاهتمامك بخدمات {HOST_DOMAIN_ARABIC}"
        ),
        html_content=HtmlContent(html_content),
    )

    # Implement retry logic
    retries = 0
    while retries <= max_retries:
        try:
            # Send the email
            response = sg.send(message)

            # Check response status
            if response.status_code >= 200 and response.status_code < 300:
                logger.info(
                    f"Follow-up email sent successfully to {lead_info.email} for lead: {lead_info.name}"
                )
                return True
            else:
                # Log the error
                logger.warning(
                    f"Failed to send follow-up email to {lead_info.email}. Status code: {response.status_code}"
                )

                # If we've reached max retries, give up
                if retries >= max_retries:
                    logger.error(
                        f"Failed to send follow-up email to {lead_info.email} after {max_retries} attempts. Final status code: {response.status_code}"
                    )
                    return False

                # Otherwise, retry
                retries += 1
                logger.info(
                    f"Retrying follow-up email send (attempt {retries}/{max_retries}) in {retry_delay} seconds..."
                )
                time.sleep(retry_delay)

        except requests.exceptions.ConnectionError as e:
            # Handle connection errors (network issues)
            if retries >= max_retries:
                logger.error(
                    f"Failed to send follow-up email to {lead_info.email} after {max_retries} attempts due to connection error: {str(e)}"
                )
                return False

            retries += 1
            logger.warning(
                f"Connection error when sending follow-up email to {lead_info.email}: {str(e)}"
            )
            logger.info(
                f"Retrying email send (attempt {retries}/{max_retries}) in {retry_delay} seconds..."
            )
            time.sleep(retry_delay)

        except Exception as e:
            # Check if the error message indicates a network/DNS issue
            error_str = str(e).lower()
            if any(
                term in error_str
                for term in [
                    "unable to find",
                    "server",
                    "network",
                    "dns",
                    "connection",
                    "timeout",
                    "connect",
                    "unreachable",
                    "refused",
                ]
            ):
                # This is likely a network-related error, so retry
                if retries >= max_retries:
                    logger.error(
                        f"Failed to send follow-up email to {lead_info.email} after {max_retries} attempts due to network error: {str(e)}"
                    )
                    return False

                retries += 1
                logger.warning(
                    f"Network-related error when sending follow-up email to {lead_info.email}: {str(e)}"
                )
                logger.info(
                    f"Retrying email send (attempt {retries}/{max_retries}) in {retry_delay} seconds..."
                )
                time.sleep(retry_delay)
            else:
                # For non-network related exceptions, log and return False
                logger.error(
                    f"Error sending follow-up email to {lead_info.email}: {str(e)}"
                )
                return False

    # This should not be reached, but just in case
    return False
