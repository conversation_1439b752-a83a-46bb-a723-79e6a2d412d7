"""CV Assessment Agent Module - Refactored

Lightweight coordinator for CV assessment operations.
This is the new, clean version that delegates to specialized components.
"""

from __future__ import annotations

import logging
import os
from typing import Optional, Dict, Any
from uuid import UUID

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_openai import Chat<PERSON>penA<PERSON>

from services.memory_service import SupabaseMemory
from services.tag_extraction_service import TagExtractionService
from services.scoring_engine_service import ScoringEngineService
from .tools import create_template_selection_tool
from .core import AssessmentOrchestrator
from utils.assessment_config import AssessmentConfig

logger = logging.getLogger("cv_assessment_agent_refactored")


class CVAssessmentAgent:
    """Lightweight coordinator for CV assessment operations."""

    def __init__(
        self,
        model_name: str = "gpt-4.1",
        host_domain: str | None = None,
        session_id: str | None = None,
        supabase_client=None,
        db_service=None,
    ) -> None:
        # Language model
        self.model = ChatOpenAI(model=model_name, temperature=0.4, max_tokens=4000)

        # Context
        self.host_domain = host_domain or os.environ.get("HOST_DOMAIN")
        self.session_id = session_id
        self.supabase_client = supabase_client
        self.db_service = db_service

        # Memory for CV assessment context
        self.memory = (
            SupabaseMemory(
                conversation_id=session_id,
                supabase_client=supabase_client,
                max_message_limit=15,
            )
            if session_id and supabase_client
            else None
        )

        # Initialize services
        self.tag_extraction_service = (
            TagExtractionService(db_service) if db_service else None
        )

        self.scoring_engine_service = (
            ScoringEngineService(db_service) if db_service else None
        )

        # Initialize the main orchestrator
        self.orchestrator = AssessmentOrchestrator(
            model_name=model_name,
            tag_extraction_service=self.tag_extraction_service,
            scoring_engine_service=self.scoring_engine_service,
        )

        # Create tools
        self.tools = self._create_tools()

        # Agent executor
        self.agent = self._create_agent()

    def _create_tools(self):
        """Create tools for the CV assessment agent."""
        tools = []

        # Add template selection tool
        if self.supabase_client:
            template_tool = create_template_selection_tool(self)
            tools.append(template_tool)

        return tools

    def _create_agent(self):
        """Create the CV assessment agent with assessment-specific logic."""

        system_prompt = """
        You are a specialized CV Assessment Agent responsible for conducting comprehensive candidate evaluations.

        Your core responsibilities:
        1. Analyze uploaded CVs and extract candidate information
        2. Select appropriate scoring templates based on CV content and job requirements
        3. Generate targeted, dynamic questions based on CV content
        4. Adapt question language based on candidate responses (English/Arabic)
        5. Validate candidate answers for quality and relevance
        6. Provide comprehensive scoring using template-based weights
        7. Handle multi-turn conversations during the assessment process

        CRITICAL LANGUAGE HANDLING:
        - ALWAYS start assessments in English
        - If candidate responds in Arabic or indicates they don't understand English, switch to Arabic
        - Continue in the language of the candidate's most recent response
        - NEVER mix languages in a single response

        ASSESSMENT WORKFLOW:
        1. Template Selection: Choose the best scoring template for the assessment
        2. CV Analysis: Extract key information and generate initial questions
        3. Dynamic Questioning: Ask targeted questions to fill information gaps
        4. Answer Validation: Ensure responses are relevant and complete
        5. Comprehensive Scoring: Provide detailed assessment with 0-100 score using template weights
        6. Profile Generation: Prepare comprehensive candidate profiles for semantic search

        SCORING ENGINE INTEGRATION:
        - Use the scoring engine service for comprehensive AI + Human score calculation
        - Provide detailed score breakdowns with component explanations
        - Support template-based scoring with customizable weights
        - Calculate tag-based scores from extracted candidate information

        You have access to tools for template selection and other assessment capabilities.
        Focus on providing thorough, fair, and accurate candidate evaluations.
        """

        if self.memory:
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    MessagesPlaceholder(variable_name="chat_history"),
                    ("human", "{input}"),
                    MessagesPlaceholder(variable_name="agent_scratchpad"),
                ]
            )
        else:
            prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    ("human", "{input}"),
                    MessagesPlaceholder(variable_name="agent_scratchpad"),
                ]
            )

        # Create agent with tools
        agent = create_tool_calling_agent(self.model, self.tools, prompt)

        if self.memory:
            return AgentExecutor(
                agent=agent, tools=self.tools, memory=self.memory, verbose=False
            )
        return AgentExecutor(agent=agent, tools=self.tools, verbose=False)

    def process_message(self, message: str) -> str:
        """Process a message during CV assessment."""
        try:
            # For CV assessment agent, messages are typically assessment-related
            response = self.agent.invoke({"input": message})
            return response["output"]
        except Exception as exc:
            logger.error("CVAssessmentAgent error: %s", exc, exc_info=True)
            return (
                "I apologize, but I encountered an error during the assessment. "
                "Please try again or contact support for assistance."
            )

    def run_cv_assessment(
        self,
        cv_text: str,
        qa_history: list | None = None,
        job_description: Optional[str] = None,
        industry: Optional[str] = None,
        template_id: Optional[str] = None,
        cv_assessment_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
    ) -> dict:
        """
        Conduct comprehensive CV assessment using the orchestrator.

        This method is now a simple delegation to the AssessmentOrchestrator,
        which handles all the complex logic that was previously in this class.

        Args:
            cv_text: The extracted text from the user's CV
            qa_history: List of Q&A dicts, e.g. [{"question": ..., "answer": ...}, ...]
            job_description: Optional job description for template selection
            industry: Optional industry for template selection
            template_id: Optional specific template ID to use
            cv_assessment_id: Optional CV assessment ID for tag-based processing
            job_id: Optional job ID for configuration

        Returns:
            A dictionary that either asks a follow-up question or returns final assessment
        """
        try:
            # Get template selection tool if available
            template_tool = None
            for tool in self.tools:
                if tool.name == "select_scoring_template":
                    template_tool = tool
                    break

            # Delegate to the orchestrator
            return self.orchestrator.run_assessment(
                cv_text=cv_text,
                qa_history=qa_history,
                job_description=job_description,
                industry=industry,
                template_id=template_id,
                cv_assessment_id=cv_assessment_id,
                job_id=job_id,
                template_selection_tool=template_tool,
            )

        except Exception as exc:
            logger.error("CV assessment error: %s", exc, exc_info=True)
            return {
                "error": True,
                "message": "Assessment could not be completed due to a technical issue.",
                "user_message": (
                    "I apologize, but I encountered an error during the assessment. "
                    "Please try again or contact support for assistance."
                ),
                "details": str(exc),
            }

    async def get_assessment_length(
        self,
        cv_assessment_id: Optional[UUID] = None,
        template_id: Optional[UUID] = None,
        job_id: Optional[UUID] = None,
    ) -> int:
        """Get the configured assessment length for this assessment."""
        if self.tag_extraction_service and cv_assessment_id:
            return await self.tag_extraction_service.get_assessment_length(
                cv_assessment_id, template_id, job_id
            )
        return AssessmentConfig.get_default_assessment_length()
